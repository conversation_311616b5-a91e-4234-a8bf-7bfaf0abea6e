package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.graph.ValueGraph;
import com.facishare.paas.appframework.common.graph.ValueGraphBuilder;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.common.util.UdobjGrayUtil;

import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.uievent.UIEventProcess;
import com.facishare.paas.appframework.core.predef.facade.CalculateServiceFacade;
import com.facishare.paas.appframework.core.predef.service.calculate.CalculateWithUIActionCallbackContainer;
import com.facishare.paas.appframework.core.predef.service.dto.calculate.*;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import com.facishare.paas.appframework.metadata.dto.CheckFieldsForCalc;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.expression.CalculateDataContext;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.expression.ExpressionDTO;
import com.facishare.paas.appframework.metadata.expression.SimpleExpression;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.appframework.metadata.relation.*;
import com.facishare.paas.appframework.metadata.task.AsyncTaskService;
import com.facishare.paas.appframework.metadata.repository.model.MtAsyncTaskMonitor;
import com.facishare.paas.expression.exception.ExpressionCompileException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.describe.RecordTypeOption;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.release.GrayRule;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.reflect.Whitebox;

import java.util.*;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class CalculateServiceTest {

    private static final String TENANT_ID = "1";
    private static final String USER_ID = "1";

    @InjectMocks
    private CalculateService calculateService;

    @Mock
    private MetaDataService metaDataService;
    @Mock
    private DescribeLogicService describeLogicService;
    @Mock
    private ExpressionCalculateLogicService expressionCalculateLogicService;
    @Mock
    private FieldRelationCalculateService fieldRelationCalculateService;
    @Mock
    private FieldRelationGraphService fieldRelationGraphService;
    @Mock
    private JobScheduleService jobScheduleService;
    @Mock
    private RecordTypeLogicService recordTypeLogicService;
    @Mock
    private ServiceFacade serviceFacade;
    @Mock
    private InfraServiceFacade infraServiceFacade;
    @Mock
    private QuoteValueService quoteValueService;
    @Mock
    private RedissonService redissonService;
    @Mock
    private MaskFieldLogicService maskFieldLogicService;
    @Mock
    private CalculateServiceFacade calculateServiceFacade;
    @Mock
    private AsyncTaskService asyncTaskService;

    private ServiceContext serviceContext;
    private MockedStatic<RequestContextManager> mockedRequestContextManager;
    private MockedStatic<I18nClient> mockedI18nClient;
    private MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig;

    @BeforeEach
    void setUp() {
        User user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();

        mockedRequestContextManager = Mockito.mockStatic(RequestContextManager.class);
        mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(requestContext);

        serviceContext = new ServiceContext(requestContext, "calculate", "test");

        mockedI18nClient = Mockito.mockStatic(I18nClient.class);
        I18nClient i18nClientMock = mock(I18nClient.class);
        when(I18nClient.getInstance()).thenReturn(i18nClientMock);

        mockedUdobjGrayConfig = Mockito.mockStatic(UdobjGrayConfig.class);
    }

    @AfterEach
    void tearDown() {
        mockedRequestContextManager.close();
        mockedI18nClient.close();
        mockedUdobjGrayConfig.close();
    }

    @Test
    @DisplayName("expressionCheck - Success")
    void testExpressionCheckSuccess() {
        ExpressionCheck.Arg arg = new ExpressionCheck.Arg();
        arg.setJson_data("{}");
        doNothing().when(expressionCalculateLogicService).compileCheck(any(), any(ExpressionDTO.class));

        ExpressionCheck.Result result = calculateService.expressionCheck(arg, serviceContext);

        assertNotNull(result);
        assertEquals(0, result.getCode());
        verify(expressionCalculateLogicService).compileCheck(any(String.class), any(ExpressionDTO.class));
    }

    @Test
    @DisplayName("expressionCheck - Exception with Error Reminder")
    void testExpressionCheckExceptionWithErrorReminder() {
        ExpressionCheck.Arg arg = new ExpressionCheck.Arg();
        arg.setJson_data("{}");
        arg.setErrorReminder(true);
        doThrow(new ExpressionCompileException("-error")).when(expressionCalculateLogicService).compileCheck(any(String.class), any(ExpressionDTO.class));
        mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(String.class), any(String.class))).thenReturn(true);

        ExpressionCheck.Result result = calculateService.expressionCheck(arg, serviceContext);

        assertEquals(400, result.getCode());
        assertTrue(result.getValue().contains("-error"));
    }

    static Stream<Arguments> expressionCheckExceptionProvider() {
        return Stream.of(
                Arguments.of(new ObjectDefNotFoundError("object not found"), ValidateException.class),
                Arguments.of(new ExpressionCompileException("expression is invalid"), MetaDataBusinessException.class),
                Arguments.of(new StackOverflowError(), ValidateException.class)
        );
    }

    @ParameterizedTest
    @MethodSource("expressionCheckExceptionProvider")
    @DisplayName("expressionCheck - Various Exceptions")
    void testExpressionCheckExceptions(Throwable compileException, Class<? extends Throwable> expectedException) {
        ExpressionCheck.Arg arg = new ExpressionCheck.Arg();
        arg.setJson_data("{}");
        doThrow(compileException).when(expressionCalculateLogicService).compileCheck(any(String.class), any(ExpressionDTO.class));

        assertThrows(expectedException, () -> calculateService.expressionCheck(arg, serviceContext));
    }

    @Test
    @DisplayName("expressionDebug - Success")
    void testExpressionDebugSuccess() {
        ExpressionDebug.Arg arg = new ExpressionDebug.Arg();
        when(expressionCalculateLogicService.expressionDebug(any(), any(), any())).thenReturn("debugResult1");

        ExpressionDebug.Result result = calculateService.expressionDebug(arg, serviceContext);

        assertTrue(result.isSuccess());
        assertEquals("debugResult1", result.getResult());
        assertNull(result.getErrorMessage());
    }

    @Test
    @DisplayName("expressionDebug - Exception")
    void testExpressionDebugException() {
        ExpressionDebug.Arg arg = new ExpressionDebug.Arg();
        when(expressionCalculateLogicService.expressionDebug(any(), any(), any())).thenThrow(new RuntimeException("error"));

        ExpressionDebug.Result result = calculateService.expressionDebug(arg, serviceContext);

        assertFalse(result.isSuccess());
        assertNull(result.getResult());
        assertEquals("error", result.getErrorMessage());
    }

    @Test
    @DisplayName("expressionCalculate - Success")
    void testExpressionCalculate() {
        ExpressionCalculate.Arg arg = new ExpressionCalculate.Arg();
        arg.setObjectDescribeApiName("testApiName");
        arg.setObject_data("{}");
        when(describeLogicService.findObject(any(String.class), any(String.class))).thenReturn(new ObjectDescribe());
        when(metaDataService.batchCalculate(any(), any(), any(), any(), anyBoolean(), anyBoolean())).thenReturn(Collections.singletonList("field1"));

        ExpressionCalculate.Result result = calculateService.expressionCalculate(arg, serviceContext);

        assertNotNull(result);
    }

    static Stream<Arguments> emptyApiNameProvider() {
        return Stream.of(
                Arguments.of((String) null),
                Arguments.of("")
        );
    }

    @ParameterizedTest
    @MethodSource("emptyApiNameProvider")
    @DisplayName("expressionCalculate - Exception for empty API name")
    void testExpressionCalculateException(String objectApiName) {
        ExpressionCalculate.Arg arg = new ExpressionCalculate.Arg();
        arg.setObjectDescribeApiName(objectApiName);
        arg.setObject_data("{}");

        assertThrows(ValidateException.class, () -> calculateService.expressionCalculate(arg, serviceContext));
    }

    @Test
    @DisplayName("batchExpressionCalculate - Success")
    void testBatchExpressionCalculate() {
        BatchExpressionCalculate.Arg arg = new BatchExpressionCalculate.Arg();
        arg.setObjectDescribeApiName("testApiName");
        arg.setObject_data("{}");
        when(describeLogicService.findObject(any(String.class), any(String.class))).thenReturn(new ObjectDescribe());
        when(metaDataService.batchCalculate(any(), any(), any(), any(), anyBoolean(), anyBoolean())).thenReturn(Collections.singletonList("field1"));

        BatchExpressionCalculate.Result result = calculateService.batchExpressionCalculate(arg, serviceContext);

        assertNotNull(result);
    }

    @ParameterizedTest
    @MethodSource("emptyApiNameProvider")
    @DisplayName("batchExpressionCalculate - Exception for empty API name")
    void testBatchExpressionCalculateException(String objectApiName) {
        BatchExpressionCalculate.Arg arg = new BatchExpressionCalculate.Arg();
        arg.setObjectDescribeApiName(objectApiName);
        arg.setObject_data("{}");

        assertThrows(ValidateException.class, () -> calculateService.batchExpressionCalculate(arg, serviceContext));
    }

    @Test
    @DisplayName("batchDataExpressionCalculate - Empty Data")
    void testBatchDataExpressionCalculateEmpty() {
        String json = "[{\"objectDescribeApiName\":\"testApiName\",\"objectDataList\":{}}]";
        BatchDataExpressionCalculate.Arg arg = new BatchDataExpressionCalculate.Arg();
        arg.setCalculate_data_list(json);

        BatchDataExpressionCalculate.Result result = calculateService.batchDataExpressionCalculate(arg, serviceContext);

        assertNotNull(result);
        assertNull(result.getValue_list());
    }

    @Test
    @DisplayName("batchDataExpressionCalculate - With Data")
    void testBatchDataExpressionCalculateWithData() {
        String json = "[{\"objectDescribeApiName\":\"testApiName\",\"objectDataList\":{\"id1\":{}}}]";
        BatchDataExpressionCalculate.Arg arg = new BatchDataExpressionCalculate.Arg();
        arg.setCalculate_data_list(json);
        when(describeLogicService.findObject(any(String.class), any(String.class))).thenReturn(new ObjectDescribe());
        when(metaDataService.batchCalculate(any(), any(), any(), any(), anyBoolean(), anyBoolean())).thenReturn(Collections.singletonList("field1"));

        BatchDataExpressionCalculate.Result result = calculateService.batchDataExpressionCalculate(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getValue_list());
        assertTrue(result.getValue_list().containsKey("testApiName"));
    }

    @Test
    @DisplayName("calculateBatch - Empty Fields")
    void testCalculateBatchWithEmptyFields() {
        CalculateBatchData.Arg arg = new CalculateBatchData.Arg();
        arg.setCalculateFieldApiNames(null);
        arg.setCalculateFields(null);

        CalculateBatchData.Result result = calculateService.calculateBatch(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getCalculateResult());
        assertTrue(result.getCalculateResult().isEmpty());
    }

    @Test
    @DisplayName("calculateBatch - With Calculate Fields")
    void testCalculateBatchWithCalculateFields() {
        CalculateBatchData.Arg arg = new CalculateBatchData.Arg();
        arg.setObjectApiName("testApiName");
        Map<String, ObjectDataDocument> dataMap = new HashMap<>();
        dataMap.put("id1", ObjectDataDocument.of(new HashMap<>()));
        arg.setDataMap(dataMap);
        arg.setModifiedDataIndexList(Arrays.asList("id1"));
        arg.setCalculateFields(new ArrayList<>(Arrays.asList(CalculateRelation.RelateField.of("field1", 1, "F"))));

        IObjectDescribe objectDescribe = mock(IObjectDescribe.class);
        lenient().when(objectDescribe.getApiName()).thenReturn("testApiName");

        Map<String, Object> fieldMap = new HashMap<>();
        fieldMap.put("api_name", "field1");
        fieldMap.put("type", IFieldType.FORMULA);
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        lenient().when(objectDescribe.getFieldDescribes()).thenReturn(Arrays.asList(fieldDescribe));
        lenient().when(objectDescribe.getFieldDescribe("field1")).thenReturn(fieldDescribe);

        Map<String, IObjectDescribe> describeMap = new HashMap<>();
        describeMap.put("testApiName", objectDescribe);
        ValueGraph<FieldNode, RelateEdge> graph = ValueGraphBuilder.directed().build();
        
        when(describeLogicService.findObject(any(), any())).thenReturn(objectDescribe);
        when(fieldRelationGraphService.buildReverseDependencyGraph(anyList(), eq(false), eq(true), eq(true)))
                .thenReturn(FieldRelationGraph.of(graph, describeMap));
        doAnswer(invocation -> {
            Map<String, ObjectDataDocument> argDataMap = arg.getDataMap();
            argDataMap.values().forEach(data -> data.put("field1", "value1"));
            return null;
        }).when(metaDataService).batchCalculateBySortFields(any(), anyList(), any(), anyMap());
        
        CalculateBatchData.Result result = calculateService.calculateBatch(arg, serviceContext);
        
        assertNotNull(result);
        assertEquals(1, result.getCalculateResult().size());
        assertEquals("value1", result.getCalculateResult().get("id1").get("field1"));
    }

    @Test
    @DisplayName("batchCalculate - Null Arg")
    void testBatchCalculateWithNullArg() {
        assertThrows(ValidateException.class, () -> calculateService.batchCalculate(null, serviceContext));
    }

    @Test
    @DisplayName("batchCalculate - Empty Params")
    void testBatchCalculateWithEmptyParams() {
        BatchCalculate.Arg arg = new BatchCalculate.Arg();
        arg.setCalculateFieldApiNames(Collections.emptyMap());
        arg.setCalculateFields(Collections.emptyMap());

        BatchCalculate.Result result = calculateService.batchCalculate(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getCalculateResult());
        assertTrue(result.getCalculateResult().isEmpty());
    }

    @Test
    @DisplayName("calculateWithExpression - Empty ObjectApiName")
    void testCalculateWithExpressionEmptyObjectApiName() {
        CalculateWithExpression.Arg arg = new CalculateWithExpression.Arg();
        arg.setObjectApiName(null);

        assertThrows(ValidateException.class, () -> calculateService.calculateWithExpression(arg, serviceContext));
    }

    @Test
    @DisplayName("calculateWithExpression - Empty Expression List")
    void testCalculateWithExpressionEmptyExpressionList() {
        CalculateWithExpression.Arg arg = new CalculateWithExpression.Arg();
        arg.setObjectApiName("testApiName");
        arg.setExpressionList(null);

        CalculateWithExpression.Result result = calculateService.calculateWithExpression(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getCalculateValues());
        assertTrue(result.getCalculateValues().isEmpty());
    }

    @Test
    @DisplayName("calculateWithExpression - Success")
    void testCalculateWithExpressionSuccess() {
        CalculateWithExpression.Arg arg = new CalculateWithExpression.Arg();
        arg.setObjectApiName("testApiName");
        arg.setExpressionList(Arrays.asList(new SimpleExpression()));

        when(describeLogicService.findObject(anyString(), anyString())).thenReturn(new ObjectDescribe());
        when(expressionCalculateLogicService.calculateWithExpression(any(), any(), any(), anyList()))
                .thenReturn(Collections.singletonMap("exp1", "value1"));

        CalculateWithExpression.Result result = calculateService.calculateWithExpression(arg, serviceContext);

        assertNotNull(result);
        assertEquals("value1", result.getCalculateValues().get("exp1"));
    }

    @Test
    @DisplayName("bulkDataCalculateWithExpression - Empty ObjectApiName")
    void testBulkDataCalculateWithExpressionEmptyObjectApiName() {
        BulkDataCalculateWithExpression.Arg arg = new BulkDataCalculateWithExpression.Arg();
        arg.setObjectApiName("");

        assertThrows(ValidateException.class, () -> calculateService.bulkDataCalculateWithExpression(arg, serviceContext));
    }

    @Test
    @DisplayName("bulkDataCalculateWithExpression - Empty Expression List")
    void testBulkDataCalculateWithExpressionEmptyExpressionList() {
        BulkDataCalculateWithExpression.Arg arg = new BulkDataCalculateWithExpression.Arg();
        arg.setObjectApiName("testApiName");
        arg.setExpressionList(Collections.emptyList());

        BulkDataCalculateWithExpression.Result result = calculateService.bulkDataCalculateWithExpression(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getCalcResult());
        assertTrue(result.getCalcResult().isEmpty());
    }

    @Test
    @DisplayName("bulkDataCalculateWithExpression - Success")
    void testBulkDataCalculateWithExpressionSuccess() {
        BulkDataCalculateWithExpression.Arg arg = new BulkDataCalculateWithExpression.Arg();
        arg.setObjectApiName("testApiName");
        arg.setDataCtx(Arrays.asList(new CalculateDataContext()));
        arg.setExpressionList(Arrays.asList(new SimpleExpression()));

        when(expressionCalculateLogicService.bulkDataCalculateWithExpression(any(), anyList(), anyList()))
                .thenReturn(Collections.singletonMap("data1", Collections.singletonMap("exp1", "value1")));

        BulkDataCalculateWithExpression.Result result = calculateService.bulkDataCalculateWithExpression(arg, serviceContext);

        assertNotNull(result);
        assertEquals("value1", result.getCalcResult().get("data1").get("exp1"));
    }

    @Test
    @DisplayName("batchCalculateCountFields - Success")
    void testBatchCalculateCountFields() {
        BatchCalculateCountFields.Arg arg = new BatchCalculateCountFields.Arg();
        arg.setObjectApiName("testApiName");
        arg.setObjectDataId("data1");
        arg.setCountFieldList(Arrays.asList(new BatchCalculateCountFields.CountField()));

        when(metaDataService.calculateCountField(any(), anyString(), anyString(), anyList()))
                .thenReturn(Collections.singletonMap("field1", "value1"));

        BatchCalculateCountFields.Result result = calculateService.batchCalculateCountFields(arg, serviceContext);

        assertNotNull(result);
        assertEquals("value1", result.getCountFieldValues().get("field1"));
    }

    static Stream<Arguments> invalidCalculateAndUpdateFormulaFieldsProvider() {
        return Stream.of(
                Arguments.of(null, null, null),
                Arguments.of("", null, null),
                Arguments.of("testApiName", null, null),
                Arguments.of("testApiName", Collections.emptyList(), null),
                Arguments.of("testApiName", Arrays.asList("data1"), null),
                Arguments.of("testApiName", Arrays.asList("data1"), Collections.emptyList())
        );
    }

    @ParameterizedTest
    @MethodSource("invalidCalculateAndUpdateFormulaFieldsProvider")
    @DisplayName("calculateAndUpdateFormulaFields - Invalid Params")
    void testCalculateAndUpdateFormulaFieldsInvalidParams(String objectApiName, List<String> dataIds, List<String> fieldApiNames) {
        CalculateAndUpdateFormulaFields.Arg arg = new CalculateAndUpdateFormulaFields.Arg();
        arg.setObjectApiName(objectApiName);
        arg.setDataIds(dataIds);
        arg.setFieldApiNames(fieldApiNames);

        assertThrows(ValidateException.class, () -> calculateService.calculateAndUpdateFormulaFields(arg, serviceContext));
    }

    @Test
    @DisplayName("calculateAndUpdateFormulaFields - Success")
    void testCalculateAndUpdateFormulaFieldsSuccess() {
        CalculateAndUpdateFormulaFields.Arg arg = new CalculateAndUpdateFormulaFields.Arg();
        arg.setObjectApiName("testApiName");
        arg.setDataIds(Arrays.asList("data1"));
        arg.setFieldApiNames(Arrays.asList("field1"));

        when(metaDataService.calculateAndUpdateFormulaFields(any(), anyString(), anyList(), anyList(), anyBoolean()))
                .thenReturn(Collections.singletonMap("data1", Collections.singletonMap("field1", "value1")));

        CalculateAndUpdateFormulaFields.Result result = calculateService.calculateAndUpdateFormulaFields(arg, serviceContext);

        assertNotNull(result);
        assertEquals("value1", result.getResult().get("data1").get("field1"));
    }

    static Stream<Arguments> invalidCalculateCountOrFormulaFieldProvider() {
        return Stream.of(
                Arguments.of(null, null, null),
                Arguments.of("", null, null),
                Arguments.of("testApiName", null, null),
                Arguments.of("testApiName", "", null),
                Arguments.of("testApiName", "data1", null),
                Arguments.of("testApiName", "data1", "")
        );
    }

    @ParameterizedTest
    @MethodSource("invalidCalculateCountOrFormulaFieldProvider")
    @DisplayName("calculateCountOrFormulaField - Invalid Params")
    void testCalculateCountOrFormulaFieldInvalidParams(String objectApiName, String dataId, String fieldApiName) {
        CalculateCountOrFormulaField.Arg arg = new CalculateCountOrFormulaField.Arg();
        arg.setObjectApiName(objectApiName);
        arg.setDataId(dataId);
        arg.setFieldApiName(fieldApiName);

        assertThrows(ValidateException.class, () -> calculateService.calculateCountOrFormulaField(arg, serviceContext));
    }

    @Test
    @DisplayName("calculateCountOrFormulaField - Success")
    void testCalculateCountOrFormulaFieldSuccess() {
        CalculateCountOrFormulaField.Arg arg = new CalculateCountOrFormulaField.Arg();
        arg.setObjectApiName("testApiName");
        arg.setDataId("data1");
        arg.setFieldApiName("field1");
        
        IObjectDescribe objectDescribe = mock(IObjectDescribe.class);
        
        Map<String, Object> fieldMap = new HashMap<>();
        fieldMap.put("api_name", "field1");
        fieldMap.put("type", IFieldType.QUOTE);
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        when(objectDescribe.getFieldDescribe("field1")).thenReturn(fieldDescribe);
        
        ObjectData objectData = new ObjectData(new HashMap<>());
        when(describeLogicService.findObject(TENANT_ID, "testApiName")).thenReturn(objectDescribe);
        when(metaDataService.findObjectData(anyString(), anyString(), any(IObjectDescribe.class))).thenReturn(objectData);
        
        doAnswer(invocation -> {
            List<IObjectData> dataList = invocation.getArgument(1);
            IObjectData data = dataList.get(0);
            data.set("field1", "value1");
            data.set("field1__v", "value1__v");
            data.set("field1__r", "value1__r");
            return null;
        }).when(quoteValueService).fillQuoteFieldValue(eq(serviceContext.getUser()), anyList(), any(IObjectDescribe.class), isNull(), eq(true), anyList(), isNull(), eq(false));
        
        CalculateCountOrFormulaField.Result result = calculateService.calculateCountOrFormulaField(arg, serviceContext);
        
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertEquals("value1", result.getResult().get("field1"));
        assertEquals("value1__v", result.getResult().get("field1__v"));
        assertEquals("value1__r", result.getResult().get("field1__r"));
    }

    static Stream<Arguments> invalidSubmitCalculateJobProvider() {
        return Stream.of(
                Arguments.of((Object) null),
                Arguments.of(Collections.emptyList()),
                Arguments.of(Arrays.asList(new SubmitCalculateJob.CalculateJob())),
                Arguments.of(Arrays.asList(createCalculateJobWithApiName("testApiName"))),
                Arguments.of(Arrays.asList(createCalculateJobWithEmptyFields("testApiName")))
        );
    }
    
    private static SubmitCalculateJob.CalculateJob createCalculateJobWithApiName(String apiName) {
        SubmitCalculateJob.CalculateJob job = new SubmitCalculateJob.CalculateJob();
        job.setObjectApiName(apiName);
        return job;
    }
    
    private static SubmitCalculateJob.CalculateJob createCalculateJobWithEmptyFields(String apiName) {
        SubmitCalculateJob.CalculateJob job = new SubmitCalculateJob.CalculateJob();
        job.setObjectApiName(apiName);
        job.setFieldApiNameList(Collections.emptyList());
        return job;
    }

    @ParameterizedTest
    @MethodSource("invalidSubmitCalculateJobProvider")
    @DisplayName("submitCalculateJob - Invalid Params")
    void testSubmitCalculateJobInvalidParams(List<SubmitCalculateJob.CalculateJob> calculateJobList) {
        SubmitCalculateJob.Arg arg = new SubmitCalculateJob.Arg();
        arg.setManual(false);
        arg.setCalculateJobList(calculateJobList);

        assertThrows(ValidateException.class, () -> calculateService.submitCalculateJob(arg, serviceContext));
    }

    @Test
    @DisplayName("submitCalculateJob - Manual Success")
    void testSubmitCalculateJobManualSuccess() {
        SubmitCalculateJob.Arg arg = new SubmitCalculateJob.Arg();
        arg.setManual(true);

        when(describeLogicService.findDescribeListWithFields(any())).thenReturn(Collections.emptyList());

        SubmitCalculateJob.Result result = calculateService.submitCalculateJob(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getCalculateFieldMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试手动模式下根据lastModifiedTime过滤对象的场景
     */
    @Test
    @DisplayName("submitCalculateJob - Manual with LastModifiedTime Filter")
    void testSubmitCalculateJobManualWithLastModifiedTimeFilter() {
        SubmitCalculateJob.Arg arg = new SubmitCalculateJob.Arg();
        arg.setManual(true);
        arg.setLastModifiedTime(8L);

        // 创建测试对象描述
        IObjectDescribe describe1 = mock(IObjectDescribe.class);
        when(describe1.getApiName()).thenReturn("testApiName1");
        when(describe1.getLastModifiedTime()).thenReturn(10L); // 大于过滤时间
        when(describe1.isActive()).thenReturn(true);

        Map<String, Object> fieldMap1 = new HashMap<>();
        fieldMap1.put("api_name", "field1");
        fieldMap1.put("type", IFieldType.FORMULA);
        fieldMap1.put("is_index", true);
        IFieldDescribe fieldDescribe1 = FieldDescribeFactory.newInstance(fieldMap1);
        when(describe1.getFieldDescribes()).thenReturn(Arrays.asList(fieldDescribe1));

        IObjectDescribe describe2 = mock(IObjectDescribe.class);
        lenient().when(describe2.getApiName()).thenReturn("testApiName2");
        lenient().when(describe2.getLastModifiedTime()).thenReturn(5L); // 小于过滤时间
        lenient().when(describe2.isActive()).thenReturn(true);

        when(describeLogicService.findDescribeListWithFields(any()))
                .thenReturn(Arrays.asList(describe1, describe2));
        lenient().when(describeLogicService.findObject(any(), any())).thenAnswer(invocation -> {
            String apiName = invocation.getArgument(1);
            if ("testApiName1".equals(apiName)) {
                return describe1;
            } else if ("testApiName2".equals(apiName)) {
                return describe2;
            }
            return null;
        });

        SubmitCalculateJob.Result result = calculateService.submitCalculateJob(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getCalculateFieldMap());
        // 只有 describe1 应该被包含，因为它的 lastModifiedTime > 8
        assertTrue(result.getCalculateFieldMap().containsKey("testApiName1"));
        assertFalse(result.getCalculateFieldMap().containsKey("testApiName2"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试手动模式下指定calculateJobList的场景
     */
    @Test
    @DisplayName("submitCalculateJob - Manual with CalculateJobList")
    void testSubmitCalculateJobManualWithCalculateJobList() {
        SubmitCalculateJob.Arg arg = new SubmitCalculateJob.Arg();
        arg.setManual(true);

        SubmitCalculateJob.CalculateJob job = new SubmitCalculateJob.CalculateJob();
        job.setObjectApiName("testApiName");
        arg.setCalculateJobList(Arrays.asList(job));

        IObjectDescribe describe = mock(IObjectDescribe.class);
        when(describe.getApiName()).thenReturn("testApiName");
        when(describe.isActive()).thenReturn(true);

        Map<String, Object> fieldMap = new HashMap<>();
        fieldMap.put("api_name", "field1");
        fieldMap.put("type", IFieldType.FORMULA);
        fieldMap.put("is_index", true);
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        when(describe.getFieldDescribes()).thenReturn(Arrays.asList(fieldDescribe));

        Map<String, IObjectDescribe> describeMap = new HashMap<>();
        describeMap.put("testApiName", describe);
        when(describeLogicService.findObjects(any(), any())).thenReturn(describeMap);

        SubmitCalculateJob.Result result = calculateService.submitCalculateJob(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getCalculateFieldMap());
        assertTrue(result.getCalculateFieldMap().containsKey("testApiName"));
        assertEquals(Arrays.asList("field1"), result.getCalculateFieldMap().get("testApiName"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试非手动模式下指定字段列表的场景
     */
    @Test
    @DisplayName("submitCalculateJob - Non-Manual with FieldList")
    void testSubmitCalculateJobNonManualWithFieldList() {
        SubmitCalculateJob.Arg arg = new SubmitCalculateJob.Arg();
        arg.setManual(false);

        SubmitCalculateJob.CalculateJob job = new SubmitCalculateJob.CalculateJob();
        job.setObjectApiName("testApiName");
        job.setFieldApiNameList(new ArrayList<>(Arrays.asList("field1")));
        arg.setCalculateJobList(Arrays.asList(job));

        IObjectDescribe describe = mock(IObjectDescribe.class);
        when(describe.getApiName()).thenReturn("testApiName");
        when(describe.isActive()).thenReturn(true);

        // 创建一个计算字段，使得 field1 能通过过滤
        Map<String, Object> fieldMap = new HashMap<>();
        fieldMap.put("api_name", "field1");
        fieldMap.put("type", IFieldType.FORMULA);
        fieldMap.put("is_index", true);
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        when(describe.getFieldDescribes()).thenReturn(Arrays.asList(fieldDescribe));

        Map<String, IObjectDescribe> describeMap = new HashMap<>();
        describeMap.put("testApiName", describe);
        when(describeLogicService.findObjects(any(), any())).thenReturn(describeMap);
        lenient().when(describeLogicService.findObject(any(), any())).thenReturn(describe);

        SubmitCalculateJob.Result result = calculateService.submitCalculateJob(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getCalculateFieldMap());
        assertTrue(result.getCalculateFieldMap().containsKey("testApiName"));
        assertEquals(Arrays.asList("field1"), result.getCalculateFieldMap().get("testApiName"));

        verify(jobScheduleService).submitCalculateJob(any(), eq(Arrays.asList("field1")), eq("testApiName"), eq(false));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象非活跃状态时被过滤的场景
     */
    @Test
    @DisplayName("submitCalculateJob - Filter Inactive Objects")
    void testSubmitCalculateJobFilterInactiveObjects() {
        SubmitCalculateJob.Arg arg = new SubmitCalculateJob.Arg();
        arg.setManual(true);

        IObjectDescribe activeDescribe = mock(IObjectDescribe.class);
        when(activeDescribe.getApiName()).thenReturn("activeObject");
        when(activeDescribe.isActive()).thenReturn(true);

        Map<String, Object> fieldMap = new HashMap<>();
        fieldMap.put("api_name", "field1");
        fieldMap.put("type", IFieldType.FORMULA);
        fieldMap.put("is_index", true);
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        when(activeDescribe.getFieldDescribes()).thenReturn(Arrays.asList(fieldDescribe));

        IObjectDescribe inactiveDescribe = mock(IObjectDescribe.class);
        lenient().when(inactiveDescribe.getApiName()).thenReturn("inactiveObject");
        when(inactiveDescribe.isActive()).thenReturn(false);

        when(describeLogicService.findDescribeListWithFields(any()))
                .thenReturn(Arrays.asList(activeDescribe, inactiveDescribe));
        lenient().when(describeLogicService.findObject(any(), any())).thenAnswer(invocation -> {
            String apiName = invocation.getArgument(1);
            if ("activeObject".equals(apiName)) {
                return activeDescribe;
            } else if ("inactiveObject".equals(apiName)) {
                return inactiveDescribe;
            }
            return null;
        });

        SubmitCalculateJob.Result result = calculateService.submitCalculateJob(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getCalculateFieldMap());
        assertTrue(result.getCalculateFieldMap().containsKey("activeObject"));
        assertFalse(result.getCalculateFieldMap().containsKey("inactiveObject"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试字段类型过滤逻辑，只包含formula、count、quote且is_index=true的字段
     */
    @Test
    @DisplayName("submitCalculateJob - Filter Field Types")
    void testSubmitCalculateJobFilterFieldTypes() {
        SubmitCalculateJob.Arg arg = new SubmitCalculateJob.Arg();
        arg.setManual(true);

        IObjectDescribe describe = mock(IObjectDescribe.class);
        when(describe.getApiName()).thenReturn("testApiName");
        when(describe.isActive()).thenReturn(true);

        // 创建不同类型的字段
        Map<String, Object> formulaFieldMap = new HashMap<>();
        formulaFieldMap.put("api_name", "formulaField");
        formulaFieldMap.put("type", IFieldType.FORMULA);
        formulaFieldMap.put("is_index", true);
        IFieldDescribe formulaField = FieldDescribeFactory.newInstance(formulaFieldMap);

        Map<String, Object> countFieldMap = new HashMap<>();
        countFieldMap.put("api_name", "countField");
        countFieldMap.put("type", IFieldType.COUNT);
        countFieldMap.put("is_index", true);
        IFieldDescribe countField = FieldDescribeFactory.newInstance(countFieldMap);

        Map<String, Object> quoteFieldMap = new HashMap<>();
        quoteFieldMap.put("api_name", "quoteField");
        quoteFieldMap.put("type", IFieldType.QUOTE);
        quoteFieldMap.put("is_index", true);
        IFieldDescribe quoteField = FieldDescribeFactory.newInstance(quoteFieldMap);

        Map<String, Object> textFieldMap = new HashMap<>();
        textFieldMap.put("api_name", "textField");
        textFieldMap.put("type", IFieldType.TEXT);
        textFieldMap.put("is_index", true);
        IFieldDescribe textField = FieldDescribeFactory.newInstance(textFieldMap);

        Map<String, Object> formulaNoIndexFieldMap = new HashMap<>();
        formulaNoIndexFieldMap.put("api_name", "formulaNoIndexField");
        formulaNoIndexFieldMap.put("type", IFieldType.FORMULA);
        formulaNoIndexFieldMap.put("is_index", false);
        IFieldDescribe formulaNoIndexField = FieldDescribeFactory.newInstance(formulaNoIndexFieldMap);

        when(describe.getFieldDescribes()).thenReturn(Arrays.asList(
                formulaField, countField, quoteField, textField, formulaNoIndexField));

        when(describeLogicService.findDescribeListWithFields(any()))
                .thenReturn(Arrays.asList(describe));
        lenient().when(describeLogicService.findObject(any(), any())).thenReturn(describe);

        SubmitCalculateJob.Result result = calculateService.submitCalculateJob(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getCalculateFieldMap());
        assertTrue(result.getCalculateFieldMap().containsKey("testApiName"));

        List<String> fieldNames = result.getCalculateFieldMap().get("testApiName");
        assertEquals(3, fieldNames.size());
        assertTrue(fieldNames.contains("formulaField"));
        assertTrue(fieldNames.contains("countField"));
        assertTrue(fieldNames.contains("quoteField"));
        assertFalse(fieldNames.contains("textField"));
        assertFalse(fieldNames.contains("formulaNoIndexField"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当没有计算字段时不调用jobScheduleService的场景
     */
    @Test
    @DisplayName("submitCalculateJob - No Calculate Fields")
    void testSubmitCalculateJobNoCalculateFields() {
        SubmitCalculateJob.Arg arg = new SubmitCalculateJob.Arg();
        arg.setManual(false);

        SubmitCalculateJob.CalculateJob job = new SubmitCalculateJob.CalculateJob();
        job.setObjectApiName("testApiName");
        job.setFieldApiNameList(new ArrayList<>(Arrays.asList("field1")));
        arg.setCalculateJobList(Arrays.asList(job));

        IObjectDescribe describe = mock(IObjectDescribe.class);
        when(describe.getApiName()).thenReturn("testApiName");
        when(describe.isActive()).thenReturn(true);
        when(describe.getFieldDescribes()).thenReturn(Collections.emptyList());

        Map<String, IObjectDescribe> describeMap = new HashMap<>();
        describeMap.put("testApiName", describe);
        when(describeLogicService.findObjects(any(), any())).thenReturn(describeMap);
        lenient().when(describeLogicService.findObject(any(), any())).thenReturn(describe);

        SubmitCalculateJob.Result result = calculateService.submitCalculateJob(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getCalculateFieldMap());
        // 由于没有计算字段，calculateFieldMap 应该为空或者包含空列表
        if (result.getCalculateFieldMap().containsKey("testApiName")) {
            assertTrue(result.getCalculateFieldMap().get("testApiName").isEmpty());
        }

        // 验证没有调用 jobScheduleService
        verify(jobScheduleService, never()).submitCalculateJob(any(), anyList(), anyString(), anyBoolean());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试MetaDataBusinessException异常处理场景
     */
    @Test
    @DisplayName("submitCalculateJob - MetaDataBusinessException Handling")
    void testSubmitCalculateJobMetaDataBusinessExceptionHandling() {
        SubmitCalculateJob.Arg arg = new SubmitCalculateJob.Arg();
        arg.setManual(false);

        SubmitCalculateJob.CalculateJob job = new SubmitCalculateJob.CalculateJob();
        job.setObjectApiName("testApiName");
        job.setFieldApiNameList(new ArrayList<>(Arrays.asList("field1")));
        arg.setCalculateJobList(Arrays.asList(job));

        IObjectDescribe describe = mock(IObjectDescribe.class);
        when(describe.getApiName()).thenReturn("testApiName");
        when(describe.isActive()).thenReturn(true);

        // 创建一个计算字段，使得 field1 能通过过滤
        Map<String, Object> fieldMap = new HashMap<>();
        fieldMap.put("api_name", "field1");
        fieldMap.put("type", IFieldType.FORMULA);
        fieldMap.put("is_index", true);
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        when(describe.getFieldDescribes()).thenReturn(Arrays.asList(fieldDescribe));

        Map<String, IObjectDescribe> describeMap = new HashMap<>();
        describeMap.put("testApiName", describe);
        when(describeLogicService.findObjects(any(), any())).thenReturn(describeMap);
        lenient().when(describeLogicService.findObject(any(), any())).thenReturn(describe);

        doThrow(new MetaDataBusinessException("Test exception"))
                .when(jobScheduleService).submitCalculateJob(any(), anyList(), anyString(), anyBoolean());

        SubmitCalculateJob.Result result = calculateService.submitCalculateJob(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getCalculateFieldMap());
        assertTrue(result.getCalculateFieldMap().containsKey("testApiName"));
        assertEquals(Arrays.asList("field1"), result.getCalculateFieldMap().get("testApiName"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试一般Exception异常处理场景
     */
    @Test
    @DisplayName("submitCalculateJob - General Exception Handling")
    void testSubmitCalculateJobGeneralExceptionHandling() {
        SubmitCalculateJob.Arg arg = new SubmitCalculateJob.Arg();
        arg.setManual(false);

        SubmitCalculateJob.CalculateJob job = new SubmitCalculateJob.CalculateJob();
        job.setObjectApiName("testApiName");
        job.setFieldApiNameList(new ArrayList<>(Arrays.asList("field1")));
        arg.setCalculateJobList(Arrays.asList(job));

        IObjectDescribe describe = mock(IObjectDescribe.class);
        when(describe.getApiName()).thenReturn("testApiName");
        when(describe.isActive()).thenReturn(true);
        // 创建一个计算字段，使得 field1 能通过过滤
        Map<String, Object> fieldMap = new HashMap<>();
        fieldMap.put("api_name", "field1");
        fieldMap.put("type", IFieldType.FORMULA);
        fieldMap.put("is_index", true);
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        when(describe.getFieldDescribes()).thenReturn(Arrays.asList(fieldDescribe));

        Map<String, IObjectDescribe> describeMap = new HashMap<>();
        describeMap.put("testApiName", describe);
        when(describeLogicService.findObjects(any(), any())).thenReturn(describeMap);
        lenient().when(describeLogicService.findObject(any(), any())).thenReturn(describe);

        doThrow(new RuntimeException("Test runtime exception"))
                .when(jobScheduleService).submitCalculateJob(any(), anyList(), anyString(), anyBoolean());

        SubmitCalculateJob.Result result = calculateService.submitCalculateJob(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getCalculateFieldMap());
        assertTrue(result.getCalculateFieldMap().containsKey("testApiName"));
        assertEquals(Arrays.asList("field1"), result.getCalculateFieldMap().get("testApiName"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试优化模式下减少计算字段的批量计算场景，验证灰度配置生效时只计算指定字段
     */
    @Test
    @DisplayName("batchCalculate - Optimize Mode with Less Calculate Fields")
    void testBatchCalculateWithOptimizeModeAndLessCalculateFields() {
        // 准备测试数据
        String masterApiName = "testApiName";
        String detailApiName = "detail1";
        String lookupApiName = "lookup1";

        // 创建主对象描述
        Map<String, Object> masterDescribeMap = new HashMap<>();
        masterDescribeMap.put("api_name", masterApiName);
        masterDescribeMap.put("is_active", true);
        Map<String, Object> masterFields = new HashMap<>();

        Map<String, Object> masterField1 = new HashMap<>();
        masterField1.put("api_name", "field1");
        masterField1.put("type", "formula");
        masterField1.put("is_active", true);
        masterFields.put("field1", masterField1);

        Map<String, Object> masterField2 = new HashMap<>();
        masterField2.put("api_name", "field2");
        masterField2.put("type", "formula");
        masterField2.put("is_active", true);
        masterFields.put("field2", masterField2);

        masterDescribeMap.put("fields", masterFields);
        IObjectDescribe masterDescribe = new ObjectDescribe(masterDescribeMap);

        // 创建明细对象描述
        Map<String, Object> detailDescribeMap = new HashMap<>();
        detailDescribeMap.put("api_name", detailApiName);
        detailDescribeMap.put("is_active", true);
        Map<String, Object> detailFields = new HashMap<>();

        Map<String, Object> detailField1 = new HashMap<>();
        detailField1.put("api_name", "field1");
        detailField1.put("type", "formula");
        detailField1.put("is_active", true);
        detailFields.put("field1", detailField1);

        Map<String, Object> detailField2 = new HashMap<>();
        detailField2.put("api_name", "field2");
        detailField2.put("type", "formula");
        detailField2.put("is_active", true);
        detailFields.put("field2", detailField2);

        Map<String, Object> detailField3 = new HashMap<>();
        detailField3.put("api_name", "field3");
        detailField3.put("type", "object_reference");
        detailField3.put("is_active", true);
        detailField3.put("target_api_name", lookupApiName);
        detailFields.put("field3", detailField3);

        detailDescribeMap.put("fields", detailFields);
        IObjectDescribe detailDescribe = new ObjectDescribe(detailDescribeMap);

        // 创建查找对象描述
        Map<String, Object> lookupDescribeMap = new HashMap<>();
        lookupDescribeMap.put("api_name", lookupApiName);
        lookupDescribeMap.put("is_active", true);
        Map<String, Object> lookupFields = new HashMap<>();

        Map<String, Object> lookupField1 = new HashMap<>();
        lookupField1.put("api_name", "field1");
        lookupField1.put("type", "count");
        lookupField1.put("is_active", true);
        lookupField1.put("sub_object_describe_apiname", detailApiName);
        lookupField1.put("field_api_name", "field3");
        lookupField1.put("count_type", "count");
        lookupFields.put("field1", lookupField1);

        lookupDescribeMap.put("fields", lookupFields);
        IObjectDescribe lookupDescribe = new ObjectDescribe(lookupDescribeMap);

        // 只计算field1字段（参数中指定的计算字段）
        Map<String, List<CalculateRelation.RelateField>> argCalculateFields = new HashMap<>();
        List<CalculateRelation.RelateField> masterArgFields = new ArrayList<>();
        masterArgFields.add(CalculateRelation.RelateField.of("field1", 0, ""));
        argCalculateFields.put(masterApiName, masterArgFields);

        List<CalculateRelation.RelateField> detailArgFields = new ArrayList<>();
        detailArgFields.add(CalculateRelation.RelateField.of("field1", 0, ""));
        argCalculateFields.put(detailApiName, detailArgFields);

        List<CalculateRelation.RelateField> lookupArgFields = new ArrayList<>();
        lookupArgFields.add(CalculateRelation.RelateField.of("field1", 0, ""));
        argCalculateFields.put(lookupApiName, lookupArgFields);

        // 计算服务实际计算的字段（包含field1和field2）
        Map<String, List<CalculateRelation.RelateField>> computeCalculateFields = new HashMap<>();
        List<CalculateRelation.RelateField> masterComputeFields = new ArrayList<>();
        masterComputeFields.add(CalculateRelation.RelateField.of("field1", 0, ""));
        masterComputeFields.add(CalculateRelation.RelateField.of("field2", 0, ""));
        computeCalculateFields.put(masterApiName, masterComputeFields);

        List<CalculateRelation.RelateField> detailComputeFields = new ArrayList<>();
        detailComputeFields.add(CalculateRelation.RelateField.of("field1", 0, ""));
        detailComputeFields.add(CalculateRelation.RelateField.of("field2", 0, ""));
        computeCalculateFields.put(detailApiName, detailComputeFields);

        List<CalculateRelation.RelateField> lookupComputeFields = new ArrayList<>();
        lookupComputeFields.add(CalculateRelation.RelateField.of("field1", 0, ""));
        computeCalculateFields.put(lookupApiName, lookupComputeFields);

        // 创建测试数据
        ObjectDataDocument masterData = ObjectDataDocument.of(new HashMap<>());
        masterData.put("id", "1");
        masterData.put("object_describe_api_name", masterApiName);

        ObjectDataDocument detailData = ObjectDataDocument.of(new HashMap<>());
        detailData.put("id", "d1");
        detailData.put("object_describe_api_name", detailApiName);

        // 创建批量计算参数
        BatchCalculate.Arg arg = new BatchCalculate.Arg();
        arg.setMasterObjectApiName(masterApiName);
        arg.setMasterData(masterData);

        Map<String, Map<String, ObjectDataDocument>> detailDataMap = new HashMap<>();
        Map<String, ObjectDataDocument> detailMap = new HashMap<>();
        detailMap.put("d1", detailData);
        detailDataMap.put(detailApiName, detailMap);
        arg.setDetailDataMap(detailDataMap);

        arg.setCalculateFields(argCalculateFields);
        arg.setModifiedObjectApiName(detailApiName);
        List<String> modifiedDataIndexList = new ArrayList<>();
        modifiedDataIndexList.add("d1");
        arg.setModifiedDataIndexList(modifiedDataIndexList);
        arg.setModifiedType(BatchCalculate.ADD);

        ObjectDataDocument oldMasterData = ObjectDataDocument.of(new HashMap<>());
        oldMasterData.put("id", "1");
        oldMasterData.put("object_describe_api_name", masterApiName);
        arg.setOldMasterData(oldMasterData);

        Map<String, Map<String, ObjectDataDocument>> oldDetailDataMap = new HashMap<>();
        Map<String, ObjectDataDocument> oldDetailMap = new HashMap<>();
        ObjectDataDocument oldDetailData = ObjectDataDocument.of(new HashMap<>());
        oldDetailData.put("id", "d1");
        oldDetailData.put("object_describe_api_name", detailApiName);
        oldDetailMap.put("d1", oldDetailData);
        oldDetailDataMap.put(detailApiName, oldDetailMap);
        arg.setOldDetailDataMap(oldDetailDataMap);

        // 创建字段关系图
        ValueGraph<FieldNode, RelateEdge> graph = ValueGraphBuilder.directed().build();
        Map<String, IObjectDescribe> describeMap = new HashMap<>();
        describeMap.put(masterApiName, masterDescribe);
        describeMap.put(detailApiName, detailDescribe);
        describeMap.put(lookupApiName, lookupDescribe);
        FieldRelationGraph fieldRelationGraph = FieldRelationGraph.of(graph, describeMap);

        // 设置灰度配置以启用优化模式
        UdobjGrayUtil.GrayConfig grayConfig = JacksonUtils.fromJson(
                "{\"functions\":[{\"functionName\":\"optimizeDetailObjectCalculateInBatchCalculate\",\"blackRule\":{\"ei\":\"white:*\"}}]}",
                UdobjGrayUtil.GrayConfig.class);
        Whitebox.setInternalState(UdobjGrayUtil.class, "GRAY_CONFIG", grayConfig);

        // 配置Mock行为
        when(describeLogicService.findObjects(any(), any())).thenReturn(describeMap);
        List<IObjectDescribe> detailDescribeList = new ArrayList<>();
        detailDescribeList.add(detailDescribe);
        when(describeLogicService.findDetailDescribesCreateWithMaster(any(), any())).thenReturn(detailDescribeList);
        when(fieldRelationGraphService.buildReverseDependencyGraph(anyList(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(fieldRelationGraph);

        when(fieldRelationCalculateService.computeCalculateFieldsForEditData(any())).thenAnswer(invocation -> {
            CalculateFields calculateFields = CalculateFields.of(fieldRelationGraph, computeCalculateFields);
            Map<String, List<CalculateObjectData>> calculateDataMap = new HashMap<>();
            List<CalculateObjectData> detailCalculateDataList = new ArrayList<>();
            detailCalculateDataList.add(CalculateObjectData.of("d1", detailData.toObjectData(),
                    new HashSet<>(computeCalculateFields.get(detailApiName)), new HashMap<>()));
            calculateDataMap.put(detailApiName, detailCalculateDataList);
            calculateFields.setCalculateDataMap(calculateDataMap);
            return calculateFields;
        });

        doAnswer(invocation -> {
            IObjectData masterDataArg = invocation.getArgument(1);
            Map<String, List<IObjectData>> detailDataMapArg = invocation.getArgument(2);
            CalculateFields calculateFields = invocation.getArgument(3);

            // 验证优化模式生效：计算字段为空
            assertTrue(calculateFields.getCalculateFieldMap().get(masterApiName).isEmpty());
            assertTrue(calculateFields.getCalculateFieldMap().get(detailApiName).isEmpty());
            assertTrue(calculateFields.getCalculateFieldMap().get(lookupApiName).isEmpty());

            // 模拟计算结果
            masterDataArg.set("field1", "masterValue1");
            if (detailDataMapArg != null && detailDataMapArg.get(detailApiName) != null) {
                for (IObjectData detailDataArg : detailDataMapArg.get(detailApiName)) {
                    detailDataArg.set("field1", "detailValue1");
                }
            }
            return null;
        }).when(metaDataService).batchCalculateBySortFields(any(User.class), any(IObjectData.class), anyMap(), any(CalculateFields.class));

        // 执行测试
        BatchCalculate.Result result = calculateService.batchCalculate(arg, serviceContext);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getCalculateResult());

        // 验证Mock被调用
        verify(metaDataService).batchCalculateBySortFields(any(User.class), any(IObjectData.class), anyMap(), any(CalculateFields.class));

        // 验证优化模式生效：由于没有需要计算的字段，计算结果可能为空或包含基本数据
        // 这个测试的核心是验证灰度配置生效，优化了计算字段
        System.out.println("Test passed: 优化模式生效，计算字段被正确优化");
    }

    static Stream<Arguments> invalidCheckCalculateFieldsProvider() {
        return Stream.of(
                Arguments.of(null, null),
                Arguments.of("", null),
                Arguments.of(null, Collections.emptyList()),
                Arguments.of("", Collections.emptyList()),
                Arguments.of(null, Arrays.asList(Collections.singletonMap("api_name", "field1"))),
                Arguments.of("", Arrays.asList(Collections.singletonMap("api_name", "field1"))),
                Arguments.of("testApiName", null),
                Arguments.of("testApiName", Collections.emptyList())
        );
    }

    @ParameterizedTest
    @MethodSource("invalidCheckCalculateFieldsProvider")
    @DisplayName("checkCalculateFields - Invalid Params")
    void testCheckCalculateFieldsInvalidParams(String objectApiName, List<Map<String, Object>> fieldList) {
        CheckCalculateFields.Arg arg = new CheckCalculateFields.Arg();
        arg.setObjectApiName(objectApiName);
        arg.setFieldList(fieldList);

        assertThrows(ValidateException.class, () -> calculateService.checkCalculateFields(arg, serviceContext));
    }

    @Test
    @DisplayName("checkCalculateFields - Success")
    void testCheckCalculateFieldsSuccess() {
        CheckCalculateFields.Arg arg = new CheckCalculateFields.Arg();
        arg.setObjectApiName("testApiName");
        Map<String, Object> fieldMap = new HashMap<>();
        fieldMap.put("api_name", "field1");
        fieldMap.put("type", "formula");
        fieldMap.put("isIndex", true);
        fieldMap.put("display_name", "Field 1");
        arg.setFieldList(Arrays.asList(fieldMap));
        
        CheckCalculateFields.Result result = calculateService.checkCalculateFields(arg, serviceContext);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
    }

    @Test
    @DisplayName("batchCalculateOfRecordType - Success")
    void testBatchCalculateOfRecordTypeSuccess() {
        BatchCalculateOfRecordType.Arg arg = new BatchCalculateOfRecordType.Arg();
        arg.setMasterObjectApiName("testApiName");
        arg.setRecordType("default__c");

        Map<String, Object> masterDataMap = new HashMap<>();
        masterDataMap.put("field1", "value1");
        arg.setMasterData(ObjectDataDocument.of(masterDataMap));

        Map<String, Object> detailDataMap = new HashMap<>();
        detailDataMap.put("field1", "value1");
        Map<String, List<IObjectData>> detailMap = new HashMap<>();
        detailMap.put("detail1", Arrays.asList(new ObjectData(detailDataMap)));
        arg.setDetailDataMap(ObjectDataDocument.ofMap(detailMap));

        Map<String, Object> recordTypeOptionMap = new HashMap<>();
        recordTypeOptionMap.put("api_name", "default__c");
        Map<String, List<IRecordTypeOption>> recordTypeMap = new HashMap<>();
        recordTypeMap.put("detail1", Arrays.asList(new RecordTypeOption(recordTypeOptionMap)));
        
        when(recordTypeLogicService.findValidRecordTypeListMap(anyList(), any()))
                .thenReturn(recordTypeMap);
        when(recordTypeLogicService.filterUnMatchRecordTypes(any(), any(), any(), any()))
                .thenReturn(recordTypeMap);
        
        when(describeLogicService.findObjects(any(), any())).thenAnswer(invocation -> {
            Collection<String> objectApiNames = invocation.getArgument(1);
            Map<String, IObjectDescribe> describeMap = new HashMap<>();
            objectApiNames.forEach(apiName -> {
                IObjectDescribe describe = mock(IObjectDescribe.class);
                lenient().when(describe.getApiName()).thenReturn(apiName);
                describeMap.put(apiName, describe);
            });
            return describeMap;
        });
        
        BatchCalculateOfRecordType.Result result = calculateService.batchCalculateOfRecordType(arg, serviceContext);
        
        assertNotNull(result);
        assertNotNull(result.getObjectData());
        assertNotNull(result.getDetail());
    }

    @ParameterizedTest
    @DisplayName("calculateWithDrafts - Various scenarios")
    @MethodSource("calculateWithDraftsTestData")
    void testCalculateWithDrafts(Map<String, Object> maskFieldEncryptGray,
                                 Map<String, Object> objectData,
                                 Map<String, List<ObjectDataDocument>> detailDataMap,
                                 Map<String, List<String>> skipCalculateFields,
                                 boolean skipCalculateDVField,
                                 Map<String, List<String>> maskFieldApiNames,
                                 Map<String, Object> expectedResultObjectData,
                                 Map<String, List<ObjectDataDocument>> expectedResultDetail) {

        CalculateWithDrafts.Arg arg = new CalculateWithDrafts.Arg();
        arg.setObjectData(ObjectDataDocument.of(objectData));
        arg.setDetailDataMap(detailDataMap);
        arg.setSkipCalculateFields(skipCalculateFields);
        arg.setSkipCalculateDVField(skipCalculateDVField);
        arg.setMaskFieldApiNames(maskFieldApiNames);

        // Mock AppFrameworkConfig.maskFieldEncryptGray using MockedStatic
        Whitebox.setInternalState(AppFrameworkConfig.class, "maskFieldEncryptGray", maskFieldEncryptGray);

        when(describeLogicService.findObject(any(), any())).thenAnswer(invocation -> {
            String apiName = invocation.getArgument(1);
            IObjectDescribe describe = mock(IObjectDescribe.class);
            when(describe.getApiName()).thenReturn(apiName);
            return describe;
        });

        when(describeLogicService.findObjects(any(), any())).thenAnswer(invocation -> {
            Collection<String> objectApiNames = invocation.getArgument(1);
            Map<String, IObjectDescribe> describeMap = new HashMap<>();
            objectApiNames.forEach(apiName -> {
                IObjectDescribe describe = mock(IObjectDescribe.class);
                when(describe.getApiName()).thenReturn(apiName);
                describeMap.put(apiName, describe);
            });
            return describeMap;
        });

        CalculateWithDrafts.Result result = calculateService.calculateWithDrafts(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getObjectData());
        assertNotNull(result.getDetailDataMap());
        assertEquals(expectedResultObjectData.get("object_describe_api_name"),
                     result.getObjectData().get("object_describe_api_name"));
        assertEquals(expectedResultDetail, result.getDetailDataMap());
    }

    static Stream<Arguments> calculateWithDraftsTestData() {
        Map<String, Object> emptyGrayMap = new HashMap<>();
        Map<String, Object> objectDataWithApiName = new HashMap<>();
        objectDataWithApiName.put("object_describe_api_name", "testApiName");

        Map<String, List<ObjectDataDocument>> emptyDetailMap = new HashMap<>();
        Map<String, List<String>> emptySkipFields = new HashMap<>();
        Map<String, List<String>> emptyMaskFields = new HashMap<>();
        Map<String, List<ObjectDataDocument>> emptyDetailResult = new HashMap<>();

        Map<String, Object> whiteColonGray = new HashMap<>();
        whiteColonGray.put("testApiName", new GrayRule("white:"));

        Map<String, Object> whiteStarGray = new HashMap<>();
        whiteStarGray.put("testApiName", new GrayRule("white:*"));

        return Stream.of(
            Arguments.of(emptyGrayMap, objectDataWithApiName, emptyDetailMap, emptySkipFields, false, emptyMaskFields, objectDataWithApiName, emptyDetailResult),
            Arguments.of(whiteColonGray, objectDataWithApiName, emptyDetailMap, emptySkipFields, false, emptyMaskFields, objectDataWithApiName, emptyDetailResult),
            Arguments.of(whiteStarGray, objectDataWithApiName, emptyDetailMap, emptySkipFields, false, emptyMaskFields, objectDataWithApiName, emptyDetailResult)
        );
    }

    @Test
    @DisplayName("calculateWithEditInfo - Null Arg")
    void testCalculateWithEditInfoNullArg() {
        assertThrows(ValidateException.class, () -> calculateService.calculateWithEditInfo(null, serviceContext));
    }

    @Test
    @DisplayName("calculateWithEditInfo - Success")
    void testCalculateWithEditInfoSuccess() {
        CalculateWithEditInfo.Arg arg = new CalculateWithEditInfo.Arg();
        arg.setDescribeApiName("testApiName");
        
        CalculateWithEditInfo.DataInfo beforeDataInfo = new CalculateWithEditInfo.DataInfo();
        beforeDataInfo.setMasterData(ObjectDataDocument.of(new HashMap<>()));
        arg.setBeforeDataInfo(beforeDataInfo);
        
        CalculateWithEditInfo.DataInfo afterDataInfo = new CalculateWithEditInfo.DataInfo();
        afterDataInfo.setMasterData(ObjectDataDocument.of(new HashMap<>()));
        arg.setAfterDataInfo(afterDataInfo);
        
        when(describeLogicService.findObject(any(), any())).thenAnswer(invocation -> {
            String apiName = invocation.getArgument(1);
            IObjectDescribe describe = mock(IObjectDescribe.class);
            when(describe.getApiName()).thenReturn(apiName);
            return describe;
        });
        
        when(calculateServiceFacade.calculateWithUIActionCallback(any(), any(), any())).thenAnswer(invocation -> {
            CalculateWithUIActionCallbackContainer container = invocation.getArgument(0);
            Map<String, List<ObjectDataDocument>> detailData = invocation.getArgument(2);
            
            ObjectData masterData = new ObjectData();
            masterData.setDescribeApiName(container.getObjectDescribe().getApiName());
            
            return UIEventProcess.ProcessRequest.builder()
                    .masterData(masterData)
                    .detailWithOnlyChangedFields(ObjectDataDocument.ofDataMap(detailData))
                    .build();
        });
        
        CalculateWithEditInfo.Result result = calculateService.calculateWithEditInfo(arg, serviceContext);
        
        assertNotNull(result);
        assertNotNull(result.getData());
    }

    @Test
    @DisplayName("checkFields - Success")
    void testCheckFieldsSuccess() {
        CheckFieldsForCalc.Arg arg = new CheckFieldsForCalc.Arg();
        arg.setFieldDescribes(Arrays.asList(
                "{\"api_name\":\"field1\",\"type\":\"formula\"}",
                "{\"api_name\":\"field2\",\"type\":\"count\"}"
        ));

        CheckFieldsForCalc.Result mockResult = CheckFieldsForCalc.Result.builder()
                .openModal(false)
                .build();
        when(serviceFacade.checkFieldsForCalc(eq(serviceContext.getUser()), isNull(), isNull(), eq(false), anyList()))
                .thenReturn(mockResult);

        assertDoesNotThrow(() -> calculateService.checkFields(arg, serviceContext));
        verify(serviceFacade).checkFieldsForCalc(eq(serviceContext.getUser()), isNull(), isNull(), eq(false), anyList());
    }

    static Stream<Arguments> invalidCalculateAndUpdateByOriginalDataProvider() {
        return Stream.of(
                Arguments.of(null, null),
                Arguments.of("", null),
                Arguments.of(null, "id1"),
                Arguments.of("", "id1"),
                Arguments.of("testApiName", null),
                Arguments.of("testApiName", "")
        );
    }

    @ParameterizedTest
    @MethodSource("invalidCalculateAndUpdateByOriginalDataProvider")
    @DisplayName("calculateAndUpdateByOriginalData - Invalid Params")
    void testCalculateAndUpdateByOriginalDataInvalidParams(String objectApiName, String dataId) {
        CalculateAndUpdateByOriginalData.Arg arg = new CalculateAndUpdateByOriginalData.Arg();
        arg.setObjectApiName(objectApiName);
        arg.setDataId(dataId);

        assertThrows(ValidateException.class, () -> calculateService.calculateAndUpdateByOriginalData(arg, serviceContext));
    }

    @Test
    @DisplayName("calculateAndUpdateByOriginalData - Success")
    void testCalculateAndUpdateByOriginalDataSuccess() {
        CalculateAndUpdateByOriginalData.Arg arg = new CalculateAndUpdateByOriginalData.Arg();
        arg.setObjectApiName("testApiName");
        arg.setDataId("id1");

        doNothing().when(metaDataService).calculateAndUpdateByOriginalData(any(), anyString(), anyString(), any());

        CalculateAndUpdateByOriginalData.Result result = calculateService.calculateAndUpdateByOriginalData(arg, serviceContext);

        assertNotNull(result);
        verify(metaDataService).calculateAndUpdateByOriginalData(any(), anyString(), anyString(), any());
    }

    static Stream<Arguments> invalidFindCalculateFieldsWithUnCompletedJobProvider() {
        return Stream.of(
                Arguments.of((String) null),
                Arguments.of("")
        );
    }

    @ParameterizedTest
    @MethodSource("invalidFindCalculateFieldsWithUnCompletedJobProvider")
    @DisplayName("findCalculateFieldsWithUnCompletedJob - Invalid Params")
    void testFindCalculateFieldsWithUnCompletedJobInvalidParams(String objectApiName) {
        FindCalculateFieldsWithUnCompletedJob.Arg arg = new FindCalculateFieldsWithUnCompletedJob.Arg();
        arg.setObjectApiName(objectApiName);

        assertThrows(ValidateException.class, () -> calculateService.findCalculateFieldsWithUnCompletedJob(arg, serviceContext));
    }

    @Test
    @DisplayName("findCalculateFieldsWithUnCompletedJob - Success")
    void testFindCalculateFieldsWithUnCompletedJobSuccess() {
        FindCalculateFieldsWithUnCompletedJob.Arg arg = new FindCalculateFieldsWithUnCompletedJob.Arg();
        arg.setObjectApiName("testApiName");

        when(describeLogicService.findObjectWithoutCopy(anyString(), anyString())).thenReturn(new ObjectDescribe());
        when(asyncTaskService.findBySearchQuery(any(), any())).thenReturn(Collections.emptyList());

        FindCalculateFieldsWithUnCompletedJob.Result result = calculateService.findCalculateFieldsWithUnCompletedJob(arg, serviceContext);

        assertNotNull(result);
        assertTrue(result.isSupportCalculationProgress());
    }

    static Stream<Arguments> invalidFindCalculationJobInfoProvider() {
        return Stream.of(
                Arguments.of(null, null, null),
                Arguments.of("", null, null),
                Arguments.of("testApiName", null, null),
                Arguments.of("testApiName", "", null)
        );
    }

    @ParameterizedTest
    @MethodSource("invalidFindCalculationJobInfoProvider")
    @DisplayName("findCalculationJobInfo - Invalid Params")
    void testFindCalculationJobInfoInvalidParams(String objectApiName, String fieldApiName, String jobId) {
        FindCalculationJobInfo.Arg arg = new FindCalculationJobInfo.Arg();
        arg.setObjectApiName(objectApiName);
        arg.setFieldApiName(fieldApiName);
        arg.setJobId(jobId);

        assertThrows(ValidateException.class, () -> calculateService.findCalculationJobInfo(arg, serviceContext));
    }

    @Test
    @DisplayName("findCalculationJobInfo - By JobId Success")
    void testFindCalculationJobInfoByJobIdSuccess() {
        FindCalculationJobInfo.Arg arg = new FindCalculationJobInfo.Arg();
        arg.setJobId("job1");

        // Mock MtAsyncTaskMonitor for asyncTaskService.find
        MtAsyncTaskMonitor mockTaskMonitor = mock(MtAsyncTaskMonitor.class);
        when(mockTaskMonitor.getId()).thenReturn("job1");
        when(mockTaskMonitor.getTaskStatus()).thenReturn("150");
        when(mockTaskMonitor.getTaskTotalNum()).thenReturn(100L);
        when(mockTaskMonitor.getCompletedNum()).thenReturn(50L);
        when(mockTaskMonitor.getObjectDescribeApiName()).thenReturn("testApiName");
        when(mockTaskMonitor.getBizApiName()).thenReturn("field1");
        when(mockTaskMonitor.jobStatusText()).thenReturn("RUNNING");
        when(mockTaskMonitor.estimatedStartTime()).thenReturn(System.currentTimeMillis());
        when(mockTaskMonitor.startTime()).thenReturn(System.currentTimeMillis());
        when(mockTaskMonitor.estimatedCompleteTime()).thenReturn(System.currentTimeMillis() + 10000);
        when(mockTaskMonitor.getEndTime()).thenReturn(null);

        when(asyncTaskService.find(any(), eq("job1"))).thenReturn(mockTaskMonitor);

        FindCalculationJobInfo.Result result = calculateService.findCalculationJobInfo(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getJobInfo());
        assertEquals("job1", result.getJobInfo().getJobId());
        assertEquals("150", result.getJobInfo().getJobStatusCode());
    }

    @Test
    @DisplayName("findCalculationJobInfo - By Field Success")
    void testFindCalculationJobInfoByFieldSuccess() {
        FindCalculationJobInfo.Arg arg = new FindCalculationJobInfo.Arg();
        arg.setObjectApiName("testApiName");
        arg.setFieldApiName("field1");

        IObjectDescribe objectDescribe = mock(IObjectDescribe.class);

        Map<String, Object> fieldMap = new HashMap<>();
        fieldMap.put("api_name", "field1");
        fieldMap.put("type", IFieldType.FORMULA);
        fieldMap.put("is_index", true);
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        when(objectDescribe.getFieldDescribe("field1")).thenReturn(fieldDescribe);

        when(describeLogicService.findObjectWithoutCopy(any(), any())).thenReturn(objectDescribe);

        // Mock MtAsyncTaskMonitor for asyncTaskService.findBySearchQuery
        MtAsyncTaskMonitor mockTaskMonitor = mock(MtAsyncTaskMonitor.class);
        when(mockTaskMonitor.getId()).thenReturn("job1");
        when(mockTaskMonitor.getTaskStatus()).thenReturn("200");
        when(mockTaskMonitor.getTaskTotalNum()).thenReturn(100L);
        when(mockTaskMonitor.getCompletedNum()).thenReturn(50L);
        when(mockTaskMonitor.getObjectDescribeApiName()).thenReturn("testApiName");
        when(mockTaskMonitor.getBizApiName()).thenReturn("field1");
        when(mockTaskMonitor.jobStatusText()).thenReturn("RUNNING");
        when(mockTaskMonitor.estimatedStartTime()).thenReturn(System.currentTimeMillis());
        when(mockTaskMonitor.startTime()).thenReturn(System.currentTimeMillis());
        when(mockTaskMonitor.estimatedCompleteTime()).thenReturn(System.currentTimeMillis() + 10000);
        when(mockTaskMonitor.getEndTime()).thenReturn(null);

        when(asyncTaskService.findBySearchQuery(any(), any())).thenReturn(Arrays.asList(mockTaskMonitor));

        FindCalculationJobInfo.Result result = calculateService.findCalculationJobInfo(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getJobInfo());
        assertEquals("job1", result.getJobInfo().getJobId());
        assertEquals("200", result.getJobInfo().getJobStatusCode());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当字段未找到时，findCalculationJobInfo方法应该返回null的jobInfo
     */
    @Test
    @DisplayName("findCalculationJobInfo - Field Not Found")
    void testFindCalculationJobInfoFieldNotFound() {
        FindCalculationJobInfo.Arg arg = new FindCalculationJobInfo.Arg();
        arg.setObjectApiName("testApiName");
        arg.setFieldApiName("field1");

        IObjectDescribe objectDescribe = mock(IObjectDescribe.class);

        when(objectDescribe.getFieldDescribe("field1")).thenReturn(null);

        when(describeLogicService.findObjectWithoutCopy(any(), any())).thenReturn(objectDescribe);

        FindCalculationJobInfo.Result result = calculateService.findCalculationJobInfo(arg, serviceContext);

        assertNotNull(result);
        assertNull(result.getJobInfo());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当任务未找到时，findCalculationJobInfo方法应该返回null的jobInfo
     */
    @Test
    @DisplayName("findCalculationJobInfo - Job Not Found")
    void testFindCalculationJobInfoJobNotFound() {
        FindCalculationJobInfo.Arg arg = new FindCalculationJobInfo.Arg();
        arg.setObjectApiName("testApiName");
        arg.setFieldApiName("field1");

        IObjectDescribe objectDescribe = mock(IObjectDescribe.class);

        Map<String, Object> fieldMap = new HashMap<>();
        fieldMap.put("api_name", "field1");
        fieldMap.put("type", IFieldType.FORMULA);
        fieldMap.put("is_index", true);
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        when(objectDescribe.getFieldDescribe("field1")).thenReturn(fieldDescribe);

        when(describeLogicService.findObjectWithoutCopy(any(), any())).thenReturn(objectDescribe);

        when(asyncTaskService.findBySearchQuery(any(), any())).thenReturn(Collections.emptyList());

        FindCalculationJobInfo.Result result = calculateService.findCalculationJobInfo(arg, serviceContext);

        assertNotNull(result);
        assertNull(result.getJobInfo());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试batchCalculate方法在处理删除明细数据时的行为
     */
    @Test
    @DisplayName("batchCalculate - With Detail Delete Data")
    void testBatchCalculateWithDetailDeleteData() {
        String masterApiName = "testApiName";
        String detailApiName = "detail1";

        IObjectDescribe masterDescribe = mock(IObjectDescribe.class);
        lenient().when(masterDescribe.getApiName()).thenReturn(masterApiName);
        lenient().when(masterDescribe.isActive()).thenReturn(true);

        Map<String, Object> masterFieldMap = new HashMap<>();
        masterFieldMap.put("api_name", "field1");
        masterFieldMap.put("type", IFieldType.FORMULA);
        masterFieldMap.put("is_active", true);
        masterFieldMap.put("is_calculate_field", true);
        IFieldDescribe masterFieldDescribe = FieldDescribeFactory.newInstance(masterFieldMap);
        lenient().when(masterDescribe.getFieldDescribes()).thenReturn(Arrays.asList(masterFieldDescribe));
        lenient().when(masterDescribe.getFieldDescribe("field1")).thenReturn(masterFieldDescribe);

        IObjectDescribe detailDescribe = mock(IObjectDescribe.class);
        lenient().when(detailDescribe.getApiName()).thenReturn(detailApiName);
        lenient().when(detailDescribe.isActive()).thenReturn(true);

        Map<String, Object> detailFieldMap = new HashMap<>();
        detailFieldMap.put("api_name", "field2");
        detailFieldMap.put("type", IFieldType.FORMULA);
        detailFieldMap.put("is_active", true);
        detailFieldMap.put("is_calculate_field", true);
        IFieldDescribe detailFieldDescribe = FieldDescribeFactory.newInstance(detailFieldMap);
        when(detailDescribe.getFieldDescribes()).thenReturn(Arrays.asList(detailFieldDescribe));

        BatchCalculate.Arg arg = new BatchCalculate.Arg();
        arg.setMasterObjectApiName(masterApiName);

        Map<String, Object> masterDataMap = new HashMap<>();
        masterDataMap.put("id", "1");
        arg.setMasterData(ObjectDataDocument.of(masterDataMap));

        Map<String, Map<String, ObjectDataDocument>> detailDataMap = new HashMap<>();
        Map<String, ObjectDataDocument> detailData = new HashMap<>();
        Map<String, Object> detailDataObjMap = new HashMap<>();
        detailDataObjMap.put("id", "d1");
        detailData.put("0", ObjectDataDocument.of(detailDataObjMap));
        detailDataMap.put(detailApiName, detailData);
        arg.setDetailDataMap(detailDataMap);

        Map<String, List<CalculateRelation.RelateField>> calculateFields = new HashMap<>();
        List<CalculateRelation.RelateField> masterFields = new ArrayList<>();
        masterFields.add(CalculateRelation.RelateField.of("field1", 1, "F"));
        calculateFields.put(masterApiName, masterFields);
        List<CalculateRelation.RelateField> detailFields = new ArrayList<>();
        detailFields.add(CalculateRelation.RelateField.of("field2", 1, "F"));
        calculateFields.put(detailApiName, detailFields);
        arg.setCalculateFields(calculateFields);

        Map<String, IObjectDescribe> describeMap = new HashMap<>();
        describeMap.put(masterApiName, masterDescribe);
        describeMap.put(detailApiName, detailDescribe);
        ValueGraph<FieldNode, RelateEdge> graph = ValueGraphBuilder.directed().build();

        lenient().when(describeLogicService.findObjects(any(), any())).thenReturn(describeMap);
        lenient().when(describeLogicService.findDetailDescribesCreateWithMaster(any(), any())).thenReturn(Arrays.asList(detailDescribe));
        lenient().when(fieldRelationGraphService.buildReverseDependencyGraph(anyList(), eq(false), eq(true), eq(true)))
                .thenReturn(FieldRelationGraph.of(graph, describeMap));
        lenient().doNothing().when(metaDataService).mergeWithDbData(any(), any(), any());

        BatchCalculate.Result result = calculateService.batchCalculate(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getCalculateResult());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试batchCalculate方法在处理复制明细数据和排除字段时的行为
     */
    @Test
    @DisplayName("batchCalculate - With Copy Detail And Excluded Fields")
    void testBatchCalculateWithCopyDetailAndExcludedFields() {
        String masterApiName = "testApiName";
        String detailApiName = "detail1";

        IObjectDescribe masterDescribe = mock(IObjectDescribe.class);
        lenient().when(masterDescribe.getApiName()).thenReturn(masterApiName);
        lenient().when(masterDescribe.isActive()).thenReturn(true);

        Map<String, Object> masterFieldMap = new HashMap<>();
        masterFieldMap.put("api_name", "field1");
        masterFieldMap.put("type", IFieldType.FORMULA);
        masterFieldMap.put("is_active", true);
        masterFieldMap.put("is_calculate_field", true);
        IFieldDescribe masterFieldDescribe = FieldDescribeFactory.newInstance(masterFieldMap);
        lenient().when(masterDescribe.getFieldDescribes()).thenReturn(Arrays.asList(masterFieldDescribe));

        IObjectDescribe detailDescribe = mock(IObjectDescribe.class);
        lenient().when(detailDescribe.getApiName()).thenReturn(detailApiName);
        lenient().when(detailDescribe.isActive()).thenReturn(true);

        Map<String, Object> detailFieldMap = new HashMap<>();
        detailFieldMap.put("api_name", "field2");
        detailFieldMap.put("type", IFieldType.FORMULA);
        detailFieldMap.put("is_active", true);
        detailFieldMap.put("is_calculate_field", true);
        IFieldDescribe detailFieldDescribe = FieldDescribeFactory.newInstance(detailFieldMap);
        lenient().when(detailDescribe.getFieldDescribes()).thenReturn(Arrays.asList(detailFieldDescribe));

        BatchCalculate.Arg arg = new BatchCalculate.Arg();
        arg.setMasterObjectApiName(masterApiName);

        Map<String, Object> masterDataMap = new HashMap<>();
        masterDataMap.put("id", "1");
        arg.setMasterData(ObjectDataDocument.of(masterDataMap));

        Map<String, Map<String, ObjectDataDocument>> detailDataMap = new HashMap<>();
        Map<String, ObjectDataDocument> detailData = new HashMap<>();
        Map<String, Object> detailDataObjMap = new HashMap<>();
        detailDataObjMap.put("id", "d1");
        detailData.put("0", ObjectDataDocument.of(detailDataObjMap));
        detailDataMap.put(detailApiName, detailData);
        arg.setDetailDataMap(detailDataMap);

        Map<String, List<CalculateRelation.RelateField>> calculateFields = new HashMap<>();
        List<CalculateRelation.RelateField> masterFields = new ArrayList<>();
        masterFields.add(CalculateRelation.RelateField.of("field1", 1, "F"));
        calculateFields.put(masterApiName, masterFields);
        List<CalculateRelation.RelateField> detailFields = new ArrayList<>();
        detailFields.add(CalculateRelation.RelateField.of("field2", 1, "F"));
        calculateFields.put(detailApiName, detailFields);
        arg.setCalculateFields(calculateFields);

        Map<String, Map<String, List<CalculateRelation.RelateField>>> excludedFields = new HashMap<>();
        Map<String, List<CalculateRelation.RelateField>> detailExcluded = new HashMap<>();
        List<CalculateRelation.RelateField> excludedFieldList = new ArrayList<>();
        excludedFieldList.add(CalculateRelation.RelateField.of("field2", 1, "F"));
        detailExcluded.put("0", excludedFieldList);
        excludedFields.put(detailApiName, detailExcluded);
        arg.setExcludedDetailCalculateFields(excludedFields);

        Map<String, IObjectDescribe> describeMap = new HashMap<>();
        describeMap.put(masterApiName, masterDescribe);
        describeMap.put(detailApiName, detailDescribe);
        ValueGraph<FieldNode, RelateEdge> graph = ValueGraphBuilder.directed().build();

        lenient().when(describeLogicService.findObjects(any(), any())).thenReturn(describeMap);
        lenient().when(describeLogicService.findDetailDescribesCreateWithMaster(any(), any())).thenReturn(Arrays.asList(detailDescribe));
        lenient().when(fieldRelationGraphService.buildReverseDependencyGraph(anyList(), eq(false), eq(true), eq(true)))
                .thenReturn(FieldRelationGraph.of(graph, describeMap));
        lenient().doNothing().when(metaDataService).mergeWithDbData(any(), any(), any());

        BatchCalculate.Result result = calculateService.batchCalculate(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getCalculateResult());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试batchCalculate方法在处理mask字段时的行为
     */
    @Test
    @DisplayName("batchCalculate - With Mask Fields")
    void testBatchCalculateWithMaskFields() {
        String masterApiName = "testApiName";

        IObjectDescribe masterDescribe = mock(IObjectDescribe.class);
        lenient().when(masterDescribe.getApiName()).thenReturn(masterApiName);
        lenient().when(masterDescribe.isActive()).thenReturn(true);

        Map<String, Object> field1Map = new HashMap<>();
        field1Map.put("api_name", "field1");
        field1Map.put("type", IFieldType.FORMULA);
        field1Map.put("is_active", true);
        field1Map.put("is_calculate_field", true);
        IFieldDescribe field1Describe = FieldDescribeFactory.newInstance(field1Map);

        Map<String, Object> maskFieldMap = new HashMap<>();
        maskFieldMap.put("api_name", "mask_field");
        maskFieldMap.put("type", IFieldType.TEXT);
        maskFieldMap.put("is_mask", true);
        IFieldDescribe maskFieldDescribe = FieldDescribeFactory.newInstance(maskFieldMap);

        when(masterDescribe.getFieldDescribes()).thenReturn(Arrays.asList(field1Describe, maskFieldDescribe));

        BatchCalculate.Arg arg = new BatchCalculate.Arg();
        arg.setMasterObjectApiName(masterApiName);

        Map<String, Object> masterDataMap = new HashMap<>();
        masterDataMap.put("id", "1");
        masterDataMap.put("mask_field", "123456");
        arg.setMasterData(ObjectDataDocument.of(masterDataMap));

        Map<String, List<String>> maskFieldApiNames = new HashMap<>();
        List<String> maskFields = new ArrayList<>();
        maskFields.add("mask_field");
        maskFieldApiNames.put(masterApiName, maskFields);
        arg.setMaskFieldApiNames(maskFieldApiNames);

        Map<String, List<CalculateRelation.RelateField>> calculateFields = new HashMap<>();
        List<CalculateRelation.RelateField> masterFields = new ArrayList<>();
        masterFields.add(CalculateRelation.RelateField.of("field1", 1, "F"));
        calculateFields.put(masterApiName, masterFields);
        arg.setCalculateFields(calculateFields);

        Map<String, IObjectDescribe> describeMap = new HashMap<>();
        describeMap.put(masterApiName, masterDescribe);
        ValueGraph<FieldNode, RelateEdge> graph = ValueGraphBuilder.directed().build();

        lenient().when(describeLogicService.findObjects(any(), any())).thenReturn(describeMap);
        lenient().when(describeLogicService.findDetailDescribesCreateWithMaster(any(), any())).thenReturn(Collections.emptyList());
        lenient().when(fieldRelationGraphService.buildReverseDependencyGraph(anyList(), eq(false), eq(true), eq(true)))
                .thenReturn(FieldRelationGraph.of(graph, describeMap));
        lenient().doNothing().when(metaDataService).mergeWithDbData(any(), any(), any());

        BatchCalculate.Result result = calculateService.batchCalculate(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getCalculateResult());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试batchCalculate方法在优化模式下的行为
     */
    @Test
    @DisplayName("batchCalculate - With Optimize Mode")
    void testBatchCalculateWithOptimizeMode() {
        String masterApiName = "testApiName";

        IObjectDescribe masterDescribe = mock(IObjectDescribe.class);
        lenient().when(masterDescribe.getApiName()).thenReturn(masterApiName);
        lenient().when(masterDescribe.isActive()).thenReturn(true);

        Map<String, Object> fieldMap = new HashMap<>();
        fieldMap.put("api_name", "field1");
        fieldMap.put("type", IFieldType.FORMULA);
        fieldMap.put("is_active", true);
        fieldMap.put("is_calculate_field", true);
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        lenient().when(masterDescribe.getFieldDescribes()).thenReturn(Arrays.asList(fieldDescribe));

        BatchCalculate.Arg arg = new BatchCalculate.Arg();
        arg.setMasterObjectApiName(masterApiName);

        Map<String, Object> masterDataMap = new HashMap<>();
        masterDataMap.put("id", "1");
        arg.setMasterData(ObjectDataDocument.of(masterDataMap));

        Map<String, List<CalculateRelation.RelateField>> calculateFields = new HashMap<>();
        List<CalculateRelation.RelateField> masterFields = new ArrayList<>();
        masterFields.add(CalculateRelation.RelateField.of("field1", 1, "F"));
        calculateFields.put(masterApiName, masterFields);
        arg.setCalculateFields(calculateFields);

        Map<String, IObjectDescribe> describeMap = new HashMap<>();
        describeMap.put(masterApiName, masterDescribe);
        ValueGraph<FieldNode, RelateEdge> graph = ValueGraphBuilder.directed().build();

        lenient().when(describeLogicService.findObjects(any(), any())).thenReturn(describeMap);
        lenient().when(describeLogicService.findDetailDescribesCreateWithMaster(any(), any())).thenReturn(Collections.emptyList());
        lenient().when(fieldRelationGraphService.buildReverseDependencyGraph(anyList(), eq(false), eq(true), eq(true)))
                .thenReturn(FieldRelationGraph.of(graph, describeMap));
        lenient().doNothing().when(metaDataService).mergeWithDbData(any(), any(), any());

        BatchCalculate.Result result = calculateService.batchCalculate(arg, serviceContext);

        assertNotNull(result);
        assertNotNull(result.getCalculateResult());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试batchCalculate方法在处理无效描述时抛出ValidateException异常
     */
    @Test
    @DisplayName("batchCalculate - With Invalid Describe")
    void testBatchCalculateWithInvalidDescribe() {
        String masterApiName = "testApiName";

        IObjectDescribe masterDescribe = mock(IObjectDescribe.class);
        lenient().when(masterDescribe.getApiName()).thenReturn(masterApiName);
        lenient().when(masterDescribe.isActive()).thenReturn(false);

        BatchCalculate.Arg arg = new BatchCalculate.Arg();
        arg.setMasterObjectApiName(masterApiName);

        Map<String, Object> masterDataMap = new HashMap<>();
        masterDataMap.put("id", "1");
        arg.setMasterData(ObjectDataDocument.of(masterDataMap));

        Map<String, List<CalculateRelation.RelateField>> calculateFields = new HashMap<>();
        List<CalculateRelation.RelateField> masterFields = new ArrayList<>();
        masterFields.add(CalculateRelation.RelateField.of("field1", 1, "F"));
        calculateFields.put(masterApiName, masterFields);
        arg.setCalculateFields(calculateFields);

        Map<String, IObjectDescribe> describeMap = new HashMap<>();
        describeMap.put(masterApiName, masterDescribe);

        when(describeLogicService.findObjects(any(), any())).thenReturn(describeMap);
        when(describeLogicService.findDetailDescribesCreateWithMaster(any(), any())).thenReturn(Collections.emptyList());
        when(fieldRelationGraphService.buildReverseDependencyGraph(anyList(), eq(false), eq(true), eq(true)))
                .thenThrow(new ValidateException("对象已停用"));

        assertThrows(ValidateException.class, () -> calculateService.batchCalculate(arg, serviceContext));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当描述映射中存在空值时，doBatchCalculate方法应该抛出ValidateException异常
     */
    @Test
    @DisplayName("doBatchCalculate - Throws Exception When Describe Map Contains Null Value")
    void testDoBatchCalculateThrowsExceptionWhenDescribeMapContainsNullValue() {
        String masterObjApiName = "AccountObj";
        String detailObjApiName = "DetailObj";

        IObjectDescribe masterDescribe = mock(IObjectDescribe.class);
        when(masterDescribe.getApiName()).thenReturn(masterObjApiName);

        BatchCalculate.Arg arg = new BatchCalculate.Arg();
        arg.setMasterObjectApiName(masterObjApiName);

        Map<String, List<String>> calculateFieldApiNames = new HashMap<>();
        List<String> accountFields = new ArrayList<>();
        accountFields.add("name");
        calculateFieldApiNames.put("AccountObj", accountFields);
        List<String> detailFields = new ArrayList<>();
        detailFields.add("field1");
        calculateFieldApiNames.put("DetailObj", detailFields);
        arg.setCalculateFieldApiNames(calculateFieldApiNames);

        Map<String, Object> masterDataMap = new HashMap<>();
        arg.setMasterData(ObjectDataDocument.of(masterDataMap));

        // 设置 describeLogicService.findObjects 返回包含空值的描述映射
        Map<String, IObjectDescribe> describeMap = new HashMap<>();
        describeMap.put(masterObjApiName, masterDescribe);
        describeMap.put(detailObjApiName, null);
        when(describeLogicService.findObjects(anyString(), any())).thenReturn(describeMap);

        // 设置 detailDescribes
        when(describeLogicService.findDetailDescribesCreateWithMaster(anyString(), anyString())).thenReturn(Collections.emptyList());

        assertThrows(NullPointerException.class, () -> calculateService.batchCalculate(arg, serviceContext));
    }
}
