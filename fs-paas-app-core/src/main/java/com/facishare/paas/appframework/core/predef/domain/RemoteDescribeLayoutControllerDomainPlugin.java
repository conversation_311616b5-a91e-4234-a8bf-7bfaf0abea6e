package com.facishare.paas.appframework.core.predef.domain;

import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.domain.ControllerDomainPlugin;
import com.facishare.paas.appframework.core.model.domain.DomainProvider;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created by zhouwr on 2022/11/2.
 */
@Component
@DomainProvider(name = "_remote_DescribeLayout")
public class RemoteDescribeLayoutControllerDomainPlugin implements DescribeLayoutControllerDomainPlugin {

    @Autowired
    private DescribeLayoutControllerDomainPluginProxy proxy;

    @Override
    public Result before(ControllerContext context, Arg arg) {
        return doPost(context, arg, BEFORE);
    }

    @Override
    public Result after(ControllerContext context, Arg arg) {
        return doPost(context, arg, AFTER);
    }

    private Result doPost(ControllerContext context, Arg arg, String method) {
        Map<String, String> header = RestUtils.buildHeaders(context.getUser());
        String url = arg.getPluginDescribe().getRestApiUrl(method);
        if (Strings.isNullOrEmpty(url)) {
            return new Result();
        }
        return proxy.post(url, arg, header).getData();
    }
}
