package com.facishare.paas.appframework.core.predef.handler;

import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.handler.Handler;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.rest.APIResult;
import com.facishare.paas.appframework.core.util.RestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * Created by zhouwr on 2023/3/3.
 */
@Slf4j
@Component
@HandlerProvider(name = "defaultRemoteHandler")
public class DefaultRemoteHandler implements Handler {
    @Autowired
    private RemoteHandlerProxy proxy;

    @Override
    public Result handle(HandlerContext context, Arg arg) {
        Map<String, String> headers = RestUtils.buildHeaders(context.getUser());
        String jsonString = proxy.post(arg, headers, arg.getHandlerDescribe().getApiName(), HANDLE, arg.getHandlerDescribe().getRestApiUrl());
        return buildResult(arg, jsonString);
    }

    protected Class<? extends Result> getResultType(Arg arg) {
        return HandlerResultTypeMappings.getHandlerResultType(arg.getHandlerDescribe().getInterfaceCode());
    }

    private Result buildResult(Arg arg, String jsonString) {
        Class<? extends Result> resultType = getResultType(arg);
        if (Objects.isNull(resultType)) {
            throw new ValidateException("Cannot find result type for interfaceCode: " + arg.getHandlerDescribe().getInterfaceCode());
        }
        APIResult apiResult = JacksonUtils.fromJson(jsonString, APIResult.class, resultType);
        if (!apiResult.isSuccess()) {
            log.warn("execute remote handler failed,result:{}", jsonString);
            throw new ValidateException(apiResult.getMessage(), apiResult.getCode());
        }
        return (Result) apiResult.getData();
    }
}
