package com.facishare.paas.appframework.core.predef.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.privilege.model.valueobject.CrmResult;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.ocr.api.model.OcrType;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.model.GetDefault;
import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.common.service.model.WhereUsed;
import com.facishare.paas.appframework.common.service.model.WhereUsed.Menu;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.controller.StandardController;
import com.facishare.paas.appframework.core.predef.controller.StandardDesignerLayoutController;
import com.facishare.paas.appframework.core.predef.controller.StandardDesignerLayoutResourceController;
import com.facishare.paas.appframework.core.predef.service.dto.ManageGroupDTO;
import com.facishare.paas.appframework.core.predef.service.dto.config.*;
import com.facishare.paas.appframework.core.predef.service.dto.describeExtra.FindDescribeExtra;
import com.facishare.paas.appframework.core.predef.service.dto.describeExtra.UpdateDescribeExtra;
import com.facishare.paas.appframework.core.predef.service.dto.objectDescribe.*;
import com.facishare.paas.appframework.core.predef.service.dto.objectDescribe.FindDescribeAndAbstractLayout.SupportFromObjApiNames;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.quota.TenantLicenseInfo;
import com.facishare.paas.appframework.license.util.LicenseConstants;
import com.facishare.paas.appframework.license.util.ModulePara;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.button.LayoutDesignerButtonManager;
import com.facishare.paas.appframework.metadata.button.SpecialButtonManager;
import com.facishare.paas.appframework.metadata.button.SpecialButtonProvider;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import com.facishare.paas.appframework.metadata.config.ObjectConfig;
import com.facishare.paas.appframework.metadata.config.ObjectConfigService;
import com.facishare.paas.appframework.metadata.copy.CopyObjectService;
import com.facishare.paas.appframework.metadata.dto.*;
import com.facishare.paas.appframework.metadata.dto.sfa.FeedRolePermission;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.expansion.DescribeExpansionRender;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.appframework.metadata.fieldextra.FieldBackgroundExtraLogicService;
import com.facishare.paas.appframework.metadata.layout.*;
import com.facishare.paas.appframework.metadata.layout.factory.ListComponentFactory;
import com.facishare.paas.appframework.metadata.layout.factory.ViewComponentFactory;
import com.facishare.paas.appframework.metadata.layout.resource.LayoutResourceService;
import com.facishare.paas.appframework.metadata.objects.ObjectListConfig;
import com.facishare.paas.appframework.metadata.ocr.OcrDataDto;
import com.facishare.paas.appframework.metadata.ocr.OcrIdentifyDto;
import com.facishare.paas.appframework.metadata.reference.RefFieldService;
import com.facishare.paas.appframework.metadata.reference.WhereUsedDisplayConf.MenuConf;
import com.facishare.paas.appframework.metadata.relation.EditCalculateParam;
import com.facishare.paas.appframework.metadata.relation.ObjectRelationGraph;
import com.facishare.paas.appframework.metadata.relation.ObjectRelationGraphService;
import com.facishare.paas.appframework.metadata.repository.model.MtConvertRule;
import com.facishare.paas.appframework.metadata.util.ProductUtil;
import com.facishare.paas.appframework.privilege.FeedPrivilegeProxy;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.dto.FeedPrivilegeInfo;
import com.facishare.paas.appframework.privilege.dto.FindFeedRolePermission;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.checker.CheckerResult;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.api.service.IObjectArchiveRuleService;
import com.facishare.paas.metadata.api.service.IObjectDescribeExtService;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.FieldSection;
import com.facishare.paas.metadata.impl.ui.layout.component.RelatedObjectList;
import com.facishare.paas.metadata.impl.ui.layout.component.SimpleComponent;
import com.facishare.paas.metadata.ui.layout.*;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.fxiaoke.i18n.client.api.Localization;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.base.Strings;
import com.google.common.collect.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.userdefobj.DefObjConstants.UDOBJ;
import static com.facishare.paas.appframework.common.util.AppFrameworkConfig.getApiNameByName;
import static com.facishare.paas.appframework.common.util.AppFrameworkConfig.isDateTimeSupportNotUseMultitimeZoneTenant;
import static com.facishare.paas.appframework.common.util.TenantUtil.Tag.CALC_CRITERIA;
import static com.facishare.paas.appframework.common.util.TenantUtil.Tag.MAX_COUNT;
import static com.facishare.paas.appframework.metadata.ObjectDescribeExt.ICON_SLOT;
import static com.facishare.paas.metadata.api.describe.IObjectDescribe.DEFINE_TYPE_CUSTOM;

/**
 * 服务于对象设计器的元数据服务
 * Created by liyiguang on 2017/10/11.
 */
@Slf4j
@Service
@ServiceModule("describe")
public class ObjectDesignerService {

    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private CopyObjectService copyObjectService;

    @Autowired
    private LayoutLogicService layoutLogicService;

    @Autowired
    private MetaDataService metaDataService;

    @Autowired
    private CustomButtonService customButtonService;

    @Autowired
    private DataListHeaderConfigService dataListHeaderConfigService;

    @Autowired
    private SpecialButtonManager specialButtonManager;

    @Autowired
    private LicenseService licenseService;

    @Autowired
    ObjectRelationGraphService objectRelationGraphService;

    @Autowired
    CRMRestService crmRestService;

    @Autowired
    ServiceFacade serviceFacade;

    @Autowired
    ConfigService configService;

    @Autowired
    LogService logService;

    @Autowired
    private LayoutDesignerButtonManager layoutDesignerButtonManager;

    @Autowired
    private ObjectConfigService objectConfigService;

    @Autowired
    private SceneLogicService sceneLogicService;

    @Autowired
    private ButtonLogicService buttonLogicService;

    @Autowired
    private FeedPrivilegeProxy feedPrivilegeProxy;

    @Autowired
    private IOcrLogicService ocrLogicService;

    @Autowired
    private RedissonService redissonService;

    @Autowired
    private IObjectArchiveRuleService objectArchiveRuleService;

    @Autowired
    private ViewComponentFactory viewComponentFactory;

    @Autowired
    private ListComponentFactory listComponentFactory;

    @Autowired
    private LayoutResourceService layoutResourceService;
    @Autowired
    private OptionalFeaturesService optionalFeaturesService;

    @Autowired
    private I18nSettingService i18nSettingService;

    @Autowired
    private FieldBackgroundExtraLogicService fieldBackgroundExtraLogicService;
    @Autowired
    private TeamMemberRoleService teamMemberRoleService;

    private EntityReferenceExtService entityReferenceExtService;

    private IObjectDescribeExtService describeExtService;
    @Autowired
    private ObjectConvertRuleService objectConvertRuleService;

    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;

    private ExtraDescribeLogicService objExtraService;

    @Autowired
    private InfraServiceFacadeImpl infraServiceFacade;

    @Autowired
    public void setEntityReferenceExtService(EntityReferenceExtService entityReferenceExtService) {
        this.entityReferenceExtService = entityReferenceExtService;
    }

    @Autowired
    public void setDescribeExtService(IObjectDescribeExtService describeExtService) {
        this.describeExtService = describeExtService;
    }

    @Autowired
    public void setObjExtraService(ExtraDescribeLogicService objExtraService) {
        this.objExtraService = objExtraService;
    }

    public static final String KEY = "relevant.team.data.permission.";

    private FsGrayReleaseBiz dataAuthGray = FsGrayRelease.getInstance("data-auth");

    private boolean disablePublicObjectReferenceRelevantTeamPermission(String tenantId) {
        return dataAuthGray.isAllow("disablePublicObjectReferenceRelevantTeamPermission", tenantId);
    }

    @ServiceMethod("findDescribeList")
    public FindDescribeList.Result findDescribeList(FindDescribeList.Arg arg, ServiceContext context) {
        String actionCode = Strings.isNullOrEmpty(arg.getActionCode()) ? ObjectAction.VIEW_LIST.getActionCode() : arg.getActionCode();
        Set<String> objectVisibleScopes = getObjectVisibleScopes(arg.isIncludeBigObject(), arg.isIncludeSocialObject());
        ObjectDescribeFinder objectDescribeFinder = ObjectDescribeFinder.builder()
                .user(context.getUser())
                .describeDefineType(!arg.isIncludeSystemObj() ? DEFINE_TYPE_CUSTOM : null)
                .isOnlyActivate(!arg.isIncludeUnActived())
                .isExcludeDetailObj(false)
                .isExcludeDetailWithMasterCreated(false)
                .isAsc(arg.isAsc())
                .visibleScope(objectVisibleScopes)
                .onlyVisibleScope(arg.isOnlyVisibleScope())
                .build();
        List<IObjectDescribe> describeList = describeLogicService.findObjectsByTenantId(objectDescribeFinder);
        describeList = describeLogicService.filterDescribesWithActionCode(context.getUser(), describeList, actionCode);
        ManageGroup manageGroup = describeLogicService.queryObjectManageGroup(context.getUser(), arg.getSourceInfo());

        return FindDescribeList.Result.builder()
                .objectDescribeList(ObjectDescribeDocument.ofList(describeList))
                .manageGroup(ManageGroupDTO.of(manageGroup))
                .build();
    }

    private Set<String> getObjectVisibleScopes(boolean includeBigObject, boolean includeSocialObject) {
        Set<String> objectVisibleScopes = Sets.newHashSet();
        if (includeBigObject) {
            objectVisibleScopes.addAll(IObjectDescribe.BIG_OBJECT_VISIBLE_SCOPE);
        }
        if (includeSocialObject) {
            objectVisibleScopes.add(IObjectDescribe.VISIBLE_SCOPE_SOCIAL);
        }
        return objectVisibleScopes;
    }

    @ServiceMethod("findDescribeManageList")
    public FindDescribeList.Result findDescribeManageList(FindDescribeList.Arg arg, ServiceContext context) {
        Set<String> objectVisibleScopes = getObjectVisibleScopes(arg.isIncludeBigObject(), arg.isIncludeSocialObject());
        ObjectDescribeFinder objectDescribeFinder = ObjectDescribeFinder.builder()
                .user(context.getUser())
                .describeDefineType(arg.getDescribeDefineType())
                .isOnlyActivate(!arg.isIncludeUnActived())
                .isExcludeDetailObj(BooleanUtils.isTrue(arg.getIsExcludeDetailObj()))
                .isExcludeDetailWithMasterCreated(false)
                .isAsc(arg.isAsc())
                .sourceInfo(arg.getSourceInfo())
                .visibleScope(objectVisibleScopes)
                .onlyVisibleScope(arg.isOnlyVisibleScope())
                .includeChangeOrderObject(arg.getIncludeChangeOrderObject())
                .build();
        List<IObjectDescribe> describeList = describeLogicService.findObjectsByTenantId(objectDescribeFinder);
        int useableDescribeCount = describeLogicService.useableDescribeCount(context.getUser(), arg.isIncludeBigObject(), arg.isIncludeSocialObject(), arg.isOnlyVisibleScope());
        ManageGroup manageGroup = describeLogicService.queryObjectManageGroup(context.getUser(), arg.getSourceInfo(), RequestUtil.isCepRequest());

        Map<String, ObjectConfig> objectConfigs = null;
        if (arg.isIncludeControlLevel()) {
            objectConfigs = objectConfigService.queryObjectConfigByControlLevel(context.getUser(), describeList);
        }
        return FindDescribeList.Result.builder()
                .objectDescribeList(ObjectDescribeDocument.ofList(describeList))
                .useableDescribeCount(useableDescribeCount)
                .manageGroup(ManageGroupDTO.of(manageGroup))
                .objectConfigs(objectConfigs)
                .build();
    }

    @ServiceMethod("findDescribeListWithPartAttribute")
    public FindDescribeList.Result findDescribeListWithPartAttribute(FindDescribeList.Arg arg, ServiceContext context) {
        List<IObjectDescribe> describeList = describeLogicService.findObjectsByTenantId(context.getTenantId(),
                arg.getDescribeDefineType(), !arg.isIncludeUnActived(), false, false, arg.isAsc(), arg.getSourceInfo());
        ManageGroup manageGroup = describeLogicService.queryObjectManageGroup(context.getUser(), arg.getSourceInfo());
        List<ObjectDescribeDocument> docList = ObjectDescribeDocument.ofList(describeList);
        ObjectDescribeDocument.projectField(docList, Lists.newArrayList(CollectionUtils.nullToEmpty(arg.getIncludeAttributes())));
        return FindDescribeList.Result.builder()
                .objectDescribeList(docList)
                .manageGroup(ManageGroupDTO.of(manageGroup))
                .build();
    }

    @ServiceMethod("findCrmObjectList")
    public FindCrmObjectList.Result findCrmObjectList(FindCrmObjectList.Arg arg, ServiceContext context) {
        List<IObjectDescribe> describeList = describeLogicService.findDescribeByPrivilegeAndModule(context.getUser(),
                ObjectAction.VIEW_LIST.getActionCode(), !arg.isIncludeSystemObj(),
                !arg.isIncludeUnActived(), false, arg.isAsc());
        ManageGroup manageGroup = describeLogicService.queryObjectManageGroup(context.getUser(), arg.getSourceInfo());
        describeList.removeIf(a -> Objects.equals(a.getApiName(), ObjectAPINameMapping.ReturnedGoodsInvoiceProduct.getApiName()));
        List<FindCrmObjectList.ObjectInfo> objectInfoList =
                describeList.stream().map(x -> FindCrmObjectList.ObjectInfo.of(x.getApiName(), x.getDisplayName()))
                        .collect(Collectors.toList());
        return FindCrmObjectList.Result.builder()
                .objectList(objectInfoList)
                .manageGroup(ManageGroupDTO.of(manageGroup))
                .build();
    }

    @ServiceMethod("findDraftByApiName")
    public FindDraftByApiName.Result findDraftByApiName(FindDraftByApiName.Arg arg, ServiceContext context) {
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getDraftApiName());
        ILayout layout = null;
        if (arg.isIncludeLayout()) {
            LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
            layout = layoutLogicService.findDefaultLayout(layoutContext, arg.getLayoutType(), describe.getApiName());
        }

        ObjectDescribeDocument describeDocument = fillQuoteFieldOption(describe, arg.isFillQuoteFieldOption());

        return FindDraftByApiName.Result.builder()
                .objectDescribeDraft(ObjectDescribeDocument.of(describe))
                .objectDescribeExt(describeDocument)
                .layout(LayoutDocument.of(layout))
                .build();
    }

    @ServiceMethod("findDescribeByApiName")
    public FindDescribeByApiName.Result findDescribeByApiName(FindDescribeByApiName.Arg arg, ServiceContext context) {
        //该接口直接通过i8n接口获取字段label
        RequestContextManager.getContext().setAttribute(RequestContext.DIRECT_KEY, arg.isGetLabelDirect());
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
        //给主从字段补充show_detail_button属性，防止设计器默认成false
        ObjectDescribeExt.of(describe).fillDefaultShowDetailButton();
        // 过滤掉order_by字段
        describe.removeFieldDescribe(ObjectData.ORDER_BY);
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        describeExt.descriptionNullToEmpty();
        ILayout layout = null;
        List<Map> relatedList = null;
        List<IButtonDocument> documents = Lists.newArrayList();

        if (arg.isIncludeLayout()) {
            LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
            layout = layoutLogicService.findDefaultLayout(layoutContext, arg.getLayoutType(), describe.getApiName());
            LayoutExt layoutExt = LayoutExt.of(layout);
            LayoutStructure.restoreLayout(layoutExt, PageType.Designer);
            layoutExt.layoutDescriptionNullToEmpty();
            // 新建空白布局下发归属部门
            layoutExt.addDefaultDataOwnDepartment();
            //顶部信息
            handleTopInfo(describeExt, layoutExt, context.getUser());

            //需要按钮排序
            if (arg.isIncludeButtons()) {
                //同步按钮名称
                ButtonOrder.synchronizeName(layoutExt, describeExt);
                //查找自定义按钮
                List<IUdefButton> iButtons = findButtonsByUsePage(describeExt, context.getUser(), ButtonUsePageType.Detail);
                //过滤自定义按钮
                List<IButton> customButtons = ButtonOrder.filteringUdefButton(iButtons);
                List<IButton> orderedButtons = ButtonOrder.getOrderedButtonList(customButtons, describeExt);
                //老对象特殊按钮处理,不启用的按钮不在布局中显示
                if (describeExt.isSFAObject()) {
                    SpecialButtonProvider provider = specialButtonManager.getProvider(describeExt.getApiName());
                    List<IButton> specialButtons = provider.getSpecialButtons();
                    if (CollectionUtils.notEmpty(specialButtons)) {
                        //移除layout中没有启用的特殊按钮
                        List<IButton> removeButtons = specialButtons.stream().filter(s ->
                                !LayoutButtonExt.of(s).isActive()).collect(Collectors.toList());
                        orderedButtons.removeAll(removeButtons);
                    }
                }
                documents = IButtonDocument.ofList(orderedButtons);
            }
        }

        if (arg.isIncludeRelatedList()) {
            List<IComponent> components = Lists.newArrayList();

            //摘要
            components.add(RelatedObjectGroupComponentBuilder.builder()
                    .build().buildRecordComponentWithoutFieldsAndButtons(arg.getDescribeApiName()));

            //相关团队
            if (!describeExt.isSlaveObject()) {
                components.add(TeamComponentBuilder.builder().build().buildUserListComponentWithoutFieldsAndButtons());
            }

            List<IObjectDescribe> relatedDescribes = describeLogicService.findRelatedDescribes(context.getTenantId(), arg.getDescribeApiName());
            List<RelatedObjectDescribeStructure> detailObjects = describeExt.getDetailObjectDescribeStructures(relatedDescribes);
            List<RelatedObjectDescribeStructure> lookupObjects = describeExt.getRelatedObjectDescribeStructures(relatedDescribes);
            //根据配置中心的配置去掉不展示的关联对象
            lookupObjects.removeIf(x -> DefObjConstants.isReferenceObjectInvisible(describe.getApiName(), x.getRelatedObjectDescribe().getApiName()));

            //从对象
            components.addAll(MasterDetailGroupComponentBuilder.builder()
                    .detailObjectsDescribeList(detailObjects).build().getComponentListForDesigner());

            //关联对象
            components.addAll(RelatedObjectGroupComponentBuilder.builder()
                    .user(context.getUser())
                    .objectDescribe(describeExt).relatedObjectDescribeList(lookupObjects).build().getComponentListForDesigner());

            //修改记录
            components.add(LayoutExt.buildModifyRecordComponent());

            //根据配置过滤组件
            components.removeIf(x -> DefObjConstants.isComponentInvisible(arg.getDescribeApiName(), x.getName()));

            relatedList = components.stream().map(x -> ((DocumentBasedBean) x).getContainerDocument()).collect(Collectors.toList());
        }

        ObjectDescribeDocument describeDocument = fillQuoteFieldOption(describe, arg.isFillQuoteFieldOption());

        ObjectDescribeDocument describeExtra = null;
        if (arg.isIncludeDescribeExtra()) {
            describeExtra = findDescribeExtra(context.getUser(), arg.getDescribeApiName());
            IActionContext metadataContext = ActionContextExt.of(context.getUser()).getContext();
            Map<String, IObjectDescribeExtra> describeExtMap = describeExtService.findDescribeExtByDescribeApiName(Lists.newArrayList(arg.getDescribeApiName()), metadataContext);
            Integer iconSlot = Optional.ofNullable(describeExtMap)
                    .map(x -> x.get(describe.getApiName()))
                    .map(IObjectDescribeExtra::getIconIndex)
                    .orElse(null);
            if (Objects.nonNull(iconSlot) && Objects.nonNull(describeExtra)) {
                describeExtra.put(ObjectDescribeExt.ICON_SLOT, iconSlot);
            }
        }

        return FindDescribeByApiName.Result.builder()
                .objectDescribe(ObjectDescribeDocument.of(describe))
                .objectDescribeExt(describeDocument)
                .describeExtra(describeExtra)
                .layout(LayoutDocument.of(layout))
                .relatedList(relatedList)
                .buttons(documents)
                .fieldsExtra(arg.isIncludeFieldsExtra() ? fieldBackgroundExtraLogicService.findFieldsExtra(context.getUser(), arg.getDescribeApiName()) : null)
                .build();
    }

    private void handleTopInfo(ObjectDescribeExt describeExt, LayoutExt layoutExt, User user) {
        if (!layoutExt.getTopInfoComponentSilently().isPresent()) {
            SimpleComponent topInfo = TopInfoComponentBuilder.builder()
                    .user(user)
                    .describeExt(describeExt)
                    .build()
                    .getSimpleComponentWithoutButtons();
            layoutExt.setTopInfo(topInfo);
        } else {
            SimpleComponent topInfo = layoutExt.getTopInfoComponentSilently().get();
            if (CollectionUtils.empty(topInfo.getFieldSections())) {
                topInfo.setFieldSections(Lists.newArrayList(new FieldSection()));
            }
            topInfo.getFieldSections().forEach(x -> {
                if (!((DocumentBasedBean) x).containsKey(IFieldSection.FORM_FIELDS)) {
                    x.setFields(Lists.newArrayList());
                }
            });
        }
    }

    private ObjectDescribeDocument fillQuoteFieldOption(IObjectDescribe describe, boolean fillQuoteFieldOption) {
        if (!fillQuoteFieldOption) {
            return null;
        }
        Map<String, Map<String, Object>> fieldExtMap = serviceFacade.fillQuoteFieldOption(describe);
        if (CollectionUtils.empty(fieldExtMap)) {
            return null;
        }
        Map<String, Object> fieldsExt = Maps.newHashMap();
        fieldsExt.put("fields", fieldExtMap);
        return ObjectDescribeDocument.of(fieldsExt);
    }

    //11.13为设计器开发,因为findByExample中不返回fieldList了。
    @ServiceMethod("findDescribeListByApiName")
    public FindDescribeListByApiName.Result findDescribeListByApiName(FindDescribeListByApiName.Arg arg, ServiceContext context) {
        Map<String, IObjectDescribe> objectDrafts = describeLogicService.findObjects(context.getTenantId(), arg.getDescribeApiNameList());
        List<IObjectDescribe> describes = Lists.newArrayList();
        describes.addAll(Lists.newArrayList(objectDrafts.values()));
        describes.removeIf(a -> BooleanUtils.isFalse(arg.getIncludeBigObject()) && a.isBigObject() || (BooleanUtils.isFalse(arg.getIncludeSocialObject()) && a.isSocialObject()));
        FindDescribeListByApiName.Result result = new FindDescribeListByApiName.Result();
        result.setObjectDescribeList(ObjectDescribeDocument.ofList(describes));
        return result;
    }

    @ServiceMethod("findDraftListByApiName")
    public FindDraftListByApiName.Result findDraftListByApiName(FindDraftListByApiName.Arg arg, ServiceContext context) {
        Map<String, IObjectDescribe> objectDrafts = describeLogicService.findObjects(context.getTenantId(), arg.getDraftApiNameList());
        List<IObjectDescribe> describes = Lists.newArrayList();
        describes.addAll(objectDrafts.entrySet().stream().map(Map.Entry::getValue).collect(Collectors.toList()));
        FindDraftListByApiName.Result result = new FindDraftListByApiName.Result();
        result.setObjectDescribeDraftList(ObjectDescribeDocument.ofList(describes));
        return result;
    }

    @ServiceMethod("checkAndFindLatest")
    public CheckAndFindLatest.Result checkAndFindLatest(CheckAndFindLatest.Arg arg, ServiceContext context) {
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(context.getTenantId(), arg.getDescribeAPIName());
        if (Objects.isNull(objectDescribe)) {
            return CheckAndFindLatest.Result.builder()
                    .errorCode(CheckAndFindLatest.Status.CODE_NOT_FOUND)
                    .errorMessage(CheckAndFindLatest.Status.MESSAGE_NOT_FOUND)
                    .build();
        }
        if (arg.getRevision() >= objectDescribe.getVersion()) {
            return CheckAndFindLatest.Result.builder()
                    .errorCode(CheckAndFindLatest.Status.CODE_DATA_NOT_CHANGE)
                    .errorMessage(arg.getRevision() + " is the latest")
                    .build();
        }
        return CheckAndFindLatest.Result.builder()
                .errorCode(CheckAndFindLatest.Status.CODE_OK)
                .errorMessage(CheckAndFindLatest.Status.MESSAGE_OK)
                .result(ObjectDescribeDocument.of(objectDescribe))
                .build();
    }

    @ServiceMethod("createDescribe")
    public CreateDescribe.Result createDescribe(CreateDescribe.Arg arg, ServiceContext context) {
        DescribeResult describeResult = describeLogicService.createDescribe(
                context.getUser(),
                arg.getJson_data(),
                arg.getJson_layout(),
                arg.getJson_list_layout(),
                arg.isActive(),
                arg.isInclude_layout());
        return CreateDescribe.Result.builder()
                .objectDescribe(ObjectDescribeDocument.of(describeResult.getObjectDescribe()))
                .layout(LayoutDocument.of(describeResult.getLayout()))
                .build();
    }

    @ServiceMethod("findCustomFieldDescribe")
    public FindCustomFieldDescribe.Result findCustomFieldDescribe(FindCustomFieldDescribe.Arg arg, ServiceContext
            context) {
        if (Strings.isNullOrEmpty(arg.getFieldApiName()) || Strings.isNullOrEmpty(arg.getDescribeAPIName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        boolean defaultLang = arg.isNoNeedReplaceI18n()
                && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FIELD_NAME_RECORD_TYPE_FIELD_PACKET,
                context.getTenantId());
        FieldResult fieldResult = describeLogicService.findCustomFieldDescribe(context.getTenantId(), arg
                .getDescribeAPIName(), arg.getFieldApiName(), defaultLang);
        FieldDescribeExt.of(fieldResult.getField()).descriptionNullToEmpty();
        FieldDescribeExt.of(fieldResult.getField()).fillDefaultShowDetailButton();

        ObjectDescribeDocument describeExtra = null;
        if (arg.isIncludeDescribeExtra()) {
            describeExtra = findDescribeExtra(context.getUser(), arg.getDescribeAPIName());
        }
        List<I18nInfo> i18nInfos = i18nSettingService.queryTranslation(context.getTenantId(),
                arg.getDescribeAPIName(),
                Lists.newArrayList(I18nInfo.builder()
                        .apiName(arg.getFieldApiName())
                        .type(I18nInfo.Type.FIELD_LABEL.getFrontType())
                        .build()));
        FindCustomFieldDescribe.Result result = new FindCustomFieldDescribe.Result();
        result.setLayout_list(fieldResult.getLayout_list());
        result.setField(((DocumentBasedBean) fieldResult.getField()).getContainerDocument());
        result.setI18nInfoList(i18nInfos);
        result.setDescribeExtra(describeExtra);
        result.setFieldsExtra(arg.isIncludeFieldsExtra() ? fieldBackgroundExtraLogicService.findFieldsExtraByFieldApiNames(context.getUser(), arg.getDescribeAPIName(), Lists.newArrayList(arg.getFieldApiName())) : null);
        return result;
    }

    @Nullable
    private ObjectDescribeDocument findDescribeExtra(User user, String describeApiName) {
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), describeApiName);
        DescribeExtra extra = describeLogicService.findDescribeExtraByRenderType(user, describe, Collections.emptyList(),
                DescribeExpansionRender.RenderType.Designer, true);
        return ObjectDescribeDocument.of(extra);
    }

    @ServiceMethod("addDescribeCustomField")
    public AddDescribeCustomField.Result addDescribeCustomField(AddDescribeCustomField.Arg arg, ServiceContext context) {
        //加锁
        RLock lock = describeLogicService.tryLockObject(context.getTenantId(), arg.getDescribeAPIName());
        try {
            //布局信息
            List<FieldLayoutPojo> fieldLayoutPojoList = JSON.parseArray(arg.getLayout_list(), FieldLayoutPojo.class);
            List<IFieldDescribe> fieldList = getGroupFields(arg.getGroup_fields());
            //描述添加字段
            IObjectDescribe describe = describeLogicService.addDescribeCustomField(
                    context.getUser(),
                    arg.getDescribeAPIName(),
                    arg.getField_describe(),//字段描述
                    arg.getPersistentDataCalc(),
                    fieldLayoutPojoList,
                    fieldList);
            //更新描述附加信息
            ObjectDescribeDocument describeExtra = updateDescribeExtra(context.getUser(), arg.getDescribeAPIName(), arg.getDescribeExtra());
            fieldBackgroundExtraLogicService.bulkUpsertFieldsExtra(context.getUser(), arg.getDescribeAPIName(), arg.getFieldsExtra());
            List<I18nInfo> i18nInfoList = arg.getI18nInfoList();
            i18nSettingService.synTranslateToMeta(context.getTenantId(), arg.getDescribeAPIName(), i18nInfoList);

            return AddDescribeCustomField.Result.builder()
                    .objectDescribe(ObjectDescribeDocument.of(describe))
                    .describeExtra(describeExtra)
                    .fieldsExtra(arg.getFieldsExtra())
                    .build();
        } finally {
            redissonService.unlock(lock);
        }
    }


    @ServiceMethod("batchAddDescribeCustomField")
    public AddDescribeCustomField.Result batchAddDescribeCustomField(BatchAddDescribeCustomField.Arg arg, ServiceContext context) {
        if (CollectionUtils.empty(arg.getFields()) || StringUtils.isBlank(arg.getDescribeAPIName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        //加锁
        RLock lock = describeLogicService.tryLockObject(context.getTenantId(), arg.getDescribeAPIName());
        try {
            //字段
            List<IFieldDescribe> fieldDescribeList = arg.getFields().stream().map(x ->
                    (IFieldDescribe) FieldDescribeExt.of(x).getFieldDescribe()
            ).collect(Collectors.toList());
            //描述添加字段
            IObjectDescribe describe = describeLogicService.batchAddDescribeCustomField(
                    context.getUser(),
                    arg.getDescribeAPIName(),
                    fieldDescribeList//字段描述
            );
            return AddDescribeCustomField.Result.builder()
                    .objectDescribe(ObjectDescribeDocument.of(describe))
                    .build();
        } finally {
            redissonService.unlock(lock);
        }
    }

    @ServiceMethod("disableCustomField")
    public DisableCustomField.Result disableCustomField(DisableCustomField.Arg arg, ServiceContext context) {
        RLock lock = describeLogicService.tryLockObject(context.getTenantId(), arg.getDescribeAPIName());
        try {
            CheckerResult checkerResult = describeLogicService.disableField(
                    context.getUser(),
                    arg.getDescribeAPIName(),
                    arg.getField_api_name());

            return DisableCustomField.Result.builder()
                    .objectDescribe(ObjectDescribeDocument.of(checkerResult.getDescribe()))
                    .build();
        } finally {
            redissonService.unlock(lock);
        }
    }

    @ServiceMethod("enableDescribeCustomField")
    public EnableField.Result enableDescribeCustomField(EnableField.Arg arg, ServiceContext context) {
        RLock lock = describeLogicService.tryLockObject(context.getTenantId(), arg.getDescribeAPIName());
        try {
            IObjectDescribe objectDescribeDraft = describeLogicService.enableField(
                    context.getUser(),
                    arg.getDescribeAPIName(),
                    arg.getField_api_name(),
                    arg.getPersistentDataCalc());

            return EnableField.Result.builder()
                    .objectDescribe(ObjectDescribeDocument.of(objectDescribeDraft))
                    .build();
        } finally {
            redissonService.unlock(lock);
        }
    }

    @ServiceMethod("enableDescribe")
    public EnableDescribe.Result enableDescribe(EnableDescribe.Arg arg, ServiceContext context) {
        String apiName = null == arg.getDescribe_apiname() ? arg.getDraft_apiname() : arg.getDescribe_apiname();
        IObjectDescribe objectDescribeDraft = describeLogicService.enableDescribe(context.getUser(), apiName);

        return EnableDescribe.Result.builder()
                .objectDescribe(ObjectDescribeDocument.of(objectDescribeDraft))
                .build();

    }

    @ServiceMethod("disableDescribe")
    public DisableDescribe.Result disableDescribe(DisableDescribe.Arg arg, ServiceContext context) {
        String apiName = null == arg.getDescribe_apiname() ? arg.getDraft_apiname() : arg.getDescribe_apiname();
        IObjectDescribe objectDescribe = describeLogicService.disableDescribe(context.getUser(), apiName);

        return DisableDescribe.Result.builder()
                .objectDescribe(ObjectDescribeDocument.of(objectDescribe))
                .build();

    }

    @ServiceMethod("updateDescribe")
    public UpdateDescribe.Result updateDescribe(UpdateDescribe.Arg arg, ServiceContext context) {
        IObjectDescribe describeDraft = new ObjectDescribe();
        describeDraft.fromJsonString(arg.getJsonData());
        OptionalFeaturesSwitchDTO optionalFeaturesSwitchInConfig = optionalFeaturesService.findOptionalFeaturesSwitch(context.getTenantId(), describeDraft);
        ObjectDescribeDocument describeExtraArg = arg.getDescribeExtra();
        OptionalFeaturesSwitchDTO optionalFeaturesSwitchInArg = checkRelatedTeam(context.getUser(), describeDraft, describeExtraArg, optionalFeaturesSwitchInConfig);
        DescribeResult describeResult = describeLogicService.updateDescribe(
                context.getUser(), arg.getJsonData(), arg.getJsonLayout(), arg.isActive(), arg.isIncludeLayout());
        String describeApiName = describeResult.getObjectDescribe().getApiName();
        ObjectDescribeDocument describeExtra = updateDescribeExtra(context.getUser(), describeApiName, describeExtraArg);   // 更新基本信息的可选功能
        IActionContext metadataContext = ActionContextExt.of(context.getUser()).getContext();
        // 问题原因：
        // icon_slot 用于替换旧的 icon_index，即如果对象附加描述存在 icon_slot 时使用 icon_slot，不存在继续使用 icon_index。
        // 因为前端控制了灰度了"预设对象支持更换图标"时更换图标才会去修改 icon_slot 值，未灰度只修改 icon_index。
        // 在灰度期间，未灰度的企业的某对象附加描述存在了 icon_slot，将会被迫使用新逻辑，但是此时更换对象图标并不会修改 icon_slot。9
        // 解决方法：
        // 所以如果当前企业没有灰度"预设对象支持更换图标" 且 对象是自定义对象 且 对象属性 icon_index 和 icon_slot 都不为空时
        // 将 icon_index + 1 设置到 icon_slot 上。
        boolean presetObjChangeIcon = UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CHANGE_ICON_PRESET_OBJECT_GRAY, context.getTenantId());
        if (!presetObjChangeIcon && Objects.nonNull(describeExtraArg)
                && Objects.nonNull(describeExtraArg.get(ICON_SLOT)) // 未灰度企业对象描述存在了 icon_slot，被迫使用了新逻辑，需保证 icon_slot
                && Objects.nonNull(describeDraft.getIconIndex()) // 修改 icon_slot 需要从 icon_index 取值计算，icon_index 不能为空
                && ObjectDescribeExt.isCustomObject(describeApiName)) { // 自定义对象才允许修改，预设对象不允许修改图标
            describeExtraArg.put(ICON_SLOT, describeDraft.getIconIndex() + 1);
        }
        if (presetObjChangeIcon || ObjectDescribeExt.isCustomObject(describeApiName)) {
            ObjectDescribeExtraExt objectDescribeExtra = ObjectDescribeExtraExt.of(describeApiName, describeDraft, describeExtraArg);
            if (Objects.nonNull(objectDescribeExtra.getIconSlot())) {
                describeExtService.upsert(metadataContext, Lists.newArrayList(objectDescribeExtra.getObjectDescribeExtra()));
            }
        }
        updateOptionalFeaturesSwitch(context.getUser(), describeApiName, describeExtra, optionalFeaturesSwitchInArg, optionalFeaturesSwitchInConfig);
        return UpdateDescribe.Result.builder()
                .objectDescribe(ObjectDescribeDocument.of(describeResult.getObjectDescribe()))
                .layout(LayoutDocument.of(describeResult.getLayout()))
                .describeExtra(describeExtra)
                .build();
    }


    @ServiceMethod("updateOptionalFeaturesSwitch")
    public UpdateOptionalFeaturesSwitch.Result updateOptionalFeaturesSwitch(UpdateOptionalFeaturesSwitch.Arg arg, ServiceContext context) {
        if (CollectionUtils.empty(arg.getDescribeApiNames())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        List<IObjectDescribe> describe = describeLogicService.findObjectList(context.getTenantId(), arg.getDescribeApiNames());
        Map<String, OptionalFeaturesSwitchDTO> stringOptionalFeaturesSwitchDTOMap = optionalFeaturesService.batchQueryOptionalFeaturesSwitch(context.getTenantId(), describe);
        stringOptionalFeaturesSwitchDTOMap.forEach((describeApiName, optionalFeaturesSwitchDTO) -> {
            // 如果修改记录、相关团队和跨对象筛选开关都已经开启，则不需要再处理
            if (BooleanUtils.isTrue(optionalFeaturesSwitchDTO.getIsModifyRecordEnabled()) &&
                    BooleanUtils.isTrue(optionalFeaturesSwitchDTO.getIsRelatedTeamEnabled()) &&
                    BooleanUtils.isTrue(optionalFeaturesSwitchDTO.getCrossObjectFilterButton())) {
                return;
            }
            if (BooleanUtils.isNotTrue(optionalFeaturesSwitchDTO.getIsModifyRecordEnabled())) {
                optionalFeaturesSwitchDTO.setIsModifyRecordEnabled(BooleanUtils.isTrue(arg.getIsModifyRecordEnabled()));
            }
            if (BooleanUtils.isNotTrue(optionalFeaturesSwitchDTO.getIsRelatedTeamEnabled())) {
                optionalFeaturesSwitchDTO.setIsRelatedTeamEnabled(BooleanUtils.isTrue(arg.getIsModifyRelatedTeamEnabled()));
            }
            // 设置跨对象筛选开关
            if (BooleanUtils.isNotTrue(optionalFeaturesSwitchDTO.getCrossObjectFilterButton())) {
                optionalFeaturesSwitchDTO.setCrossObjectFilterButton(BooleanUtils.isTrue(arg.getCrossObjectFilterButton()));
            }
            configService.upsertTenantConfig(context.getUser(), describeApiName + "|" + OptionalFeaturesService.OPTIONAL_FEATURES, JSON.toJSONString(optionalFeaturesSwitchDTO), ConfigValueType.JSON);
            logService.log(context.getUser(), EventType.MODIFY, ActionType.UPDATE_OBJ, describeApiName,
                    I18NExt.text(I18NKey.OPTIONAL_FEATURES_DETAILS,
                            I18NExt.getSwitchState(optionalFeaturesSwitchDTO.getIsRelatedTeamEnabled()),
                            I18NExt.getSwitchState(optionalFeaturesSwitchDTO.getIsGlobalSearchEnabled()),
                            I18NExt.getSwitchState(optionalFeaturesSwitchDTO.getIsFollowUpDynamicEnabled()),
                            I18NExt.getSwitchState(optionalFeaturesSwitchDTO.getIsModifyRecordEnabled()),
                            I18NExt.getSwitchState(optionalFeaturesSwitchDTO.getMultiFieldSort()),
                            I18NExt.getSwitchState(optionalFeaturesSwitchDTO.getCrossObjectFilterButton())
                    )
            );
        });
        return new UpdateOptionalFeaturesSwitch.Result();
    }

    private OptionalFeaturesSwitchDTO checkRelatedTeam(User user, IObjectDescribe objectDescribe, ObjectDescribeDocument describeExtraArg, OptionalFeaturesSwitchDTO optionalFeaturesSwitchInConfig) {
        if (CollectionUtils.empty(describeExtraArg) || !AppFrameworkConfig.isOptionalFeaturesSupport(user.getTenantId())) {
            return null;
        }
        OptionalFeaturesSwitchDTO optionalFeaturesSwitchInArg = generateOptionalFeaturesSwitchDTO(user, objectDescribe, describeExtraArg);
        if (isSkipOptionalFeaturesProcess(optionalFeaturesSwitchInArg, optionalFeaturesSwitchInConfig)) {
            return null;
        }
        // 关闭相关团队开关需要检验
        if (optionalFeaturesSwitchInConfig.getIsRelatedTeamEnabled() && !optionalFeaturesSwitchInArg.getIsRelatedTeamEnabled()
                && !metaDataService.checkDataRelevantTeamOnlyExistOwner(user, objectDescribe.getApiName())) {
            // 有历史数据时提示："已有历史数据使用了相关团队，请先处理历史数据再关闭"
            // "Relevant teams have been used for historical data, please process the historical data before closing
            throw new ValidateException(I18NExt.text(I18NKey.HISTORICAL_DATA_USED_RELATED_TEAM));
        }
        return optionalFeaturesSwitchInArg;
    }


    private static OptionalFeaturesSwitchDTO generateOptionalFeaturesSwitchDTO(User user, IObjectDescribe objectDescribe, ObjectDescribeDocument describeExtraArg) {
        Boolean relatedTeamSwitch = (Boolean) describeExtraArg.get(OptionalFeaturesService.RELATED_TEAM_SWITCH);
        Boolean globalSearchSwitch = (Boolean) describeExtraArg.get(OptionalFeaturesService.GLOBAL_SEARCH_SWITCH);
        Boolean followUpDynamicSwitch = (Boolean) describeExtraArg.get(OptionalFeaturesService.FOLLOW_UP_DYNAMIC_SWITCH);
        Boolean modifyRecordSwitch = (Boolean) describeExtraArg.get(OptionalFeaturesService.MODIFY_RECORD_SWITCH);
        Boolean multiFieldSortSwitch = (Boolean) describeExtraArg.get(OptionalFeaturesService.MULTI_FIELD_SORT);
        Boolean crossObjectFilterButtonSwitch = (Boolean) describeExtraArg.get(OptionalFeaturesService.CROSS_OBJECT_FILTER_BUTTON);
        OptionalFeaturesSwitchDTO mackDefaultByDescribe = OptionalFeaturesSwitchDTO.mackDefaultByDescribe(user.getTenantId(), objectDescribe);
        return OptionalFeaturesSwitchDTO.builder()
                .isRelatedTeamEnabled(Objects.isNull(relatedTeamSwitch) ? mackDefaultByDescribe.getIsRelatedTeamEnabled() : relatedTeamSwitch)
                .isGlobalSearchEnabled(Objects.isNull(globalSearchSwitch) ? mackDefaultByDescribe.getIsGlobalSearchEnabled() : globalSearchSwitch)
                .isFollowUpDynamicEnabled(Objects.isNull(followUpDynamicSwitch) ? mackDefaultByDescribe.getIsFollowUpDynamicEnabled() : followUpDynamicSwitch)
                .isModifyRecordEnabled(Objects.isNull(modifyRecordSwitch) ? mackDefaultByDescribe.getIsModifyRecordEnabled() : modifyRecordSwitch)
                .multiFieldSort(BooleanUtils.isTrue(multiFieldSortSwitch))
                .crossObjectFilterButton(Objects.isNull(crossObjectFilterButtonSwitch) ? mackDefaultByDescribe.getCrossObjectFilterButton() : crossObjectFilterButtonSwitch)
                .build();
    }


    private boolean isSkipOptionalFeaturesProcess(OptionalFeaturesSwitchDTO OptionalFeaturesSwitchArg, OptionalFeaturesSwitchDTO optionalFeaturesSwitchInConfig) {
        return Objects.equals(optionalFeaturesSwitchInConfig.getIsRelatedTeamEnabled(), OptionalFeaturesSwitchArg.getIsRelatedTeamEnabled())
                && Objects.equals(optionalFeaturesSwitchInConfig.getIsGlobalSearchEnabled(), OptionalFeaturesSwitchArg.getIsGlobalSearchEnabled())
                && Objects.equals(optionalFeaturesSwitchInConfig.getIsFollowUpDynamicEnabled(), OptionalFeaturesSwitchArg.getIsFollowUpDynamicEnabled())
                && Objects.equals(optionalFeaturesSwitchInConfig.getIsModifyRecordEnabled(), OptionalFeaturesSwitchArg.getIsModifyRecordEnabled())
                && Objects.equals(optionalFeaturesSwitchInConfig.getMultiFieldSort(), OptionalFeaturesSwitchArg.getMultiFieldSort())
                && Objects.equals(optionalFeaturesSwitchInConfig.getCrossObjectFilterButton(), OptionalFeaturesSwitchArg.getCrossObjectFilterButton());
    }

    private void updateOptionalFeaturesSwitch(User user, String describeApiName, ObjectDescribeDocument describeExtra, OptionalFeaturesSwitchDTO optionalFeaturesSwitchInArg, OptionalFeaturesSwitchDTO optionalFeaturesSwitchInConfig) {
        if (Objects.isNull(describeExtra)) {
            return;
        }
        if (Objects.isNull(optionalFeaturesSwitchInArg)) {
            describeExtra.putAll(infraServiceFacade.convertToMap(optionalFeaturesSwitchInConfig));
            return;
        }
        configService.upsertTenantConfig(user, describeApiName + "|" + OptionalFeaturesService.OPTIONAL_FEATURES, JSON.toJSONString(optionalFeaturesSwitchInArg), ConfigValueType.JSON);
        describeExtra.putAll(infraServiceFacade.convertToMap(optionalFeaturesSwitchInArg));
        logService.log(user, EventType.MODIFY, ActionType.UPDATE_OBJ, describeApiName,  // 记录对象基本信息可选功能的审计日志
                I18NExt.text(I18NKey.OPTIONAL_FEATURES_DETAILS,     // 新增验证规则{}
                        I18NExt.getSwitchState(optionalFeaturesSwitchInArg.getIsRelatedTeamEnabled()),
                        I18NExt.getSwitchState(optionalFeaturesSwitchInArg.getIsGlobalSearchEnabled()),
                        I18NExt.getSwitchState(optionalFeaturesSwitchInArg.getIsFollowUpDynamicEnabled()),
                        I18NExt.getSwitchState(optionalFeaturesSwitchInArg.getIsModifyRecordEnabled()),
                        I18NExt.getSwitchState(optionalFeaturesSwitchInArg.getMultiFieldSort()),
                        I18NExt.getSwitchState(optionalFeaturesSwitchInArg.getCrossObjectFilterButton())
                )
        );
    }

    @ServiceMethod("updateCustomFieldDescribe")
    public UpdateCustomFieldDescribe.Result updateCustomFieldDescribe(UpdateCustomFieldDescribe.Arg arg,
                                                                      ServiceContext context) {
        StopWatch stopWatch = StopWatch.create("updateCustomFieldDescribe");
        RLock lock = describeLogicService.tryLockObject(context.getTenantId(), arg.getDescribeAPIName());
        stopWatch.lap("tryLock");
        try {
            List<FieldLayoutPojo> fieldLayoutPojoList = JSON.parseArray(arg.getLayout_list(), FieldLayoutPojo.class);
            List<IFieldDescribe> fieldList = getGroupFields(arg.getGroup_fields());

            validateCascadeParent(arg.getField_describe(), context, fieldLayoutPojoList, arg.getDescribeAPIName());

            stopWatch.lap("updateCustomFieldDescribe start");
            IObjectDescribe objectDescribeDraft = describeLogicService.updateCustomFieldDescribe(
                    context.getUser(),
                    arg.getDescribeAPIName(),
                    arg.getField_describe(),
                    arg.getPersistentDataCalc(),
                    fieldLayoutPojoList, fieldList);
            stopWatch.lap("updateCustomFieldDescribe end");

            List<I18nInfo> i18nInfoList = arg.getI18nInfoList();
            i18nSettingService.synTranslateToMeta(context.getTenantId(), arg.getDescribeAPIName(), i18nInfoList);
            stopWatch.lap("syn trans info end");

            // 更新拓展属性
            ObjectDescribeDocument describeExtra = updateDescribeExtra(context.getUser(), arg.getDescribeAPIName(), arg.getDescribeExtra());
            stopWatch.lap("updateDescribeExtra");
            // 更新系统备注
            fieldBackgroundExtraLogicService.bulkUpsertFieldsExtra(context.getUser(), arg.getDescribeAPIName(), arg.getFieldsExtra());
            stopWatch.lap("bulkUpsertFieldsExtra");
            return UpdateCustomFieldDescribe.Result.builder()
                    .objectDescribe(ObjectDescribeDocument.of(objectDescribeDraft))
                    .describeExtra(describeExtra)
                    .fieldsExtra(arg.getFieldsExtra())
                    .build();
        } finally {
            redissonService.unlock(lock);
            stopWatch.logSlow(500);
        }
    }

    private ObjectDescribeDocument updateDescribeExtra(User user, String describeAPIName, ObjectDescribeDocument argDescribeExtra) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DESCRIBE_EXTRA_GRAY_EI, user.getTenantId()) || CollectionUtils.empty(argDescribeExtra)) {
            return null;
        }
        DescribeExtra describeExtra = describeLogicService.updateDescribeExtra(user, describeAPIName, argDescribeExtra);
        return ObjectDescribeDocument.of(describeExtra);
    }

    @ServiceMethod("find_roles_permission_list")
    public FindRolesPermissionList.Result findRolesPermissionList(FindRolesPermissionList.Arg arg, ServiceContext context) {
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
        List<FindRolesPermissionList.Role> roleList = null;

        if (BooleanUtils.isTrue(arg.getOutPermission())) {
            roleList = getOuterRoleInfoList(describe, arg.isIncludeRoles());
        } else {
            if (TeamMember.isTeamRoleGray(context.getTenantId())) {
                roleList = getCustomRoleInfoList(context.getTenantId(), describe, arg.isIncludeRoles());
            } else {
                roleList = getRoleInfoList(describe, arg.isIncludeRoles());
            }
        }
        // 从对象不能配置相关团队角色权限
        if (Objects.isNull(describe) || ObjectDescribeExt.of(describe).isSlaveObject()) {
            return FindRolesPermissionList.Result.builder()
                    .rolePermissionList(Lists.newArrayList())
                    .roles(roleList)
                    .enable(false)
                    .build();
        }

        boolean outPermission = BooleanUtils.isTrue(arg.getOutPermission());
        Boolean enableModify = whetherHavePermissionToModify(context.getUser(), arg.getDescribeApiName());
        List<IObjectDescribe> relatedDescribes = describeLogicService.findRelatedDescribes(context.getTenantId(), arg.getDescribeApiName());
        relatedDescribes = relatedDescribes.stream().filter(x -> !x.isBigObject()).collect(Collectors.toList());
        relatedDescribes.sort(Comparator.comparing(iObjectDescribe -> Optional.ofNullable(iObjectDescribe.getCreateTime()).orElse(0L)));
        List<FindRolesPermissionList.RolePermission> rolePermissionList = Lists.newArrayList();

        ManageGroup manageGroup = describeLogicService.queryObjectManageGroup(context.getUser(), arg.getSourceInfo());
        relatedDescribes.stream()
                .filter(x -> Objects.nonNull(x) && !ObjectDescribeExt.of(x).isSlaveObject())
                .filter(it -> ManageGroup.support(manageGroup, it.getApiName()))
                .forEach(v -> {
                    List<ObjectReferenceWrapper> referenceFieldDescribes = ObjectDescribeExt.of(v).getReferenceFieldDescribes().stream()
                            .filter(x -> arg.getDescribeApiName().equals(x.getTargetApiName()))
                            .filter(x -> !AppFrameworkConfig.isTeamMemberRoleBlackField(x.getIFieldDescribe()))
                            .filter(x -> x.isActive())
                            .collect(Collectors.toList());
                    referenceFieldDescribes.forEach(x -> {
                        String permission = getLookupRolesPermission(x, arg.getRoleType(), outPermission);
                        FindRolesPermissionList.RolePermission rolePermission = FindRolesPermissionList.RolePermission.builder()
                                .roleType(arg.getRoleType())
                                .enable(Objects.equals(permission, "read"))
                                .fieldApiName(x.getApiName())
                                .objectApiName(x.getDescribeApiName())
                                .objectDisplayName(v.getDisplayName())
                                .targetRelatedListLabel(referenceFieldDescribes.size() > 1 ? x.getTargetRelatedListLabel() : null)
                                .permissionType(permission)
                                .build();
                        rolePermissionList.add(rolePermission);
                    });
                });

        if (BooleanUtils.isNotTrue(arg.getOutPermission())) {
            fillFeedRolePermission(arg, rolePermissionList, context.getUser(), false);
        } else {
            fillFeedRolePermission(arg, rolePermissionList, context.getUser(), true);
        }

        List<FindRolesPermissionList.RolePermission> finalRolePermissionList = rolePermissionList;

        if (disablePublicObjectReferenceRelevantTeamPermission(context.getTenantId()) && ObjectListConfig.OBJECT_MANAGEMENT.equals(arg.getSourceInfo())) {
            finalRolePermissionList = skipPublicObjectRolePermission(context.getTenantId(), finalRolePermissionList);
        }

        return FindRolesPermissionList.Result.builder()
                .rolePermissionList(finalRolePermissionList)
                .roles(roleList)
                .enable(!Boolean.FALSE.equals(enableModify))
                .build();
    }

    private List<FindRolesPermissionList.RolePermission> skipPublicObjectRolePermission(String tenantId,
                                                                                        List<FindRolesPermissionList.RolePermission> rolePermissionList) {
        if (StringUtils.isBlank(tenantId) || CollectionUtils.empty(rolePermissionList)) {
            return rolePermissionList;
        }

        ObjectDescribeFinder objectDescribeFinder = ObjectDescribeFinder
                .builder()
                .user(User.systemUser(tenantId))
                .visibleScope(Sets.newHashSet(IObjectDescribe.PUBLIC_OBJECT_VISIBLE_SCOPE))
                .onlyVisibleScope(true)
                .build();

        List<IObjectDescribe> publicObjectList = describeLogicService.findObjectsByTenantId(objectDescribeFinder);

        if (CollectionUtils.empty(publicObjectList)) {
            return rolePermissionList;
        }

        Set<String> publicDescribeApiName = Sets.newHashSet();

        for (IObjectDescribe v : publicObjectList) {
            if (StringUtils.isBlank(v.getApiName())) {
                continue;
            }
            publicDescribeApiName.add(v.getApiName());
        }

        if (CollectionUtils.empty(publicDescribeApiName)) {
            return rolePermissionList;
        }

        List<FindRolesPermissionList.RolePermission> result = Lists.newArrayList();

        for (FindRolesPermissionList.RolePermission rolePermission : rolePermissionList) {
            if (StringUtils.isNotBlank(rolePermission.getObjectApiName()) && publicDescribeApiName.contains(rolePermission.getObjectApiName())) {
                continue;
            }
            result.add(rolePermission);
        }

        return result;
    }

    private List<FindRolesPermissionList.Role> getOuterRoleInfoList(IObjectDescribe describe, boolean includeRoles) {
        if (!includeRoles) {
            return Lists.newArrayList();
        }

        return getOutTeamMemberRole(describe.getApiName()).stream()
                .map(FindRolesPermissionList.Role::fromOutTeamMemberRole)
                .collect(Collectors.toList());
    }

    private List<TeamMember.OuterRole> getOutTeamMemberRole(String describeApiName) {
        if (AppFrameworkConfig.getOutTeamMemberRole().containsKey(describeApiName)) {
            return CollectionUtils.nullToEmpty(AppFrameworkConfig.getOutTeamMemberRole().get(describeApiName)).stream()
                    .map(TeamMember.OuterRole::of).collect(Collectors.toList());
        }
        return AppFrameworkConfig.getOutTeamMemberRole().getOrDefault(UDOBJ, Collections.emptyList()).stream()
                .map(TeamMember.OuterRole::of).collect(Collectors.toList());
    }

    private List<FindRolesPermissionList.Role> getRoleInfoList(IObjectDescribe describe, boolean includeRoles) {
        if (!includeRoles) {
            return Lists.newArrayList();
        }
        return getTeamMemberRole(describe.getApiName()).stream()
                .map(FindRolesPermissionList.Role::fromTeamMemberRole)
                .collect(Collectors.toList());
    }

    private List<FindRolesPermissionList.Role> getCustomRoleInfoList(String tenantId, IObjectDescribe describe, boolean includeRoles) {
        if (!includeRoles) {
            return Lists.newArrayList();
        }
        List<TeamRoleInfo> teamRoleInfoList = teamMemberRoleService.queryTeamRoleInfo(tenantId, describe.getApiName());
        if (teamRoleInfoList == null) {
            return Lists.newArrayList();
        }
        List<FindRolesPermissionList.Role> rolePermissionList = Lists.newArrayList();
        for (TeamRoleInfo v : teamRoleInfoList) {
            if (v.getStatus() == null || v.getStatus() != 1) {
                continue;
            }
            FindRolesPermissionList.Role rolePermission = new FindRolesPermissionList.Role();
            rolePermission.setType(v.getRoleType());
            rolePermission.setLabel(v.getRoleName());
            rolePermissionList.add(rolePermission);
        }
        return rolePermissionList;
    }

    private static List<TeamMember.Role> getTeamMemberRole(String describeApiName) {
        if (AppFrameworkConfig.getTeamMemberRole().containsKey(describeApiName)) {
            return CollectionUtils.nullToEmpty(AppFrameworkConfig.getTeamMemberRole().get(describeApiName)).stream()
                    .map(TeamMember.Role::of).collect(Collectors.toList());
        }
        return AppFrameworkConfig.getTeamMemberRole().getOrDefault(UDOBJ, Collections.emptyList()).stream()
                .map(TeamMember.Role::of).collect(Collectors.toList());
    }

    private void fillFeedRolePermission(FindRolesPermissionList.Arg arg, List<FindRolesPermissionList.RolePermission> rolePermissionList, User user, boolean isOut) {
        FeedPrivilegeInfo.Arg feedArg = FeedPrivilegeInfo.Arg
                .builder()
                .objectApiName(arg.getDescribeApiName())
                .dataRoleType(Integer.parseInt(arg.getRoleType()))
                .outPermission(isOut)
                .build();
        Map<String, String> headers = RestUtils.buildHeaders(user);
        FeedPrivilegeInfo.Result feedResult = feedPrivilegeProxy.getActivityDataPermission(headers, feedArg);
        if (Objects.isNull(feedResult) || Objects.isNull(feedResult.getData())) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.FOLLOW_UP_QUERY_EXCEPTION));
        }
        rolePermissionList.add(FindRolesPermissionList.RolePermission.builder()
                .fieldApiName("feed")
                .objectApiName("FeedObj")
                .objectDisplayName(I18N.text(I18NKey.FOLLOW_UP_DYNAMIC))
                .roleType(arg.getRoleType())
                .targetRelatedListLabel(I18N.text(I18NKey.FOLLOW_UP_DYNAMIC_TARGET))
                .enable(feedResult.getData().isEnable())
                .permissionType(feedResult.getData().isEnable() ? "read" : "disable")
                .build());
    }

    @ServiceMethod("save_role_permissions")
    public SavePermissionList.Result saveRolePermissions(SavePermissionList.Arg arg, ServiceContext context) {

        Set<String> objectApiNames = arg.getRolePermissionList().stream()
                .map(SavePermissionList.RolePermission::getObjectApiName).collect(Collectors.toSet());
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjectsIncludeMultiField(context.getTenantId(), objectApiNames);

        Map<String, List<IFieldDescribe>> fieldDescribeMap = Maps.newHashMap();
        List<FindFeedRolePermission.DataPermissionItem> dataPermissionItems = Lists.newArrayList();

        boolean outPermission = BooleanUtils.isTrue(arg.getOutPermission());
        arg.getRolePermissionList().forEach(x -> {

            if (fillDataPermissionItems(x, dataPermissionItems, arg.getDescribeApiName(), arg.getRoleType())) {
                return;
            }

            IFieldDescribe fieldDescribe = describeMap.get(x.getObjectApiName()).getFieldDescribe(x.getFieldAPiName());
            if (Objects.isNull(fieldDescribe) || !FieldDescribeExt.of(fieldDescribe).isLookupField()) {
                return;
            }
            ObjectReferenceWrapper objectReferenceWrapper = ObjectReferenceWrapper.of(fieldDescribe);
            List<String> lookupRoles = Optional.ofNullable(objectReferenceWrapper.getLookupRoles()).orElse(Lists.newArrayList());
            if (x.isEnableRead() || x.isEnableWrite()) {
                if (!containsPermission(outPermission, x.isEnableRead(), arg.getRoleType(), lookupRoles)) {
                    String rolePermissionKey = getRolePermissionKey(outPermission, x.isEnableRead(), arg.getRoleType());
                    lookupRoles.removeIf(roles -> Objects.equals(roles, arg.getRoleType()) || (outPermission ? roles.startsWith("1_" + arg.getRoleType()) : roles.startsWith("0_" + arg.getRoleType())));
                    lookupRoles.add(rolePermissionKey);
                }
            } else {
                removeALLPermission(arg.getRoleType(), outPermission, lookupRoles);
            }
            objectReferenceWrapper.setLookupRoles(lookupRoles);
            fieldDescribeMap.putIfAbsent(x.getObjectApiName(), Lists.newArrayList());
            fieldDescribeMap.get(x.getObjectApiName()).add(fieldDescribe);
        });
        describeLogicService.batchUpdateLookupRoles(context.getTenantId(), fieldDescribeMap);

        if (CollectionUtils.notEmpty(dataPermissionItems)) {
            crmRestService.setDataPermissionGroups(context.getUser(), dataPermissionItems);
        }

        return SavePermissionList.Result
                .builder()
                .success(true)
                .build();
    }

    @ServiceMethod("batch_save_role_permissions")
    public BatchSavePermissionList.Result batchSaveRolePermissions(BatchSavePermissionList.Arg arg, ServiceContext context) {
        BatchSavePermissionList.Result result = BatchSavePermissionList.Result.builder().success(false).enable(false).build();
        if (StringUtils.isEmpty(arg.getDescribeApiName()) || CollectionUtils.empty(arg.getPermissionList())) {
            return result;
        }

        //判断是否24h之内更新过
        Boolean enableModify = whetherHavePermissionToModify(context.getUser(), arg.getDescribeApiName());
        if (Boolean.FALSE.equals(enableModify)) {
            return result;
        }

        Set<String> objectApiNames = arg.getPermissionList().get(0).getRolePermissionList().stream()
                .map(SavePermissionList.RolePermission::getObjectApiName).collect(Collectors.toSet());
        IObjectDescribe objectDescribe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjectsIncludeMultiField(context.getTenantId(), objectApiNames);
        Map<String, List<IFieldDescribe>> fieldDescribeMap = Maps.newHashMap();
        Table<String, String, IFieldDescribe> fieldTable = HashBasedTable.create();

        Map<String, String> describeDisplayNameMap = describeMap.values().stream().collect(Collectors.toMap(IObjectDescribe::getApiName, IObjectDescribe::getDisplayName));
        Map<String, Set<String>> addPermissionRecords = Maps.newHashMap();
        Map<String, Set<String>> removePermissionRecords = Maps.newHashMap();


        boolean outPermission = BooleanUtils.isTrue(arg.getOutPermission());
        List<FeedPrivilegeInfo.Arg> feedPrivilegeInfos = new ArrayList<>();
        for (BatchSavePermissionList.Permission permission : arg.getPermissionList()) {
            for (SavePermissionList.RolePermission rolePermission : permission.getRolePermissionList()) {
                //Feed 特殊处理，不支持内外部，而且只读
                if ("FeedObj".equals(rolePermission.getObjectApiName())) {
                    FeedPrivilegeInfo.Arg feedPrivilegeInfo = FeedPrivilegeInfo.Arg
                            .builder()
                            .objectApiName(arg.getDescribeApiName())
                            .dataRoleType(Integer.parseInt(permission.getRoleType()))
                            .enable(rolePermission.isEnableRead())
                            .outPermission(outPermission)
                            .build();
                    feedPrivilegeInfos.add(feedPrivilegeInfo);
                    continue;
                }

                IFieldDescribe fieldDescribe = fieldTable.get(rolePermission.getObjectApiName(), rolePermission.getFieldAPiName());
                if (Objects.isNull(fieldDescribe)) {
                    IObjectDescribe describe = describeMap.get(rolePermission.getObjectApiName());
                    if (Objects.isNull(describe)) {
                        continue;
                    }
                    fieldDescribe = describe.getFieldDescribe(rolePermission.getFieldAPiName());
                }
                if (Objects.isNull(fieldDescribe) || !FieldDescribeExt.of(fieldDescribe).isLookupField()) {
                    continue;
                }
                ObjectReferenceWrapper objectReferenceWrapper = ObjectReferenceWrapper.of(fieldDescribe);
                List<String> lookupRoles = Optional.ofNullable(objectReferenceWrapper.getLookupRoles()).orElse(Lists.newArrayList());

                if (rolePermission.isEnableRead() || rolePermission.isEnableWrite()) {
                    if (!containsPermission(outPermission, rolePermission.isEnableRead(), permission.getRoleType(), lookupRoles)) {
                        lookupRoles.removeIf(roles -> Objects.equals(roles, permission.getRoleType()) || (outPermission ? roles.startsWith("1_" + permission.getRoleType()) : roles.startsWith("0_" + permission.getRoleType())));
                        lookupRoles.add(getRolePermissionKey(outPermission, rolePermission.isEnableRead(), permission.getRoleType()));
                        addPermissionRecords.putIfAbsent(permission.getRoleType(), Sets.newHashSet());
                        addPermissionRecords.get(permission.getRoleType()).add(describeDisplayNameMap.get(fieldDescribe.getDescribeApiName()));
                    }
                } else {
                    boolean remove = removeALLPermission(permission.getRoleType(), outPermission, lookupRoles);
                    if (remove) {
                        removePermissionRecords.putIfAbsent(permission.getRoleType(), Sets.newHashSet());
                        removePermissionRecords.get(permission.getRoleType()).add(describeDisplayNameMap.get(fieldDescribe.getDescribeApiName()));
                    }
                }
                objectReferenceWrapper.setLookupRoles(lookupRoles);
                fieldTable.put(rolePermission.getObjectApiName(), rolePermission.getFieldAPiName(), fieldDescribe);
            }
        }

        boolean enable = true;
        if (CollectionUtils.notEmpty(fieldTable.values())) {
            for (String describeApiName : fieldTable.rowKeySet()) {
                Collection<IFieldDescribe> fieldDescribes = fieldTable.row(describeApiName).values();
                if (CollectionUtils.notEmpty(fieldDescribes)) {
                    fieldDescribeMap.put(describeApiName, Lists.newArrayList(fieldDescribes));
                }
            }
            describeLogicService.batchUpdateLookupRoles(context.getTenantId(), fieldDescribeMap);

            //记录修改日志
            logService.logRelatedTeamDataPermission(context.getUser(), objectDescribe, addPermissionRecords, removePermissionRecords);

            if (enableModify == null) {
                enable = false;
                configService.createTenantConfig(context.getUser(), KEY + arg.getDescribeApiName(),
                        String.valueOf(System.currentTimeMillis()), ConfigValueType.STRING);
            } else {
                enable = false;
                configService.updateTenantConfig(context.getUser(), KEY + arg.getDescribeApiName(),
                        String.valueOf(System.currentTimeMillis()), ConfigValueType.STRING);
            }
        }

        if (ObjectUtils.isNotEmpty(feedPrivilegeInfos)) {
            Map<String, String> headers = RestUtils.buildHeaders(context.getUser());
            for (FeedPrivilegeInfo.Arg feedArg : feedPrivilegeInfos) {
                feedPrivilegeProxy.upsertActivityDataPermission(headers, feedArg);
            }
        }

        result.setSuccess(true);
        result.setEnable(enable);
        return result;
    }

    @ServiceMethod("batch_save_relevant_team_permission")
    public BatchSavePermissionList.Result batchSaveRelevantTeamPermission(BatchSavePermissionList.Arg arg, ServiceContext context) {
        BatchSavePermissionList.Result result = BatchSavePermissionList.Result.builder().success(false).enable(false).build();
        if (StringUtils.isBlank(arg.getDescribeApiName()) || CollectionUtils.empty(arg.getPermissionList())) {
            return result;
        }

        IObjectDescribe objectDescribe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());

        Set<String> currentDescribeRoleType = findTeamRoleTypeByTenantIdAndDescribe(context.getTenantId(), objectDescribe, arg.getOutPermission());

        if (CollectionUtils.empty(currentDescribeRoleType)) {
            return result;
        }

        Set<String> objectApiNames = Sets.newHashSet();

        for (BatchSavePermissionList.Permission currentPermission : arg.getPermissionList()) {
            if (StringUtils.isBlank(currentPermission.getRoleType()) || CollectionUtils.empty(currentPermission.getRolePermissionList())) {
                return result;
            }

            if (!currentDescribeRoleType.contains(currentPermission.getRoleType())) {
                return result;
            }

            for (SavePermissionList.RolePermission currentRolePermission : currentPermission.getRolePermissionList()) {
                if (StringUtils.isAnyBlank(currentRolePermission.getObjectApiName(), currentRolePermission.getObjectDisplayName(), currentRolePermission.getFieldAPiName())) {
                    return result;
                }
                if (!"write".equals(currentRolePermission.getType()) && !"read".equals(currentRolePermission.getType()) && !"disable".equals(currentRolePermission.getType())) {
                    return result;
                }
                objectApiNames.add(currentRolePermission.getObjectApiName());
            }
        }

        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjectsIncludeMultiField(context.getTenantId(), objectApiNames);
        Map<String, List<IFieldDescribe>> fieldDescribeMap = Maps.newHashMap();
        Table<String, String, IFieldDescribe> fieldTable = HashBasedTable.create();

        Map<String, String> describeDisplayNameMap = describeMap
                .values()
                .stream()
                .collect(Collectors.toMap(IObjectDescribe::getApiName, IObjectDescribe::getDisplayName));
        Map<String, Set<String>> addPermissionRecords = Maps.newHashMap();
        Map<String, Set<String>> removePermissionRecords = Maps.newHashMap();


        boolean outPermission = BooleanUtils.isTrue(arg.getOutPermission());
        List<FeedPrivilegeInfo.Arg> feedPrivilegeInfos = new ArrayList<>();
        for (BatchSavePermissionList.Permission permission : arg.getPermissionList()) {
            for (SavePermissionList.RolePermission rolePermission : permission.getRolePermissionList()) {
                //Feed 特殊处理，不支持内外部，而且只读
                if ("FeedObj".equals(rolePermission.getObjectApiName())) {
                    FeedPrivilegeInfo.Arg feedPrivilegeInfo = FeedPrivilegeInfo.Arg
                            .builder()
                            .objectApiName(arg.getDescribeApiName())
                            .dataRoleType(Integer.parseInt(permission.getRoleType()))
                            .enable(rolePermission.isEnableRead())
                            .outPermission(outPermission)
                            .build();
                    feedPrivilegeInfos.add(feedPrivilegeInfo);
                    continue;
                }

                IFieldDescribe fieldDescribe = fieldTable.get(rolePermission.getObjectApiName(), rolePermission.getFieldAPiName());
                if (Objects.isNull(fieldDescribe)) {
                    IObjectDescribe describe = describeMap.get(rolePermission.getObjectApiName());
                    if (Objects.isNull(describe)) {
                        continue;
                    }
                    fieldDescribe = describe.getFieldDescribe(rolePermission.getFieldAPiName());
                }
                if (Objects.isNull(fieldDescribe) || !FieldDescribeExt.of(fieldDescribe).isLookupField()) {
                    continue;
                }
                ObjectReferenceWrapper objectReferenceWrapper = ObjectReferenceWrapper.of(fieldDescribe);
                List<String> lookupRoles = Optional.ofNullable(objectReferenceWrapper.getLookupRoles()).orElse(Lists.newArrayList());

                if (rolePermission.isEnableRead() || rolePermission.isEnableWrite()) {
                    if (!containsPermission(outPermission, rolePermission.isEnableRead(), permission.getRoleType(), lookupRoles)) {
                        lookupRoles.removeIf(roles -> Objects.equals(roles, permission.getRoleType()) || (outPermission ? roles.startsWith("1_" + permission.getRoleType()) : roles.startsWith("0_" + permission.getRoleType())));
                        lookupRoles.add(getRolePermissionKey(outPermission, rolePermission.isEnableRead(), permission.getRoleType()));
                        addPermissionRecords.putIfAbsent(permission.getRoleType(), Sets.newHashSet());
                        addPermissionRecords.get(permission.getRoleType()).add(describeDisplayNameMap.get(fieldDescribe.getDescribeApiName()));
                    }
                } else {
                    boolean remove = removeALLPermission(permission.getRoleType(), outPermission, lookupRoles);
                    if (remove) {
                        removePermissionRecords.putIfAbsent(permission.getRoleType(), Sets.newHashSet());
                        removePermissionRecords.get(permission.getRoleType()).add(describeDisplayNameMap.get(fieldDescribe.getDescribeApiName()));
                    }
                }
                objectReferenceWrapper.setLookupRoles(lookupRoles);
                fieldTable.put(rolePermission.getObjectApiName(), rolePermission.getFieldAPiName(), fieldDescribe);
            }
        }

        boolean enable = true;
        if (CollectionUtils.notEmpty(fieldTable.values())) {
            for (String describeApiName : fieldTable.rowKeySet()) {
                Collection<IFieldDescribe> fieldDescribes = fieldTable.row(describeApiName).values();
                if (CollectionUtils.notEmpty(fieldDescribes)) {
                    fieldDescribeMap.put(describeApiName, Lists.newArrayList(fieldDescribes));
                }
            }
            describeLogicService.batchUpdateLookupRoles(context.getTenantId(), fieldDescribeMap);

            //记录修改日志
            logService.logRelatedTeamDataPermission(context.getUser(), objectDescribe, addPermissionRecords, removePermissionRecords);

        }

        if (ObjectUtils.isNotEmpty(feedPrivilegeInfos)) {
            Map<String, String> headers = RestUtils.buildHeaders(context.getUser());
            for (FeedPrivilegeInfo.Arg feedArg : feedPrivilegeInfos) {
                feedPrivilegeProxy.upsertActivityDataPermission(headers, feedArg);
            }
        }

        log.info("batch_save_relevant_team_permission, tenantId:{}, arg:{}", context.getTenantId(), JSON.toJSONString(arg));

        result.setSuccess(true);
        result.setEnable(enable);
        return result;
    }

    private Set<String> findTeamRoleTypeByTenantIdAndDescribe(String tenantId, IObjectDescribe objectDescribe, Boolean isOutPermission) {
        // 从对象不能配置相关团队角色权限
        if (StringUtils.isBlank(tenantId) || Objects.isNull(objectDescribe) || ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            return Sets.newHashSet();
        }

        List<FindRolesPermissionList.Role> roleList = null;

        if (BooleanUtils.isTrue(isOutPermission)) {
            roleList = getOuterRoleInfoList(objectDescribe, true);
        } else {
            if (TeamMember.isTeamRoleGray(tenantId)) {
                roleList = getCustomRoleInfoList(tenantId, objectDescribe, true);
            } else {
                roleList = getRoleInfoList(objectDescribe, true);
            }
        }

        if (CollectionUtils.empty(roleList)) {
            return Sets.newHashSet();
        }

        Set<String> result = Sets.newHashSet();

        for (FindRolesPermissionList.Role v : roleList) {
            if (StringUtils.isBlank(v.getType())) {
                continue;
            }
            result.add(v.getType());
        }

        return result;
    }

    private Boolean whetherHavePermissionToModify(User user, String describeApiName) {
        String config = configService.findTenantConfig(user, KEY + describeApiName);
        if (StringUtils.isNotEmpty(config)) {
            if (AppFrameworkConfig.isModifyRelatedTeamDataPermissionWhiteTenant(user.getTenantId())) {
                return true;
            }
            Long value = Long.valueOf(config);
            if (System.currentTimeMillis() - value > 24 * 60 * 60 * 1000) {
                return true;
            }
            return false;
        }
        return null;
    }

    private boolean fillDataPermissionItems(SavePermissionList.RolePermission rolePermission, List<FindFeedRolePermission.DataPermissionItem> dataPermissionItems,
                                            String describeApiName, String roleType) {
        if (!"FeedObj".equals(rolePermission.getObjectApiName()) || Objects.isNull(FeedRolePermission.of(describeApiName, roleType))) {
            return false;
        }
        dataPermissionItems.add(FindFeedRolePermission.DataPermissionItem.builder()
                .dataDisplayName(rolePermission.getTargetRelatedListLabel())
                .dataKey(FeedRolePermission.of(describeApiName, roleType).getDataKey())
                .dataRoleID(FeedRolePermission.of(describeApiName, roleType).getPermissionRole())
                .isChecked(rolePermission.isEnable())
                .isEditable(true)
                .build());
        return true;
    }

    @ServiceMethod("setDataPermissionItemList")
    public SetDataPermissionItemList.Result setDataPermissionItemList(SetDataPermissionItemList.Arg arg, ServiceContext context) {
        if (CollectionUtils.empty(arg.getDataPermissionItemList())) {
            return null;
        }
        updateLookupRolesList(arg.getDataPermissionItemList(), context);

        return SetDataPermissionItemList.Result.builder()
                .dataPermissionGroupInfos(getDataPermissionItems(context).getDataPermissionGroupInfos())
                .success(true)
                .build();
    }

    private void updateLookupRolesList(List<SetDataPermissionItemList.DataPermissionItems> dataPermissionItemList, ServiceContext context) {
        Set<String> apiNames = dataPermissionItemList.stream().map(SetDataPermissionItemList.DataPermissionItems::getObjectApiName).collect(Collectors.toSet());
        Map<String, IObjectDescribe> objectDescribeMap = describeLogicService.findObjectsIncludeMultiField(context.getTenantId(), apiNames);
        Map<String, List<IFieldDescribe>> fieldDescribeMap = Maps.newHashMap();
        dataPermissionItemList.forEach(dataPermissionItems -> {
            IObjectDescribe describe = objectDescribeMap.get(dataPermissionItems.getObjectApiName());
            IFieldDescribe fieldDescribe = dealLookupRoles(describe, dataPermissionItems);
            if (Objects.isNull(fieldDescribe)) {
                return;
            }
            List<IFieldDescribe> fieldDescribeList = fieldDescribeMap.getOrDefault(describe.getApiName(), Lists.newArrayList());
            fieldDescribeList.add(fieldDescribe);
            fieldDescribeMap.put(describe.getApiName(), fieldDescribeList);
        });
        describeLogicService.batchUpdateLookupRoles(context.getTenantId(), fieldDescribeMap);
    }

    private IFieldDescribe dealLookupRoles(IObjectDescribe objectDescribe, SetDataPermissionItemList.DataPermissionItems dataPermissionItems) {
        ObjectReferenceWrapper referenceField = ObjectDescribeExt.of(objectDescribe).getRefFieldAndHaveLookupRolesByApiName(dataPermissionItems.getFieldApiName());
        if (Objects.isNull(referenceField)) {
            return null;
        }
        List<String> lookupRoles = referenceField.getLookupRoles();
        dataPermissionItems.getDataPermissionItems().forEach(dataPermissionItem -> {
            if (dataPermissionItem.getIsChecked()) {
                if (!lookupRoles.contains(dataPermissionItem.getRoleType())) {
                    lookupRoles.add(dataPermissionItem.getRoleType());
                }
            } else {
                lookupRoles.remove(dataPermissionItem.getRoleType());
            }
        });
        referenceField.setLookupRoles(lookupRoles);
        return referenceField.getIFieldDescribe();
    }

    @ServiceMethod("setDataPermissionItems")
    public SetDataPermissionItems.Result setDataPermissionItems(SetDataPermissionItems.Arg arg, ServiceContext context) {
        boolean success = describeLogicService.updateLookupRoles(context.getTenantId(), arg.getObjectApiName(), arg.getFieldApiName(), arg.getIsChecked(), arg.getRoleType());
        return SetDataPermissionItems.Result.builder()
                .success(success)
                .build();
    }

    @ServiceMethod("getDataPermissionItems")
    public GetDataPermissionItems.Result getDataPermissionItems(ServiceContext context) {

        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjectsIncludeMultiField(context.getTenantId(), RelationMemberDataPermission.apiNames);
        List<GetDataPermissionItems.DataPermissionItemInfo> dataPermissionItemInfos = Lists.newArrayList();
        RelationMemberDataPermission.objectRelatedList.forEach(objectRelated -> {
            if (Objects.isNull(describeMap.get(objectRelated.getObjectApiName()))) {
                return;
            }
            IFieldDescribe fieldDescribe = describeMap.get(objectRelated.getObjectApiName()).getFieldDescribe(objectRelated.getFiledAPiName());
            if (fieldDescribe instanceof IObjectReferenceField) {
                List<String> lookupRoles = ((IObjectReferenceField) fieldDescribe).getLookupRoles();
                lookupRoles = Objects.isNull(lookupRoles) ? Lists.newArrayList() : lookupRoles;
                List<String> finalLookupRoles = lookupRoles;
                objectRelated.getRoleType().forEach(role -> {
                    GetDataPermissionItems.DataPermissionItemInfo permissionItemInfo = GetDataPermissionItems.DataPermissionItemInfo.builder()
                            .dataDisplayName(I18N.text(objectRelated.getDisplayName(),
                                    I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(objectRelated.getTargetObjectApiName())),
                                    I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(objectRelated.getObjectApiName()))))
                            .isEditable(true)
                            .fieldApiName(objectRelated.getFiledAPiName())
                            .objectApiName(objectRelated.getObjectApiName())
                            .targetApiName(objectRelated.getTargetObjectApiName())
                            .roleType(role)
                            .dataKey(String.format("%s_%s_%s", objectRelated.getObjectApiName(), objectRelated.getFiledAPiName(), role))
                            .build();
                    permissionItemInfo.setIsChecked(finalLookupRoles.contains(role));
                    dataPermissionItemInfos.add(permissionItemInfo);
                });
            }
        });

        List<GetDataPermissionItems.DataPermissionGroupInfos> dataPermissionGroupInfos = Lists.newArrayList();

        Map<String, List<GetDataPermissionItems.DataPermissionItemInfo>> map = Maps.newHashMap();
        List<String> keys = Lists.newArrayList(
                I18N.text(I18NKey.OF, I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(Utils.ACCOUNT_API_NAME)), I18N.text(I18NKey.constant_owner)),
                I18N.text(I18NKey.OF, I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(Utils.ACCOUNT_API_NAME)), I18N.text(I18NKey.SALES_TEAMMATE)),
                I18N.text(I18NKey.OF, I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(Utils.ACCOUNT_API_NAME)), I18N.text(I18NKey.AFTER_SALES_PERSONNEL)),
                I18N.text(I18NKey.OF, I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(Utils.OPPORTUNITY_API_NAME)), I18N.text(I18NKey.SALES_TEAMMATE)),
                I18N.text(I18NKey.OF, I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(Utils.OPPORTUNITY_API_NAME)), I18N.text(I18NKey.AFTER_SALES_PERSONNEL)));
        dataPermissionItemInfos.forEach(x -> {
            if (Utils.ACCOUNT_API_NAME.equals(x.getTargetApiName()) && "1".equals(x.getRoleType())) {
                String key = keys.get(0);
                List<GetDataPermissionItems.DataPermissionItemInfo> list = map.getOrDefault(key, Lists.newArrayList());
                list.add(x);
                map.put(key, list);
            }
            if (Utils.ACCOUNT_API_NAME.equals(x.getTargetApiName()) && "2".equals(x.getRoleType())) {
                String key = keys.get(1);
                List<GetDataPermissionItems.DataPermissionItemInfo> list = map.getOrDefault(key, Lists.newArrayList());
                list.add(x);
                map.put(key, list);
            }
            if (Utils.ACCOUNT_API_NAME.equals(x.getTargetApiName()) && "3".equals(x.getRoleType())) {
                String key = keys.get(2);
                List<GetDataPermissionItems.DataPermissionItemInfo> list = map.getOrDefault(key, Lists.newArrayList());
                list.add(x);
                map.put(key, list);
            }
            if (Utils.OPPORTUNITY_API_NAME.equals(x.getTargetApiName()) && "2".equals(x.getRoleType())) {
                String key = keys.get(3);
                List<GetDataPermissionItems.DataPermissionItemInfo> list = map.getOrDefault(key, Lists.newArrayList());
                list.add(x);
                map.put(key, list);
            }
            if (Utils.OPPORTUNITY_API_NAME.equals(x.getTargetApiName()) && "3".equals(x.getRoleType())) {
                String key = keys.get(4);
                List<GetDataPermissionItems.DataPermissionItemInfo> list = map.getOrDefault(key, Lists.newArrayList());
                list.add(x);
                map.put(key, list);
            }
        });

        keys.forEach(x -> dataPermissionGroupInfos.add(GetDataPermissionItems.DataPermissionGroupInfos.builder()
                .dataRoleName(x)
                .dataPermissionItemInfos(map.get(x))
                .build()));

        return GetDataPermissionItems.Result.builder()
                .dataPermissionGroupInfos(dataPermissionGroupInfos)
                .build();
    }

    private void validateCascadeParent(String fieldDescribeJson, ServiceContext context,
                                       List<FieldLayoutPojo> fieldLayoutPojoList, String describeAPIName) {
        if (Strings.isNullOrEmpty(fieldDescribeJson)) {
            return;
        }
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldDescribeJson);
        // 单选、多选类型需要校验 级联关系
        if (!FieldDescribeExt.CASCADE_CHILD_API_NAME_FIELD_TYPE.contains(fieldDescribe.getType())) {
            return;
        }
        String cascadeParentApiName = fieldDescribe.get("cascade_parent_api_name", String.class);
        if (Strings.isNullOrEmpty(cascadeParentApiName)) {
            return;
        }
        List<String> buttonApiNameList = fieldLayoutPojoList.stream()
                .filter(FieldLayoutPojo::isShow)
                .map(FieldLayoutPojo::getApiName)
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(buttonApiNameList)) {
            FieldResult cascadeParentFieldResult = describeLogicService.findCustomFieldDescribe(context.getTenantId(),
                    describeAPIName, cascadeParentApiName);
            List<FieldLayoutPojo> cascadeParentFieldLayoutList = cascadeParentFieldResult.getLayout_list();
            Map<String, FieldLayoutPojo> cascadeParentLayoutMap = cascadeParentFieldLayoutList.stream()
                    .collect(Collectors.toMap(x -> x.getApiName(), x -> x));
            buttonApiNameList.stream().filter(x -> !cascadeParentLayoutMap.get(x).isShow()).findFirst().ifPresent(x -> {
                throw new ValidateException(I18N.text(I18NKey.FIELD_DEPEND_RELATED_PLEASE_ADD_DEPEND_FIELD,
                        cascadeParentFieldResult.getField().getLabel(), cascadeParentLayoutMap.get(x).getLabel()));
            });
        }
    }

    @ServiceMethod("findDescribeAndLayout")
    public FindDescribeAndLayout.Result findDescribeAndLayout(FindDescribeAndLayout.Arg arg, ServiceContext context) {
        DescribeResult describeResult = describeLogicService.findDescribeAndLayout(
                context.getUser(),
                arg.getDescribe_apiname(),
                arg.isInclude_layout(),
                arg.getLayout_apiname());

        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describeResult.getObjectDescribe());
        describeExt.descriptionNullToEmpty();
        describeExt.fillDefaultShowDetailButton();

        ILayout layout = describeResult.getLayout();
        if (layout != null) {
            LayoutExt layoutExt = LayoutExt.of(layout);
            layoutExt.layoutDescriptionNullToEmpty();

            LayoutStructure.restoreLayout(layoutExt, PageType.Designer);
            handleLayoutButtons(context, describeExt, layoutExt);

            //查询关联对象和从对象
            List<IObjectDescribe> relatedDescribes = describeLogicService.findRelatedDescribes(context.getTenantId(), describeExt.getApiName());
            List<RelatedObjectDescribeStructure> detailObjects = describeExt.getDetailObjectDescribeStructures(relatedDescribes);
            List<RelatedObjectDescribeStructure> lookupObjects = describeExt.getRelatedObjectDescribeStructures(relatedDescribes);
            //根据配置中心的配置去掉不展示的关联对象
            lookupObjects.removeIf(x -> DefObjConstants.isReferenceObjectInvisible(describeExt.getApiName(), x.getRelatedObjectDescribe().getApiName()));

            //顶部信息
            handleTopInfo(describeExt, layoutExt, context.getUser());

            List<IComponent> components = Lists.newArrayList();

            //详细信息
            components.addAll(layoutExt.getComponentsSilently());

            //摘要
            IComponent saleLogComponent = RelatedObjectGroupComponentBuilder.builder().build().buildRecordComponentWithoutFieldsAndButtons(arg.getDescribe_apiname());
            if (!layoutExt.containsComponent(saleLogComponent.getName())) {
                components.add(saleLogComponent);
            }

            //相关团队
            IComponent teamComponent = TeamComponentBuilder.builder().build().buildUserListComponentWithoutFieldsAndButtons();
            if (!layoutExt.containsComponent(teamComponent.getName())) {
                components.add(teamComponent);
            }

            //从对象
            List<IComponent> detailComponentList = MasterDetailGroupComponentBuilder.builder()
                    .detailObjectsDescribeList(detailObjects).build().getComponentListForDesigner();
            List<String> detailComponentNames = detailComponentList.stream().map(x -> x.getName()).collect(Collectors.toList());
            List<String> oldDetailComponentNames = layoutExt.getMasterDetailComponentNames();
            components.removeIf(x -> oldDetailComponentNames.contains(x.getName()) && !detailComponentNames.contains(x.getName()));
            detailComponentList.stream().filter(x -> !layoutExt.containsComponent(x.getName())).forEach(x -> components.add(x));

            //同步从对象的列表名称到component的header
            detailComponentList.stream().forEach(x -> {
                components.stream().filter(y -> x.getName().equals(y.getName())).forEach(y -> y.setHeader(x.getHeader()));
            });

            //关联对象
            List<IComponent> relateComponentList = RelatedObjectGroupComponentBuilder.builder().objectDescribe(describeExt)
                    .user(context.getUser())
                    .relatedObjectDescribeList(lookupObjects).build().getComponentListForDesigner();
            List<String> relatedComponentNames = relateComponentList.stream().map(x -> x.getName()).collect(Collectors.toList());
            List<String> oldRelatedComponentNames = layoutExt.getRelatedListComponentNames();
            components.removeIf(x -> oldRelatedComponentNames.contains(x.getName()) && !relatedComponentNames.contains(x.getName()));
            relateComponentList.stream().filter(x -> !layoutExt.containsComponent(x.getName())).forEach(x -> components.add(x));

            //同步相关对象的列表名称到component的header
            relateComponentList.stream().filter(x -> x instanceof RelatedObjectList).forEach(x -> {
                components.stream().filter(y -> x.getName().equals(y.getName())).forEach(y -> y.setHeader(x.getHeader()));
            });

            //修改记录
            IComponent modifyLogComponent = LayoutExt.buildModifyRecordComponent();
            if (!layoutExt.containsComponent(modifyLogComponent.getName())) {
                components.add(modifyLogComponent);
            }
            //使用新版附件替换旧版附件
            LayoutComponents.replaceOldAttachComponent(layoutExt, components);

            //根据配置过滤组件
            components.removeIf(x -> DefObjConstants.isComponentInvisible(arg.getDescribe_apiname(), x.getName()));
            Map<String, Boolean> stringBooleanMap = serviceFacade.existModule(context.getTenantId(), Sets.newHashSet(LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP));
            Boolean existMultiLanguage = stringBooleanMap.get(LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP);
            //根据i18nKey重置component的header
            ComponentHeaderSetter.builder()
                    .tenantId(context.getTenantId())
                    .layoutType(LayoutTypes.DETAIL)
                    .layoutVersion(LayoutVersion.V1)
                    .components(components)
                    .objectApiName(describeExt.getApiName())
                    .layoutApiName(layoutExt.getName())
                    .existMultiLanguage(existMultiLanguage)
                    .componentPreKeyMap(serviceFacade.getLayoutLogicService().findComponentPreKeys(layoutExt.getComponentsSilently()))
                    .build()
                    .reset();

            layoutExt.fillHiddenComponentInfo(components);
            layoutExt.setComponentOrder(components);
            layoutExt.setComponents(components);

            // 补充UI事件中的函数信息
            List<Map<String, Object>> events = handleUIEventFunction(context, describeExt, layoutExt);
            layoutExt.setEvents(events);
        }
        return FindDescribeAndLayout.Result.builder()
                .objectDescribe(ObjectDescribeDocument.of(describeResult.getObjectDescribe()))
                .layout(LayoutDocument.of((describeResult.getLayout())))
                .build();

    }

    /*
     * 新增自定义按钮存入布局，默认排最后
     */
    private void fillLayoutWithNewButtons(LayoutExt layoutExt, ServiceContext context, ObjectDescribeExt describeExt) {
        //查找自定义按钮
        List<IUdefButton> iButtons = findButtonsByUsePage(describeExt, context.getUser(), ButtonUsePageType.Detail);
        //过滤自定义按钮,更新自定义按钮激活状态
        List<IButton> customButtons = ButtonOrder.filteringUdefButton(iButtons);
        //更新布局中自定义按钮激活状态和名称
        ButtonOrder.updateCustomButtonWithLayout(customButtons, layoutExt);
        //无重复并集
        ButtonOrder.backhanderNewCustomButtons(layoutExt, customButtons);
        //追加新增预定义按钮
        ButtonOrder.backhanderNewPredefinedButtons(layoutExt, describeExt);
        // 过滤被被隐藏的按钮
        filterHiddenButtons(layoutExt);

    }

    private void filterHiddenButtons(LayoutExt layoutExt) {
        List<String> hiddenButtons = layoutExt.getHiddenButtons();
        if (CollectionUtils.empty(hiddenButtons)) {
            return;
        }
        List<IButton> buttonOrder = layoutExt.getButtonOrder();
        buttonOrder.removeIf(it -> hiddenButtons.contains(it.getName()));
        layoutExt.setButtonOrder(buttonOrder);
    }

    @ServiceMethod("findDraftAndLayout")
    public FindDraftAndLayout.Result findDraftAndLayout(FindDraftAndLayout.Arg arg, ServiceContext context) {

        DescribeResult describeResult = describeLogicService.findDescribeAndLayout(
                context.getUser(),
                arg.getDraft_apiname(),
                arg.isInclude_layout(),
                arg.getLayout_apiname());

        return FindDraftAndLayout.Result.builder()
                .objectDescribeDraft(ObjectDescribeDocument.of(describeResult.getObjectDescribe()))
                .layout(LayoutDocument.of((describeResult.getLayout())))
                .build();

    }

    @ServiceMethod("findIconList")
    public FindIconList.Result findIconList(FindIconList.Arg arg, ServiceContext context) {

        return FindIconList.Result.builder()
                .iconList(Lists.newArrayList())
                .build();
//        List<IconExt> result = describeLogicService.findIconList();
//        return FindIconList.Result.builder()
//                .iconList(IconDocument.ofList(result))
//                .build();
    }

    @ServiceMethod("ifShowRelatedObjs")
    public Boolean ifShowRelatedObjs(CheckNeedShowRelatedObjs.Arg arg, ServiceContext
            context) {

        return layoutLogicService.checkNeedShowRelatedObjs(
                context.getUser(),
                arg.getObjectDataId(),
                arg.getObjectDescribeApiName(),
                Lists.newArrayList()
        );
    }

    @ServiceMethod("deleteDescribeCustomField")
    public DeleteCustomField.Result deleteDescribeCustomField(DeleteCustomField.Arg arg, ServiceContext context) {
        RLock lock = describeLogicService.tryLockObject(context.getTenantId(), arg.getDescribeAPIName());
        try {
            IObjectDescribe objectDescribe = describeLogicService.deleteCustomField(
                    context.getUser(),
                    arg.getDescribeAPIName(),
                    arg.getField_api_name());

            return DeleteCustomField.Result.builder()
                    .objectDescribe(ObjectDescribeDocument.of(objectDescribe))
                    .build();
        } finally {
            redissonService.unlock(lock);
        }
    }

    @ServiceMethod("deleteDescribe")
    public DeleteDescribe.Result deleteDescribe(DeleteDescribe.Arg arg, ServiceContext context) {
        String apiName = null == arg.getDescribe_apiname() ? arg.getDraft_apiname() : arg.getDescribe_apiname();
        IObjectDescribe objectDescribe = describeLogicService.deleteDescribe(context.getUser(), apiName);

        return DeleteDescribe.Result.builder()
                .objectDescribe(ObjectDescribeDocument.of(objectDescribe))
                .build();
    }

    @ServiceMethod("checkCountLimit")
    public CheckCountLimit.Result checkCountLimit(CheckCountLimit.Arg arg, ServiceContext context) {
        CheckCountLimit.Result result = new CheckCountLimit.Result();
        if (Strings.isNullOrEmpty(arg.getCheckType()) || (!"describe".equals(arg.getCheckType()) && Strings.isNullOrEmpty(arg.getObjectDescribeApiName()))) {
            log.warn("parameter is null in checkCountLimit,tenantId:{}, userId:{}, arg:{}",
                    context.getTenantId(), context.getUser().getUserId(), arg);
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        log.debug("Entering checkCountLimit(describeApiName={},checkType={});tenantId:{}, userId:{}", arg
                .getObjectDescribeApiName(), arg.getCheckType(), context.getTenantId(), context.getUser().getUserId());
        String res = metaDataService.checkCountLimit(context.getUser(), arg.getCheckType(), arg
                .getObjectDescribeApiName());
        result.setValue(res);
        return result;
    }

    @ServiceMethod("createFieldListConfig")
    public CreateFieldListConfig.Result createFieldListConfig(CreateFieldListConfig.Arg arg, ServiceContext context) {
        // 处理新参数filter_fields_new
        CommonFilterField filterField = null;
        if (CollectionUtils.notEmpty(arg.getFilterFieldsNew())) {
            filterField = CommonFilterField.builder()
                    .fieldListNew(arg.getFilterFieldsNew())
                    .build();
        } else if (CollectionUtils.notEmpty(arg.getFilterFields())) {
            // 兼容老参数
            filterField = CommonFilterField.builder()
                    .fieldList(arg.getFilterFields())
                    .build();
        }

        String extendAttribute = arg.getExtendAttribute();
        String whatApiName = arg.getWhatApiName();
        String sessionKey = arg.getSessionKey();
        if (!Strings.isNullOrEmpty(whatApiName) && !Strings.isNullOrEmpty(sessionKey)) {
            extendAttribute = String.format("%s_%s", whatApiName, sessionKey);
            whatApiName = null;
        }
        // 调用服务实现
        IFieldListConfig fieldListConfig = dataListHeaderConfigService.createFieldListConfig(
                context.getUser(),
                arg.getObjectDescribeApiName(),
                arg.getFieldList(),
                whatApiName,
                extendAttribute,
                filterField,
                arg.getCovertTopListFilter()
        );

        return CreateFieldListConfig.Result.builder()
                .value(fieldListConfig.toJsonString())
                .build();
    }

    @ServiceMethod("findFieldListConfig")
    public FindFieldListConfig.Result findFieldListConfig(FindFieldListConfig.Arg arg, ServiceContext context) {
        List<Map<String, Object>> fieldListConfig = dataListHeaderConfigService.findFieldListConfig(
                context.getUser(),
                arg.getObjectDescribeApiName(),
                arg.getExtendAttribute());

        return FindFieldListConfig.Result.builder()
                .list(fieldListConfig)
                .build();
    }

    @ServiceMethod("findRelatedObjectList")
    public FindRelatedObjectList.Result findRelatedObjectList(FindRelatedObjectList.Arg arg, ServiceContext context) {
        FindRelatedObjectList.Result.ResultBuilder builder = FindRelatedObjectList.Result.builder();

        List<String> unusableProductApiNames = ProductUtil.findUnusableProductApiNames(context.getTenantId());
        boolean excludeBigObject = BooleanUtils.isFalse(arg.getIncludeBigObject());
        boolean excludeSocialObject = BooleanUtils.isFalse(arg.getIncludeSocialObject());
        boolean simplyDescribe = arg.isSimplyDescribe();
        if (arg.isIncludeRefList()) {
            List<IObjectDescribe> lookupDescribes = describeLogicService.findLookupDescribes(
                    context.getTenantId(), arg.getDescribeApiName(), Objects.equals(arg.getExcludeInvalid(), Boolean.TRUE));
            removeUnSupportDescribe(lookupDescribes, unusableProductApiNames, excludeBigObject, excludeSocialObject);
            List<IObjectDescribe> describeList = processRelatedListForm(lookupDescribes, context.getTenantId(), arg.getDescribeApiName(), arg.getRenderType());
            simplyDescribe(simplyDescribe, describeList);
            builder.lookupDescribeList(ObjectDescribeDocument.ofList(describeList));
        }

        if (arg.isIncludeRefManyList()) {
            List<IObjectDescribe> lookupManyDescribes = describeLogicService.findLookupManyDescribes(context.getTenantId(),
                    arg.getDescribeApiName(), Objects.equals(arg.getExcludeInvalid(), Boolean.TRUE));
            removeUnSupportDescribe(lookupManyDescribes, unusableProductApiNames, excludeBigObject, excludeSocialObject);
            simplyDescribe(simplyDescribe, lookupManyDescribes);
            builder.lookupManyDescribeList(ObjectDescribeDocument.ofList(lookupManyDescribes));
        }

        if (arg.isIncludeDetailList()) {
            List<IObjectDescribe> detailDescribes = describeLogicService.findDetailDescribes(context.getTenantId(), arg.getDescribeApiName());
            removeUnSupportDescribe(detailDescribes, unusableProductApiNames, excludeBigObject, excludeSocialObject);
            filterDescribeList(context.getTenantId(), detailDescribes, arg.isConvertRule());
            filterByRuleApiName(context.getUser(), arg.getRuleApiName(), detailDescribes);
            simplyDescribe(simplyDescribe, detailDescribes);
            builder.detailDescribeList(ObjectDescribeDocument.ofList(detailDescribes));
        }

        if (arg.isIncludeWhatList()) {
            List<IObjectDescribe> whatDescribes = describeLogicService.findDescribeByFieldTypes(context.getTenantId(),
                    FieldDescribeExt.GROUP_TYPE, FieldDescribeExt.WHAT_GROUP_TYPES);
            removeUnSupportDescribe(whatDescribes, unusableProductApiNames, excludeBigObject, excludeSocialObject);
            simplyDescribe(simplyDescribe, whatDescribes);
            builder.whatDescribeList(ObjectDescribeDocument.ofList(whatDescribes));
        }
        if (arg.isIncludeCurrentObject()) {
            builder.objectDescribe(ObjectDescribeDocument.of(describeLogicService.findObjectWithoutCopy(context.getTenantId(), arg.getDescribeApiName())));
        }
        ManageGroup manageGroup = describeLogicService.queryObjectManageGroup(context.getUser(), arg.getSourceInfo());
        return builder
                .manageGroup(ManageGroupDTO.of(manageGroup))
                .build();
    }

    private static void simplyDescribe(boolean simplyDescribe, List<IObjectDescribe> detailDescribes) {
        if (!simplyDescribe) {
            return;
        }
        detailDescribes.forEach(describe -> describe.setFieldDescribes(Lists.newArrayList()));
    }

    private void filterByRuleApiName(User user, String ruleApiName, List<IObjectDescribe> detailDescribes) {
        if (StringUtils.isNotEmpty(ruleApiName)) {
            List<MtConvertRule> convertRuleList = objectConvertRuleService.findConvertRuleInInternalObjByApiName(user, ruleApiName, true);
            List<String> describeApiNames = convertRuleList.stream()
                    .map(MtConvertRule::getSourceObjectDescribeApiName).collect(Collectors.toList());
            detailDescribes.removeIf(describe -> !describeApiNames.contains(describe.getApiName()));
        }
    }

    private void removeUnSupportDescribe(List<IObjectDescribe> objectDescribeList, List<String> unusableProductApiNames, boolean excludeBigObject, boolean excludeSocialObject) {
        objectDescribeList.removeIf(a -> ObjectDescribeExt.BLACK_LIST.contains(a.getApiName()));
        if (CollectionUtils.notEmpty(unusableProductApiNames)) {
            objectDescribeList.removeIf(a -> unusableProductApiNames.contains(a.getApiName()));
        }
        objectDescribeList.removeIf(a -> (excludeBigObject && a.isBigObject()) || (excludeSocialObject && a.isSocialObject()));
    }

    private List<IObjectDescribe> processRelatedListForm(List<IObjectDescribe> describes, String tenantId, String describeApiName, String renderType) {
        if (!"related_list_form".equalsIgnoreCase(renderType)) {
            return describes;
        }
        if (CollectionUtils.empty(describes)) {
            return describes;
        }
        List<IObjectDescribe> result = Lists.newArrayList();
        describes.forEach(it -> {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(it);
            if (describeExt.isSlaveObject()) {
                return;
            }
            List<String> fields = describeExt.stream()
                    .map(FieldDescribeExt::of)
                    .filter(FieldDescribeExt::isObjectReferenceField)
                    .filter(FieldDescribeExt::isActive)
                    .map(FieldDescribeExt::<IObjectReferenceField>getFieldDescribe)
                    .filter(field -> Objects.equals(describeApiName, field.getTargetApiName()))
                    .map(IFieldDescribe::getApiName)
                    .collect(Collectors.toList());
            Set<String> fieldNames = AppFrameworkConfig.relatedListFormSupportObjectFields(tenantId, describeExt.getApiName(), fields);
            if (CollectionUtils.empty(fieldNames) || describeLogicService.isMasterObject(tenantId, describeExt.getApiName())) {
                return;
            }
            result.add(describeExt.retainFields(fieldNames));
        });
        return result;
    }

    @ServiceMethod("findSimpleDetailObjectList")
    public FindSimpleDetailObjectList.Result findSimpleDetailObjectList(FindSimpleDetailObjectList.Arg arg, ServiceContext context) {
        List<IObjectDescribe> detailDescribes = describeLogicService.findSimpleDetailDescribes(context.getTenantId(), arg.getDescribeApiName());
        detailDescribes.removeIf(a -> ObjectDescribeExt.BLACK_LIST.contains(a.getApiName()));
        List<String> unusableProductApiNames = ProductUtil.findUnusableProductApiNames(context.getTenantId());
        if (CollectionUtils.notEmpty(unusableProductApiNames)) {
            detailDescribes.removeIf(a -> unusableProductApiNames.contains(a.getApiName()));
        }

        if (!Strings.isNullOrEmpty(arg.getActionCode())) {
            detailDescribes = describeLogicService.filterDescribesWithActionCode(context.getUser(), detailDescribes, arg.getActionCode());
        }
        ManageGroup manageGroup = describeLogicService.queryObjectManageGroup(context.getUser(), arg.getSourceInfo());
        return FindSimpleDetailObjectList.Result.builder()
                .detailDescribeList(ObjectDescribeDocument.ofList(detailDescribes))
                .manageGroup(ManageGroupDTO.of(manageGroup))
                .build();
    }

    @ServiceMethod("findRelatedFields")
    public FindRelatedFields.Result findRelatedFields(FindRelatedFields.Arg arg, ServiceContext context) {
        List<IFieldDescribe> relatedFields = describeLogicService.findRelatedFields(context.getTenantId(), arg.getTargetApiName());
        return FindRelatedFields.Result.of(relatedFields);
    }

    @ServiceMethod("findBizDescribeList")
    public FindBizDescribeList.Result findBizDescribeList(FindBizDescribeList.Arg arg, ServiceContext context) {
        Set<String> objectVisibleScopes = getObjectVisibleScopes(arg.isOnlyBigObject(), false);
        ObjectDescribeFinder objectDescribeFinder = ObjectDescribeFinder.builder()
                .user(context.getUser())
                .describeDefineType(!arg.isIncludeSystemObj() ? DEFINE_TYPE_CUSTOM : null)
                .isOnlyActivate(!arg.isIncludeUnActived())
                .isExcludeDetailObj(arg.isArchiveRule() && !arg.isOnlyBigObject())
                .isExcludeDetailWithMasterCreated(arg.isArchiveRule() && !arg.isOnlyBigObject())
                .isAsc(arg.isAsc())
                .visibleScope(objectVisibleScopes)
                .onlyVisibleScope(arg.isOnlyBigObject())
                .build();
        List<IObjectDescribe> describeList = describeLogicService.findObjectsByTenantId(objectDescribeFinder);
        describeList.removeIf(a -> ObjectDescribeExt.CAN_NOT_MAPPING_OBJECTS.contains(a.getApiName()));
        if (arg.isArchiveRule() && arg.isOnlyBigObject()) {
            List<String> apiNames = objectArchiveRuleService.getBigObjectsUsedByRule(context.getTenantId());
            describeList.removeIf(a -> apiNames.contains(a.getApiName()) || StringUtils.equals(a.getVisibleScope(), IObjectDescribe.VISIBLE_SCOPE_PUBLIC_BIG));
        }
        if (arg.isArchiveRule()) {
            describeList.removeIf(x -> StringUtils.equals(x.getVisibleScope(), IObjectDescribe.VISIBLE_SCOPE_PUBLIC));
        }

        filterDescribeList(context.getTenantId(), describeList, arg.isConvertRule());

        ManageGroup manageGroup = describeLogicService.queryObjectManageGroup(context.getUser(), arg.getSourceInfo());

        //校验从对象是否屏蔽单独入口
        if (arg.isCheckDetailObjectButton()) {
            List<String> hideButtonApiNames = describeLogicService.findDetailApiNamesCreateWithMasterAndHiddenButton(context.getTenantId());
            describeList.stream().filter(x -> hideButtonApiNames.contains(x.getApiName())).forEach(x -> ObjectDescribeExt.of(x).hideButton());
        }

        List<String> sortedList = ObjectDescribeExt.getObjectOrderList();
        describeList = CollectionUtils.sortByGivenOrder(describeList, sortedList, IObjectDescribe::getApiName);
        return FindBizDescribeList.Result.builder()
                .objectDescribeList(ObjectDescribeDocument.ofList(describeList))
                .manageGroup(ManageGroupDTO.of(manageGroup))
                .build();
    }

    private static void filterDescribeList(String tenantId, List<IObjectDescribe> describeList, boolean convertRule) {
        if (convertRule && !UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CONVERT_RULE_SUPPORT_ALL_OBJ_GRAY, tenantId)) {
            describeList.removeIf(a -> AppFrameworkConfig.isConvertRuleBlackObject(a.getApiName()));
        }
    }

    @ServiceMethod("checkDisableFields")
    public CheckDisableFields.Result checkDisableFields(CheckDisableFields.Arg arg, ServiceContext context) {
        describeLogicService.checkDisableFields(context.getUser(), arg.getDescribeApiName(),
                arg.getFieldList());

        return CheckDisableFields.Result.builder().build();
    }

    private List<IFieldDescribe> getGroupFields(String groupFields) {
        if (Strings.isNullOrEmpty(groupFields)) {
            return null;
        }

        List<JSONObject> jsonObjects = JSON.parseArray(groupFields, JSONObject.class);
        if (CollectionUtils.empty(jsonObjects)) {
            return null;
        }

        List<IFieldDescribe> list = Lists.newArrayList();
        for (JSONObject object : jsonObjects) {
            IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(object.toJSONString());
            if (null != fieldDescribe) {
                list.add(fieldDescribe);
            }
        }
        return list;
    }

    @ServiceMethod("maxCustomDescribeCount")
    public MaxCustomDescribeCount.Result getMaxCustomDescribeCount(ServiceContext context) {
        TenantLicenseInfo tenantLicenseInfo = TenantLicenseInfo.builder()
                .user(context.getUser())
                .licenseService(licenseService)
                .build()
                .init(Sets.newHashSet(ModulePara.ModuleBiz.UDOBJ.getBizCode()));
        return MaxCustomDescribeCount.Result.builder()
                .maxCustomDescribeCount(tenantLicenseInfo.getModuleParaQuota(ModulePara.CUSTOM_OBJECTS_LIMIT))
                .build();
    }

    @ServiceMethod("usedCustomDescribeCount")
    public UsedCustomDescribeCount.Result getUsedCustomDescribeCount(ServiceContext context) {
        return UsedCustomDescribeCount.Result.builder()
                .usedCustomDescribeCount(describeLogicService.getCustomDescribeCount(context.getTenantId()))
                .build();
    }

    @ServiceMethod("findSpecifiedRange")
    public FindSpecifiedRange.Result findSpecifiedRange(FindSpecifiedRange.Arg arg, ServiceContext context) {
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(context.getTenantId(),
                Lists.newArrayList(arg.getObjectApiName(), arg.getAssociatedObjectApiName()));
        IObjectDescribe objectDescribe = describeMap.get(arg.getObjectApiName());
        IObjectDescribe targetDescribe = describeMap.get(arg.getAssociatedObjectApiName());
        if (Objects.isNull(objectDescribe) || Objects.isNull(targetDescribe)) {
            log.warn("describe not exit, tenantId:{}, userId:{}, arg:{}", context.getTenantId(), context.getUser().getUserId(), JSON.toJSONString(arg));
            return FindSpecifiedRange.Result.of(Collections.emptyMap());
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        // 当前对象不是主从同时新建的从对象，不需要构图计算依赖关系
        if (!describeExt.isSlaveObject() || !describeExt.isCreateWithMaster()) {
            FindSpecifiedRange.Result.builder().build();
        }

        ObjectRelationGraph objectRelationGraph = objectRelationGraphService.buildObjectRelationNetwork(objectDescribe, targetDescribe);

        Map<String, List<Tuple<String, String>>> specifiedRangeMap = objectRelationGraph.specifiedRange(
                arg.getObjectApiName(),
                arg.getAssociatedObjectApiName());

        return FindSpecifiedRange.Result.of(specifiedRangeMap);
    }

    @ServiceMethod("isMasterObject")
    public Boolean isMasterObject(IsMasterObject.Arg arg, ServiceContext context) {
        return describeLogicService.isMasterObject(context.getTenantId(), arg.getObjectApiName());
    }

    @ServiceMethod("initObjectFunctionPrivilege")
    public InitObjectFunctionPrivilege.Result initObjectFunctionPrivilege(InitObjectFunctionPrivilege.Arg arg, ServiceContext context) {
        describeLogicService.initObjectFunctionPrivilege(context.getUser(), arg.getObjectApiName());
        return InitObjectFunctionPrivilege.Result.builder().build();
    }

    @ServiceMethod("getCRMFeedRelatedObj")
    public CRMFeedRelatedObjResult getCRMFeedRelatedObj(ServiceContext context) {
        List<IObjectDescribe> describes = describeLogicService.findObjectsByTenantId(context.getTenantId(), IObjectDescribe.PACKAGE, true, true, false, false, ObjectListConfig.FEED);
        return CRMFeedRelatedObjResult.fromDescribes(describes);
    }

    @ServiceMethod("findCustomBasicConfiguration")
    public StandardDesignerLayoutResourceController.Result findCustomBasicConfiguration(StandardDesignerLayoutResourceController.Arg arg, ServiceContext context) {
        ControllerContext controllerContext = ContextManager.buildControllerContext(arg.getDescribeApiName(), StandardController.DesignerLayoutResource.name());
        return serviceFacade.triggerController(controllerContext, arg, StandardDesignerLayoutResourceController.Result.class);
    }

    private List<IUdefButton> findButtonsByUsePage(ObjectDescribeExt describeExt, User user, ButtonUsePageType usePageType) {
        List<IUdefButton> buttonList = customButtonService.findButtonList(user, describeExt.getApiName());
        return customButtonService.filterButtonsForUsePageType(user, null, describeExt, usePageType.getId(), buttonList, Collections.emptySet());
    }

    private List<IButton> handleLayoutDesignerButtons(String describeApiName, List<IButton> buttons, User user) {
        if (CollectionUtils.empty(buttons)) {
            return buttons;
        }
        buttons = serviceFacade.filterPartnerButtons(user, describeApiName, buttons);
        if (ObjectAPINameMapping.SalesOrder.getApiName().equals(describeApiName)
                || ObjectAPINameMapping.Leads.getApiName().equals(describeApiName)) {
            return layoutDesignerButtonManager.getProvider(describeApiName).getButtons(buttons, user);
        }
        return buttons;
    }

    @ServiceMethod("findDescribeAndNewLayout")
    public FindDescribeAndNewLayout.Result findDescribeAndNewLayout(FindDescribeAndNewLayout.Arg arg, ServiceContext context) {
        DescribeResult describeResult = describeLogicService.findDescribeAndLayout(
                context.getUser(),
                arg.getDescribeApiName(),
                arg.isIncludeLayout(),
                arg.getLayoutApiName());

        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describeResult.getObjectDescribe());
        describeExt.descriptionNullToEmpty();
        describeExt.fillDefaultShowDetailButton();

        ILayout layout = describeResult.getLayout();
        if (layout != null) {
            LayoutExt layoutExt = LayoutExt.of(layout);
            layoutExt.layoutDescriptionNullToEmpty();

            //支持按钮排序
            handleLayoutButtons(context, describeExt, layoutExt);

            List<IComponent> components = layoutExt.getComponentsSilently().stream()
                    .filter(x -> !ComponentExt.of(x).isCustomComponent()
                            && !ComponentExt.of(x).isMasterDetailComponent()
                            && !ComponentExt.of(x).isRelatedList())
                    .collect(Collectors.toList());

            //查询关联对象和从对象
            List<IObjectDescribe> relatedDescribes = describeLogicService.findRelatedDescribes(context.getTenantId(), describeExt.getApiName());
            List<RelatedObjectDescribeStructure> detailObjects = describeExt.getDetailObjectDescribeStructures(relatedDescribes);
            List<RelatedObjectDescribeStructure> lookupObjects = describeExt.getRelatedObjectDescribeStructures(relatedDescribes);
            //根据配置中心的配置去掉不展示的关联对象
            lookupObjects.removeIf(x -> DefObjConstants.isReferenceObjectInvisible(describeExt.getApiName(), x.getRelatedObjectDescribe().getApiName()));

            //从对象
            List<IComponent> detailComponentList = MasterDetailGroupComponentBuilder.builder()
                    .detailObjectsDescribeList(detailObjects).build().getComponentListForNewDesigner();
            detailComponentList = layoutExt.filterComponents(detailComponentList);
            components.addAll(detailComponentList);

            //关联对象
            List<IComponent> relateComponentList = RelatedObjectGroupComponentBuilder.builder().layout(layoutExt).objectDescribe(describeExt)
                    .user(context.getUser())
                    .relatedObjectDescribeList(lookupObjects).build().getComponentListForDesigner();
            relateComponentList = layoutExt.filterComponents(relateComponentList);
            //使用新版附件替换旧版附件
            LayoutComponents.replaceOldAttachComponent(layoutExt, relateComponentList);
            components.addAll(relateComponentList);

            //自定义组件
            List<ICustomComponent> newCustomComponents = layoutLogicService.findCustomComponents(context.getTenantId(), layoutExt);
            List<IComponent> customComponents = layoutExt.filterCustomComponents(newCustomComponents);
            components.addAll(customComponents);

            List<Map> leftTabsApiNames = null;
            //兼容只有详细信息的古老布局
            if (!layoutExt.isNewLayout()) {
                //跟进动态
                IComponent saleLogComponent = RelatedObjectGroupComponentBuilder.builder().build()
                        .buildRecordComponentWithoutFieldsAndButtons(arg.getDescribeApiName());
                if (!layoutExt.containsComponent(saleLogComponent.getName())) {
                    components.add(saleLogComponent);
                }

                //相关团队
                IComponent teamComponent = TeamComponentBuilder.builder().build().buildUserListComponentWithoutFieldsAndButtons();
                if (!layoutExt.containsComponent(teamComponent.getName())) {
                    components.add(teamComponent);
                }

                //修改记录
                IComponent modifyLogComponent = LayoutExt.buildModifyRecordComponent();
                if (!layoutExt.containsComponent(modifyLogComponent.getName())) {
                    relateComponentList.add(modifyLogComponent);
                    components.add(modifyLogComponent);
                }

                //将相关对象适配成layout_structure中的结构
                leftTabsApiNames = LayoutStructure.getOrderComponentsStructure(layoutExt, detailComponentList,
                        relateComponentList);
            }

            //根据配置过滤组件
            components.removeIf(x -> DefObjConstants.isComponentInvisible(arg.getDescribeApiName(), x.getName()));

            layoutExt.removeHiddenComponents(components);
            //将销售记录修改为跟进动态
            components.stream().filter(x -> ComponentExt.of(x).isSaleLog()).forEach(x -> x.setHeader(I18N.text(I18NKey.FOLLOW_UP_DYNAMIC)));

            Map<String, Boolean> stringBooleanMap = serviceFacade.existModule(context.getTenantId(), Sets.newHashSet(LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP));
            Boolean existMultiLanguage = stringBooleanMap.get(LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP);
            //根据i18nKey重置component的header
            ComponentHeaderSetter.builder()
                    .tenantId(context.getTenantId())
                    .layoutType(LayoutTypes.DETAIL)
                    .layoutVersion(LayoutVersion.V2)
                    .components(components)
                    .objectApiName(describeExt.getApiName())
                    .layoutApiName(layoutExt.getName())
                    .componentPreKeyMap(serviceFacade.getLayoutLogicService().findComponentPreKeys(layoutExt.getComponentsSilently()))
                    .existMultiLanguage(existMultiLanguage)
                    .build()
                    .reset();

            LayoutStructure.buildLayoutStructure(layoutExt, describeExt, components, leftTabsApiNames,
                    Lists.newArrayList(), false, false);

            List<Map<String, Object>> events = handleUIEventFunction(context, describeExt, layoutExt);
            layoutExt.setEvents(events);
        }

        return FindDescribeAndNewLayout.Result.builder()
                .objectDescribe(ObjectDescribeDocument.of(describeResult.getObjectDescribe()))
                .layout(LayoutDocument.of((describeResult.getLayout())))
                .build();
    }

    @ServiceMethod("findDescribeAndV3Layout")
    public StandardDesignerLayoutController.Result findDescribeAndV3Layout(StandardDesignerLayoutController.Arg arg, ServiceContext context) {
        ControllerContext controllerContext = ContextManager.buildControllerContext(arg.getDescribeApiName(), StandardController.DesignerLayout.name());
        return serviceFacade.triggerController(controllerContext, arg, StandardDesignerLayoutController.Result.class);
    }

    @ServiceMethod("findDescribeAndListLayout")
    public FindDescribeAndListLayout.Result findDescribeAndListLayout(ServiceContext context,
                                                                      FindDescribeAndListLayout.Arg arg) {
        LayoutContext.get().setNoReplaceLayoutNameI18n(arg.isNoNeedReplaceI18n());
        LayoutContext.get().setRemoveI18n(arg.isRemoveI18n());
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
        ILayout listLayout = findListLayout(describe, arg.getLayoutApiName(), context.getUser());
        ListLayoutExt listLayoutExt = ListLayoutExt.of(listLayout);
        Map<String, Localization> localizationMap = null;
        if (licenseService.isSupportMultiLanguage(context.getTenantId())) {
            List<String> keys = listLayoutExt.getListLayoutComponentKeys();
            localizationMap = i18nSettingService.getLocalization(keys, context.getTenantId(), true, true);
        }
        ListLayoutRender.builder()
                .describeExt(ObjectDescribeExt.of(describe))
                .listLayoutExt(ListLayoutExt.of(listLayout))
                .user(context.getUser())
                .customButtonService(customButtonService)
                .sceneLogicService(sceneLogicService)
                .buttonLogicService(buttonLogicService)
                .licenseService(licenseService)
                .pageType(PageType.Designer)
                .layoutResourceService(layoutResourceService)
                .viewComponentFactory(viewComponentFactory)
                .listComponentFactory(listComponentFactory)
                .optionalFeaturesService(optionalFeaturesService)
                .localizationMap(localizationMap)
                .layoutLogicService(layoutLogicService)
                .functionPrivilegeService(functionPrivilegeService)
                .build()
                .render();
        DescribeExtra describeExtra = describeLogicService.findDescribeExtraByRenderType(context.getUser(), describe, Collections.emptyList(),
                DescribeExpansionRender.RenderType.Designer, true);

        return FindDescribeAndListLayout.Result.builder()
                .objectDescribe(ObjectDescribeDocument.of(describe))
                .describeExtra(ObjectDescribeDocument.of(describeExtra))
                .layout(LayoutDocument.of(listLayout))
                .build();
    }

    @ServiceMethod("findDescribeAndAbstractLayout")
    public FindDescribeAndAbstractLayout.Result findDescribeAndAbstractLayout(ServiceContext context,
                                                                              FindDescribeAndAbstractLayout.Arg arg) {
        LayoutContext.get().setNoReplaceLayoutNameI18n(arg.isNoNeedReplaceI18n());
        LayoutContext.get().setRemoveI18n(arg.isRemoveI18n());
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
        ILayout layout = layoutLogicService.findAbstractLayoutByApiName(context.getUser(), arg.getLayoutApiName(), describe);
        DescribeExtra describeExtra = describeLogicService.findDescribeExtraByRenderType(context.getUser(), describe, Collections.emptyList(),
                DescribeExpansionRender.RenderType.Designer, true);

        return FindDescribeAndAbstractLayout.Result.builder()
                .objectDescribe(ObjectDescribeDocument.of(describe))
                .layout(LayoutDocument.of(layout))
                .describeExtra(ObjectDescribeDocument.of(describeExtra))
                .build();
    }

    @ServiceMethod("findDescribeAndFLowTaskLayout")
    public FindDescribeAndFLowTaskLayout.Result findDescribeAndFLowTaskLayout(ServiceContext context, FindDescribeAndFLowTaskLayout.Arg arg) {
        LayoutContext.get().setNoReplaceLayoutNameI18n(arg.isNoNeedReplaceI18n());
        LayoutContext.get().setRemoveI18n(arg.isRemoveI18n());
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
        IObjectDescribe whatDescribe = describeLogicService.findObject(context.getTenantId(), arg.getWhatDescribeApiName());
        ILayout layout = layoutLogicService.findFlowTaskListLayoutsByLayoutNameAndType(context.getUser(), describe, whatDescribe, arg.getLayoutApiName(), arg.getLayoutType());

        if (Objects.nonNull(layout) && LayoutExt.of(layout).isFlowTaskLayout()) {
            FLowTaskLayoutRender.builder()
                    .flowTaskLayout(layout)
                    .objectDescribe(describe)
                    .whatObjectDescribe(whatDescribe)
                    .user(context.getUser())
                    .layoutLogicService(layoutLogicService)
                    .pageType(PageType.Designer)
                    .build()
                    .render();
        }
        return FindDescribeAndFLowTaskLayout.Result.builder()
                .objectDescribe(ObjectDescribeDocument.of(describe))
                .whatDescribe(ObjectDescribeDocument.of(whatDescribe))
                .layout(LayoutDocument.of(layout))
                .build();
    }

    @ServiceMethod("supportManyAbstractLayout")
    public FindDescribeAndAbstractLayout.Support.Result supportManyAbstractLayout(ServiceContext context,
                                                                                  FindDescribeAndAbstractLayout.Support.Arg arg) {

        boolean gray = AppFrameworkConfig.manyAbstractLayout(arg.getDescribeApiName(), context.getTenantId());
        return FindDescribeAndAbstractLayout.Support.Result.builder()
                .gray(gray)
                .build();
    }

    @ServiceMethod("supportManyAbstractLayoutFromObjApiNames")
    public SupportFromObjApiNames.Result supportManyAbstractLayoutBulk(ServiceContext context,
                                                                       SupportFromObjApiNames.Arg arg) {

        List<Map<String, Object>> supportFromObjApiNames = AppFrameworkConfig.manyAbstractLayout(arg.getDescribeApiNames(), context.getTenantId());
        return SupportFromObjApiNames.Result.builder()
                .grayList(supportFromObjApiNames)
                .build();
    }

    private ILayout findListLayout(IObjectDescribe describe, String layoutApiName, User user) {
        ILayout listLayout = layoutLogicService.findListLayoutByApiName(user, layoutApiName, describe.getApiName());
        // 兼容错误数据
        ListLayoutExt.of(listLayout).handleViewInfo();
        return listLayout;
    }


    @ServiceMethod("findObjectConfig")
    public FindObjectConfig.Result findObjectConfig(ServiceContext context, FindObjectConfig.Arg arg) {
        Map<String, ObjectConfig> objectConfig;
        if (arg.getIncludeBusiness() == null && arg.getIncludeFilters() == null) {
            objectConfig = objectConfigService.getObjectConfig(context.getUser(), arg.getApiNameList(), arg.getSource());
        } else {
            objectConfig = objectConfigService.getObjectConfig(context.getUser(), arg.getApiNameList(), arg.getIncludeBusiness(), arg.getIncludeFilters(), arg.getSource());
        }
        return FindObjectConfig.Result.builder().config(objectConfig).build();
    }

    @ServiceMethod("findBusinessConfig")
    public FindBusinessConfig.Result findBusinessConfig(ServiceContext context, FindBusinessConfig.Arg arg) {
        Map<String, Object> businessConfig = objectConfigService.getBusinessConfig(context.getUser(), arg.getApiNameList());
        return FindBusinessConfig.Result.builder().businessConfig(businessConfig).build();
    }

    @ServiceMethod("findWebConfig")
    public FindWebConfig.Result findWebConfig(ServiceContext context) {
        Map<String, Object> webConfig = objectConfigService.getWebConfig(context.getUser());
        webConfig.put(CALC_CRITERIA.name(), TenantUtil.isCalcCriteria(context.getTenantId()));
        webConfig.put(MAX_COUNT.name(), TenantUtil.isMaxCount(context.getTenantId()));
        webConfig.put(AppFrameworkConfig.WHERE_FIELD_IS_USED_GRAY_KEY, UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FIND_WHERE_A_FIELD_IS_USED_GRAY_KEY, context.getTenantId()));
        webConfig.put(AppFrameworkConfig.NO_SUPPORT_CURRENT_LOGIN_USERID, AppFrameworkConfig.getNoSupportCurrentLoginUserObjects());
        webConfig.put(AppFrameworkConfig.GRAY_HEAD_INFO_TEMPLATE, TenantUtil.isCardTemplates(context.getTenantId()));
        //是否支持查看计算进度
        webConfig.put(UdobjGrayConfigKey.SUPPORT_CALCULATION_PROGRESS, true);
        webConfig.put(UdobjGrayConfigKey.DEFAULT_VALUE_SUPPORT_OWNER_GRAY, UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEFAULT_VALUE_SUPPORT_OWNER_GRAY, context.getTenantId()));
        webConfig.put(UdobjGrayConfigKey.SUPPORT_CROSS_OBJECT_GRAY, UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SUPPORT_CROSS_OBJECT_GRAY, context.getTenantId()));
        webConfig.put(UdobjGrayConfigKey.OPEN_SIDEBAR_LAYOUT_GRAY, UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OPEN_SIDEBAR_LAYOUT_GRAY, context.getTenantId()));
        webConfig.put(UdobjGrayConfigKey.OPEN_SIDEBAR_LAYOUT_BLACK_OBJ, AppFrameworkConfig.getSidebarLayoutTemplate().keySet());
        return FindWebConfig.Result.builder().result(webConfig).build();
    }


    @ServiceMethod("findOcrType")
    public FindOcrType.Result findOcrType(ServiceContext context, FindOcrType.Arg arg) {
        List<OcrType> ocrTypes = ocrLogicService.findOcrTypes(context.getUser());
        return FindOcrType.Result.builder().ocrTypes(ocrTypes).build();
    }

    @ServiceMethod("ocrIdentify")
    public OcrIdentify.Result ocrIdentify(ServiceContext context, OcrIdentify.Arg arg) {
        IObjectDescribe objectDescribe = describeLogicService.findObject(context.getTenantId(), arg.getMasterObjectApiName());
        IObjectData masterData = arg.getMasterData().toObjectData();
        OcrIdentifyDto ocrIdentifyDto = ocrLogicService.ocrIdentify(context.getUser(), objectDescribe, arg.getOcrFieldApiName(), masterData);
        OcrIdentify.Result result = new OcrIdentify.Result();
        if (Objects.nonNull(ocrIdentifyDto)) {
            if (Objects.nonNull(ocrIdentifyDto.getOcrResult())) {
                List<OcrDataDto> ocrResultList = Lists.newArrayList();
                ocrIdentifyDto.getOcrResult().forEach((path, ocrData) -> {
                    OcrDataDto ocrDataResult = OcrDataDto.builder()
                            .fieldName(arg.getOcrFieldApiName())
                            .path(path)
                            .ocrData(ocrData)
                            .build();
                    ocrResultList.add(ocrDataResult);
                });
                result.setOcrDataResult(ocrResultList);
            }
            IObjectData objectData = ocrIdentifyDto.getObjectData();
            Map<String, Object> diff = ObjectDataExt.of(masterData).diff(objectData, objectDescribe);
            if (CollectionUtils.empty(diff)) {
                return result;
            }
            EditCalculateParam param = EditCalculateParam.builder()
                    .masterDescribe(objectDescribe)
                    .masterData(objectData)
                    .oldMasterData(masterData)
                    .masterModifyData(diff)
                    .excludeDefaultValue(false)
                    .includeQuoteField(true)
                    .excludeLookupRelateField(false)
                    .build();
            Map<String, Map<String, ObjectDataDocument>> detailDataMap = arg.getDetailDataMap();
            Map<String, List<IObjectData>> oldDetailData = Maps.newHashMap();
            Map<String, List<IObjectData>> newDetailData = Maps.newHashMap();
            Map<String, IObjectDescribe> detailDescribeMap = Maps.newHashMap();
            if (CollectionUtils.notEmpty(detailDataMap)) {
                Set<String> detailDescribeApiNames = detailDataMap.keySet();
                detailDescribeMap = describeLogicService.findObjects(context.getTenantId(), detailDescribeApiNames);
                detailDataMap.forEach((apiName, dataMap) -> {
                    List<IObjectData> dataList = Lists.newArrayList();
                    dataMap.forEach((mark, data) -> {
                        ObjectDataExt dataExt = ObjectDataExt.of(data);
                        dataExt.setTemporaryId(mark);
                        IObjectData detail = dataExt.getObjectData();
                        detail.setDescribeApiName(apiName);
                        detail.setTenantId(context.getTenantId());
                        dataList.add(detail);
                    });
                    oldDetailData.put(apiName, dataList);
                });
                newDetailData = ObjectDataExt.copyMap(oldDetailData);
            }
            param.setDetailDescribeMap(detailDescribeMap);
            param.setOldDetailDataMap(newDetailData);
            param.setDetailDataMap(newDetailData);
            param.setDetailModifyDataMap(newDetailData);
            param.initWithUIEvent();
            // 调用计算接口
            serviceFacade.calculateForEditData(context.getUser(), param);
            Map<String, Object> masterModifyData = ObjectDataExt.of(masterData).diff(objectData, objectDescribe);
            result.addData(arg.getMasterObjectApiName(), "0", ObjectDataExt.of(masterModifyData));
            Map<String, List<IObjectData>> detailModifyDataMap = ObjectDataExt.diffDetailWithTemporaryId(oldDetailData, newDetailData, detailDescribeMap, true);
            detailModifyDataMap.forEach((detailApiName, detailDataList) ->
                    detailDataList.forEach(detailModifyData ->
                            result.addData(detailApiName, ObjectDataExt.of(detailModifyData).getTemporaryId(), detailModifyData)));
        }
        return result;
    }

    @ServiceMethod("findDescribeByGroupFieldType")
    public FindDescribeByGroupFieldType.Result findDescribeByGroupFieldType(ServiceContext context, FindDescribeByGroupFieldType.Arg arg) {
        if (CollectionUtils.empty(arg.getFieldType())) {
            return FindDescribeByGroupFieldType.Result.builder().build();
        }
        List<IObjectDescribe> objectDescribes = describeLogicService.findDescribeByFieldTypes(context.getTenantId(), FieldDescribeExt.GROUP_TYPE, new ArrayList<>(arg.getFieldType()));
        ManageGroup manageGroup = describeLogicService.queryObjectManageGroup(context.getUser(), arg.getSourceInfo());
        objectDescribes.removeIf(x -> !x.isActive());
        objectDescribes.forEach(x -> {
            List<IFieldDescribe> fieldDescribes = x.getFieldDescribes();
            fieldDescribes.removeIf(y -> !FieldDescribeExt.of(y).isGroupField() || !arg.getFieldType().contains(((GroupField) y).getGroupType()));
            x.setFieldDescribes(fieldDescribes);
        });

        return FindDescribeByGroupFieldType.Result.builder()
                .objectDescribeList(ObjectDescribeDocument.ofList(objectDescribes))
                .manageGroup(ManageGroupDTO.of(manageGroup))
                .build();
    }

    @ServiceMethod("findDescribeExtra")
    public FindDescribeExtra.Result findDescribeExtra(ServiceContext context, FindDescribeExtra.Arg arg) {
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
        // 查询字段附加描述
        DescribeExtra describeExtra = describeLogicService.findDescribeExtraByRenderType(context.getUser(), describe, Collections.emptyList(),
                DescribeExpansionRender.RenderType.of(arg.getDescribeExtraRenderType()), true);

        // 查询对象附加描述
        Map<String, IObjectDescribeExtra> objExtraMap = objExtraService.find(context.getTenantId(), Lists.newArrayList(arg.getDescribeApiName()));
        Optional<IObjectDescribeExtra> extraOpt = Optional.ofNullable(objExtraMap)
                .map(extra -> extra.get(arg.getDescribeApiName()));
        extraOpt.ifPresent(extra -> {
            if (Objects.nonNull(extra.getIconIndex())) {
                describeExtra.getDescribeExpansion().putIfAbsent(ICON_SLOT, extra.getIconIndex());
            }
        });
        return FindDescribeExtra.Result.of(ObjectDescribeDocument.of(describeExtra));
    }

    @ServiceMethod("updateDescribeExtra")
    public UpdateDescribeExtra.Result updateDescribeExtra(ServiceContext context, UpdateDescribeExtra.Arg arg) {
        Map<String, Object> describeExtraAttribute = arg.getDescribeExtra();
        // 更新字段附加描述
        DescribeExtra updateDescribeExtra = describeLogicService.updateDescribeExtra(context.getUser(), arg.getDescribeApiName(), arg.getDescribeExtra());
        // 更新对象附加描述
        // 描述中存在 icon_slot 值才刷
        if (Objects.nonNull(describeExtraAttribute.get(ObjectDescribeExt.ICON_SLOT))) {
            if (!ObjectDescribeExt.isCustomObject(arg.getDescribeApiName())) {
                // 如果为预设对象设置 icon 需要存在默认图标，在对象编辑恢复默认图标时使用
                int iconSlot = PreObjDefaultConfig.existIconSlotAndGet(arg.getDescribeApiName());
                if (iconSlot < 0) {
                    log.warn("not found default icon_slot for PreObj: 「{}」, you need config fs-webpage-iconSlot-preObject for your PreObj", arg.getDescribeApiName());
                    throw new ValidateException("not found default icon_slot for PreObj");
                }
            }
            objExtraService.save(context.getTenantId(), arg.getDescribeApiName(), describeExtraAttribute);
        }


        return UpdateDescribeExtra.Result.of(ObjectDescribeDocument.of(updateDescribeExtra));
    }

    @ServiceMethod("deleteFieldListConfig")
    public DeleteFieldListConfig.Result deleteFieldListConfig(DeleteFieldListConfig.Arg arg, ServiceContext context) {
        dataListHeaderConfigService.deleteByUserObjApiName(context.getUser(), arg.getObjApiName());
        return DeleteFieldListConfig.Result.builder().done(true).build();
    }

    @ServiceMethod("findObjectResourceSummary")
    public ObjectResourceSummary.Result findObjectResourceSummary(ObjectResourceSummary.Arg arg, ServiceContext context) {
        List<ResourcesRecord> records = describeLogicService.getCustomFieldCountLimit(context.getUser(), arg.getDescribeApiName());
        return ObjectResourceSummary.Result.builder().records(records).build();
    }

    @Nullable
    private List<Map<String, Object>> handleUIEventFunction(ServiceContext context, ObjectDescribeExt describeExt, LayoutExt layoutExt) {
        // 补充UI事件中的函数信息
        List<Map<String, Object>> events = layoutExt.getEvents();
        if (CollectionUtils.notEmpty(events)) {
            List<UIEventExt> eventXs = UIEventExt.ofList(events);
            List<String> funcApiNames = eventXs.stream().map(UIEventExt::getFuncApiName).collect(Collectors.toList());
            List<IUdefFunction> functions = serviceFacade.getFunctionLogicService().findFunctionByApiNames(context.getUser(),
                    funcApiNames, describeExt.getApiName());
            for (UIEventExt e : eventXs) {
                functions.stream().filter(
                        f -> f.getApiName().equals(e.getFuncApiName())).findFirst().ifPresent(func -> {
                    e.set(UIEventExt.FUNC_NAME, func.getFunctionName());
                    e.set(UIEventExt.FUNC_DESCRIBE, func.getRemark());
                });

            }
        }
        return events;
    }

    private void handleLayoutButtons(ServiceContext context, ObjectDescribeExt describeExt, LayoutExt layoutExt) {
        if (CollectionUtils.empty(layoutExt.getButtonOrder())) {
            //如果按钮为空，说明没有排序，响应默认排序
            //自定义按钮
            List<IUdefButton> iButtons = findButtonsByUsePage(describeExt, context.getUser(), ButtonUsePageType.Detail);
            //过滤自定义按钮
            List<IButton> customButtons = ButtonOrder.filteringUdefButton(iButtons);
            ButtonOrder.fillLayoutWithButtonsByDefaultOrder(layoutExt, customButtons, describeExt);
        } else {
            //保存于布局中的按钮，按照ObjectAction的名称显示
            ButtonOrder.synchronizeName(layoutExt, describeExt);
            //layout中存在按钮，说明已排序，将新增按钮加入layout
            fillLayoutWithNewButtons(layoutExt, context, describeExt);
        }
        //老对象特殊按钮处理,不启用的按钮不在布局中显示
        if (describeExt.isSFAObject()) {
            SpecialButtonProvider provider = specialButtonManager.getProvider(describeExt.getApiName());
            List<IButton> specialButtons = provider.getSpecialButtons();
            if (CollectionUtils.notEmpty(specialButtons)) {
                //移除layout中没有启用的特殊按钮
                List<IButton> removeButtons = specialButtons.stream().filter(s ->
                        !LayoutButtonExt.of(s).isActive()).collect(Collectors.toList());
                List<IButton> buttons = layoutExt.getButtonOrder();
                buttons.removeAll(removeButtons);
                layoutExt.setButtonOrder(buttons);
            }
        }
        //过滤禁用的按钮
        layoutExt.removeUnActiveButton();
        // 处理预置对象,需要开通服务才展示的按钮
        layoutExt.setButtonOrder(handleLayoutDesignerButtons(describeExt.getApiName(), layoutExt.getButtonOrder(), context.getUser()));
    }

    private void handleSFAButtons(List<IButton> orderedButtons, User user, String apiName) {
        // 特殊处理合作伙伴 sfa的老对象全都走合作伙伴判断
        String partnerOpen = configService.findTenantConfig(user, "config_partner_open");
        // 处理未开启合作伙伴按钮
        if (partnerOpen == null) {
            orderedButtons.removeIf(o -> Objects.equals(ObjectAction.CHANGE_PARTNER.getActionCode(), o.getAction())
                    || Objects.equals(ObjectAction.DELETE_PARTNER.getActionCode(), o.getAction())
                    || Objects.equals(ObjectAction.CHANGE_PARTNER_OWNER.getActionCode(), o.getAction()));
        }

        // 处理订单的按钮下发逻辑
        if (Objects.equals(Utils.SALES_ORDER_API_NAME, apiName)) {
            String deliveryNoteEnabled = configService.findTenantConfig(user, "33");
            if (deliveryNoteEnabled == null || Objects.equals(deliveryNoteEnabled, "0")) {
                orderedButtons.removeIf(o -> Objects.equals(ObjectAction.ADD_DELIVERY_NOTE.getActionCode(), o.getAction()));
            }
            if (Objects.equals(deliveryNoteEnabled, "1")) {
                orderedButtons.removeIf(o -> Objects.equals(ObjectAction.CONFIRM_DELIVERY.getActionCode(), o.getAction())
                        || Objects.equals(ObjectAction.CONFIRM_RECEIPT.getActionCode(), o.getAction()));
            }
        }
    }

    private String getLookupRolesPermission(ObjectReferenceWrapper fieldDescribe, String roleType, boolean outPermission) {
        if (ObjectUtils.isEmpty(fieldDescribe)
                || ObjectUtils.isEmpty(roleType)
                || ObjectUtils.isEmpty(fieldDescribe.getLookupRoles())) {
            return "disable";
        }


        boolean writePermission = containsPermission(outPermission, false, roleType, fieldDescribe.getLookupRoles());
        if (writePermission) {
            return "write";
        }

        boolean readPermission = containsPermission(outPermission, true, roleType, fieldDescribe.getLookupRoles());
        if (readPermission) {
            return "read";
        }


        return "disable";
    }

    private boolean containsPermission(boolean outPermission, boolean readPermission, String roleType, List<String> roles) {
        if (ObjectUtils.isEmpty(roles) || ObjectUtils.isEmpty(roleType)) {
            return false;
        }

        String key = getRolePermissionKey(outPermission, readPermission, roleType);

        //内部只读的权限，历史数据 只包含 roleType,没有前后缀，这个需要兼容
        if (!outPermission && readPermission) {
            return roles.contains(roleType) || roles.contains(key);
        }

        return roles.contains(key);
    }

    private boolean removeALLPermission(String roleType, boolean outPermission, List<String> roles) {
        if (ObjectUtils.isEmpty(roles)) {
            return false;
        }

        if (outPermission) {
            return roles.removeIf(role -> ObjectUtils.isNotEmpty(role) && role.startsWith("1_" + roleType));
        }

        return roles.removeIf(role -> ObjectUtils.isNotEmpty(role) && (Objects.equals(roleType, role) || role.startsWith("0_" + roleType)));
    }


    /**
     * 字段中存储的权限
     * 1_1_r
     * 第一个数字  0 内部  1 外部
     * 第二个数字  角色类型
     * 第三个字符串 r 读 w 写
     */
    private String getRolePermissionKey(Boolean outPermission, boolean readPermission, String roleType) {
        String prefix = outPermission ? "1_" : "0_";
        String suffix = readPermission ? "_r" : "_w";
        return prefix + roleType + suffix;
    }

    @ServiceMethod("findDescribe")
    public FindDescribe.Result findDescribe(FindDescribe.Arg arg, ServiceContext context) {
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
        List<IObjectDescribe> details = null;
        if (Boolean.TRUE.equals(arg.getIncludeDetails())) {
            details = describeLogicService.findDetailDescribesCreateWithMaster(context.getTenantId(), arg.getDescribeApiName());
        }
        ManageGroup manageGroup = describeLogicService.queryObjectManageGroup(context.getUser(), arg.getSourceInfo());
        return FindDescribe.Result.builder()
                .describe(ObjectDescribeDocument.of(describe))
                .details(arg.getIncludeDetails() ? ObjectDescribeDocument.ofList(details) : null)
                .manageGroup(ManageGroupDTO.of(manageGroup))
                .build();
    }

    @ServiceMethod("isGrayMultipleTimeZone")
    public Boolean isGrayMultipleTimeZone(ServiceContext context) {
        return isDateTimeSupportNotUseMultitimeZoneTenant(context.getTenantId());
    }

    @ServiceMethod("queryAllSlave2Master")
    public Map<String, String> queryAllSlave2Master(ServiceContext context) {
        Map<String, String> allSlave2Master = describeLogicService.queryAllSlave2Master(context.getUser());
        if (CollectionUtils.empty(allSlave2Master)) {
            return Maps.newHashMap();
        }
        return allSlave2Master;
    }

    @ServiceMethod("copyObject")
    public CopyObject.Result copyObject(ServiceContext context, CopyObject.Arg arg) {
        String sourceDescribeApiName = arg.getSourceDescribeApiName();
        String describeLabel = arg.getDescribeLabel();
        String describeApiName = arg.getDescribeApiName();
        if (StringUtils.isAnyEmpty(sourceDescribeApiName, describeLabel, describeApiName)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        copyObjectService.copyObject(context.getUser(), sourceDescribeApiName, describeApiName, describeLabel);
        return CopyObject.Result.builder().success(true).build();
    }


    /**
     * 初始化工具使用，单独灰度fs企业
     *
     * @param context
     * @param arg
     * @return
     */
    @ServiceMethod("createOrUpdateDescribe")
    public Map<String, String> createOrUpdateDescribe(ServiceContext context, CreateOrUpdateDescribe.Arg arg) {
        ObjectDescribeDocument describeJson = arg.getDescribeJson();
        String objectApiName = arg.getDescribeApiName();
        IObjectDescribe describeDraft;
        boolean existObj = false;
        if (CollectionUtils.notEmpty(describeJson)) {
            describeDraft = new ObjectDescribe(describeJson);
            describeDraft.setTenantId(context.getTenantId());
            if (StringUtils.isNotEmpty(objectApiName)) {
                describeDraft.setApiName(objectApiName);
            }
        } else {
            describeDraft = describeLogicService.findObject(context.getTenantId(), objectApiName);
            existObj = true;
        }

        ObjectDescribeDocument describeAttribute = arg.getDescribeAttribute();
        if (CollectionUtils.notEmpty(describeAttribute)) {
            describeAttribute.keySet().forEach(attr -> describeDraft.set(attr, describeAttribute.get(attr)));
        }
        Map<String, Map<String, Object>> fields = arg.getFields();
        if (CollectionUtils.notEmpty(fields)) {
            fields.keySet().forEach(fieldApiName -> {
                IFieldDescribe fieldDescribeInDB = describeDraft.getFieldDescribe(fieldApiName);
                IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fields.get(fieldApiName));
                if (Objects.isNull(fieldDescribeInDB)) {
                    describeDraft.addFieldDescribe(fieldDescribe);
                } else {
                    describeDraft.updateFieldDescribe(fieldDescribe);
                }
            });
        }
        if (existObj) {
            describeLogicService.update(describeDraft);
        } else {
            describeLogicService.create(false, describeDraft);
        }
        return Maps.newHashMap();
    }

    @ServiceMethod("changePeopleAllocateRule")
    public ChangePeopleAllocateRule.Result changePeopleAllocateRule(ServiceContext context, ChangePeopleAllocateRule.Arg arg) {
        if (Objects.isNull(arg) || StringUtils.isAnyBlank(arg.getAllocateRule(), arg.getFieldApiName(), arg.getObjectApiName())) {
            throw new ValidateException(I18NExt.text(I18NExt.text(I18NKey.PARAM_ERROR)));
        }
        IObjectDescribe objectDescribe = describeLogicService.findObject(context.getTenantId(), arg.getObjectApiName());
        if (Objects.isNull(objectDescribe)) {
            throw new ValidateException(I18NExt.text(I18NKey.OBJECT_NOT_EXIST, arg.getObjectApiName()));
        }
        if (ObjectDescribeExt.of(objectDescribe).getMasterDetailFieldDescribe().isPresent()) {
            throw new ValidateException(I18NExt.text(I18NKey.CAN_NOT_ONLY_CHANGE_DETAIL_OBJECT_OWNER));
        }
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(arg.getFieldApiName());
        if (Objects.isNull(fieldDescribe)) {
            throw new ValidateException(I18NExt.text(I18NKey.FIELD_ALREADY_DELETED));
        }
        if (!FieldDescribeExt.of(fieldDescribe).isEmployeeField()) {
            throw new ValidateException(I18NExt.text(I18NKey.FIELD_TYPE_ERROR, fieldDescribe.getLabel()));
        }
        Employee employee = (Employee) fieldDescribe;
        employee.setPeopleAllocateRule(arg.getAllocateRule());
        describeLogicService.updateCustomFieldDescribe(context.getUser(), arg.getObjectApiName(), employee.toJsonString(), null, null);
        return ChangePeopleAllocateRule.Result.builder().success(true).build();
    }

    @ServiceMethod("validateObjName")
    public ValidateObjName.Result validateObjName(ValidateObjName.Arg arg, ServiceContext serviceContext) {
        String name = arg.getName();
        if (StringUtils.isBlank(name)) {
            return ValidateObjName.Result.builder().build();
        }
        String apiName = getApiNameByName(name);
        if (StringUtils.isNotEmpty(apiName)) {
            //当前企业有license的对象
            Set<String> hasLicenseObj = licenseService.getObjectApiNames(serviceContext.getTenantId());
            if (hasLicenseObj.contains(apiName)) {
                return ValidateObjName.Result.builder().value(I18NExt.text(I18NKey.SYSTEM_EXISTS_OBJ, getApiNameByName(name))).build();
            }
            return ValidateObjName.Result.builder().value(I18NExt.text(I18NKey.ALREADY_SUPPORT_PRE_OBJ)).build();
        }
        return ValidateObjName.Result.builder().build();
    }

    @ServiceMethod("batchUpdateFields")
    public BatchUpdateFields.Result batchUpdateFields(ServiceContext context, BatchUpdateFields.Arg arg) {
        List<BatchUpdateFields.BatchUpdateFieldsInfo> batchUpdateFieldsInfoList = arg.getBatchUpdateFieldsInfoList();
        if (CollectionUtils.empty(batchUpdateFieldsInfoList)) {
            return BatchUpdateFields.Result.builder().build();
        }

        Map<String, List<BatchUpdateFields.FieldInfo>> objToFieldInfos = batchUpdateFieldsInfoList.stream()
                .collect(Collectors.toMap(BatchUpdateFields.BatchUpdateFieldsInfo::getDescribeApiName,
                        BatchUpdateFields.BatchUpdateFieldsInfo::getFieldInfos, (x1, x2) -> x2));

        Map<String, IObjectDescribe> obejctDescribeMap = describeLogicService.findObjects(context.getTenantId(), objToFieldInfos.keySet());

        Map<String, Set<String>> failApiNameMap = Maps.newHashMap();
        List<DynamicDescribe> dynamicDescribeList = convertArgWithBatchUpdateFieldAttribute(objToFieldInfos, obejctDescribeMap, failApiNameMap);

        describeLogicService.batchUpdateFieldDescribe(context.getUser(), dynamicDescribeList, failApiNameMap);

        BatchUpdateFields.Result result = BatchUpdateFields.Result.builder().success(true).build();
        if (CollectionUtils.notEmpty(failApiNameMap)) {
            result.setFailApiNameList(failApiNameMap);
            result.setSuccess(false);
        }
        return result;
    }

    private List<DynamicDescribe> convertArgWithBatchUpdateFieldAttribute(Map<String, List<BatchUpdateFields.FieldInfo>> objToFieldInfos,
                                                                          Map<String, IObjectDescribe> obejctDescribeMap, Map<String, Set<String>> failApiNameMap) {
        List<DynamicDescribe> dynamicDescribeList = Lists.newArrayList();
        //转换参数格式
        for (Map.Entry<String, List<BatchUpdateFields.FieldInfo>> objFieldInfo : objToFieldInfos.entrySet()) {
            String describeApiName = objFieldInfo.getKey();
            List<BatchUpdateFields.FieldInfo> fieldInfos = objFieldInfo.getValue();
            List<Map<String, Object>> fields = Lists.newArrayList();
            IObjectDescribe describe = obejctDescribeMap.get(describeApiName);
            Set<String> failFieldList = Sets.newHashSet();
            for (BatchUpdateFields.FieldInfo fieldInfo : fieldInfos) {
                //判断要编辑的字段是否
                if (!AppFrameworkConfig.isMatchFieldTypeWithAttributeKey(describe.getFieldDescribe(fieldInfo.getFieldApiName()), fieldInfo.getAttributeKey())) {
                    failFieldList.add(fieldInfo.getFieldApiName());
                    continue;
                }
                formatAttributeValue(fieldInfo);
                fields.add(fieldInfo.toMap());
            }
            dynamicDescribeList.add(new DynamicDescribe(describeApiName, fields));
            if (CollectionUtils.notEmpty(failFieldList)) {
                failApiNameMap.put(describeApiName, failFieldList);
            }
        }
        return dynamicDescribeList;
    }

    private void formatAttributeValue(BatchUpdateFields.FieldInfo fieldInfo) {
        if (Objects.isNull(fieldInfo) || Objects.isNull(fieldInfo.getAttributeValue())) {
            return;
        }
        if (fieldInfo.getAttributeValue() instanceof Map) {
            fieldInfo.setAttributeValue(JSON.toJSONString(fieldInfo.getAttributeValue()));
        }
    }


    @ServiceMethod("queryDisplayNameByApiNames")
    public QueryDisplayNameByApiNames.Result queryDisplayNameByApiNames(QueryDisplayNameByApiNames.Arg arg, ServiceContext serviceContext) {
        Map<String, String> displayNames = describeLogicService.queryDisplayNameByApiNames(serviceContext.getTenantId(), arg.getApiNames());
        return QueryDisplayNameByApiNames.Result.builder()
                .displayNames(displayNames)
                .build();
    }

    @ServiceMethod("roleList")
    public TeamRole.Result findTeamRoleList(TeamRole.Arg arg, ServiceContext serviceContext) {
        String describeApiName = arg.getDescribeApiName();
        List<TeamRole.TeamMemberRole> roleList = Lists.newArrayList();
        List<TeamRoleInfo> teamRoleInfos = teamMemberRoleService.queryTeamRoleInfo(serviceContext.getTenantId(), describeApiName);
        teamRoleInfos.forEach(roleInfo -> {
            if (roleInfo.getStatus() == 0 || StringUtils.equals(roleInfo.getRoleType(), TeamMember.Role.OWNER.getValue())) {
                return;
            }
            TeamRole.TeamMemberRole memberRole = TeamRole.TeamMemberRole.builder()
                    .roleName(roleInfo.getRoleName())
                    .roleType(roleInfo.getRoleType())
                    .status(roleInfo.getStatus())
                    .build();
            roleList.add(memberRole);
        });
        return TeamRole.Result.builder()
                .roleList(roleList)
                .build();
    }

    @ServiceMethod("supportCustomTeamRole")
    public Map<String, Boolean> findTeamRoleList(ServiceContext serviceContext) {
        Map<String, Boolean> result = Maps.newHashMap();
        result.put(CrmResult.SUCCESS_MSG, TeamMember.isTeamRoleGray(serviceContext.getTenantId()));
        return result;
    }

    @ServiceMethod("whereitisused")
    public WhereUsed.Result whereItIsUsed(WhereUsed.Arg arg, ServiceContext serviceContext) {
        // menu
        List<MenuConf> menuConfList = RefFieldService.INVOKER_LIST;
        List<WhereUsed.Menu> menuList = Lists.newArrayList();
        for (MenuConf conf : menuConfList) {
            Menu menu = Menu.builder()
                    .invokerType(conf.getInvokerType())
                    .label(I18NExt.getOrDefault(conf.getLabelI18n().getKey(), conf.getLabelI18n().getDefaultVal()))
                    .build();
            menuList.add(menu);
        }
        // Find Where a Field Is Used
        List<WhereUsed.Entity> entityList = entityReferenceExtService.findWhereUsedEntity(serviceContext.getUser(),
                arg.getDescribeApiName(), arg.getInvokerType(), arg.getInvokerValue());
        return WhereUsed.Result.builder().menuList(menuList).entityList(entityList).build();

    }

    @ServiceMethod("findDefaultByPreObj")
    public GetDefault.Result findDefaultConfig(GetDefault.Arg arg, ServiceContext serviceContext) {
        String preObjApiName = arg.getDescribeApiName();
        Map<String, Object> defaultConfig = PreObjDefaultConfig.getConfByApi(preObjApiName);
        return GetDefault.Result.builder().config(defaultConfig).build();
    }


}