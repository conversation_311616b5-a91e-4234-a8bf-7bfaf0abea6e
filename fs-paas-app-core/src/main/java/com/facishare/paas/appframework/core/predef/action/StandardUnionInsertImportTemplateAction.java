package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Pair;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.util.ImportExportExt;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.ObjectReferenceManyFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.IOException;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.util.ImportExportExt.IMPORT_TYPE;

/**
 * 联合导入下载模板
 *
 * <AUTHOR>
 * @date 2019-04-25 16:25
 */
@Slf4j
public class StandardUnionInsertImportTemplateAction extends BaseInsertImportTemplateAction<BaseImportTemplateAction.Arg> {
    /**
     * 主对象
     */
    protected IObjectDescribe masterDescribe;
    /**
     * 从对象列表
     */
    protected List<IObjectDescribe> detailDescribeList = Lists.newArrayList();

    @Override
    protected void before(BaseImportTemplateAction.Arg arg) {
        super.before(arg);
        List<String> apiNames = arg.getUnionImportApiNameList();
        if (CollectionUtils.notEmpty(arg.getDetailArg())) {
            masterDescribe = serviceFacade.findObject(actionContext.getTenantId(), apiNames.get(0));
            List<String> detailApiNames = arg.getDetailArg().stream().map(DetailArg::getDetailApiName).collect(Collectors.toList());
            Map<String, IObjectDescribe> detailDescribes = serviceFacade.findObjects(actionContext.getTenantId(), detailApiNames);
            if (CollectionUtils.notEmpty(detailDescribes)) {
                List<IObjectDescribe> detailDescribeResult = Lists.newArrayList(detailDescribes.values());
                detailDescribeResult = CollectionUtils.sortByGivenOrder(detailDescribeResult, detailApiNames, IObjectDescribe::getApiName);
                detailDescribeList.addAll(detailDescribeResult);
            }
        } else {
            List<DetailArg> detailArgList = Lists.newArrayList();
            //主对象跳过
            for (int i = 0; i < apiNames.size(); i++) {
                if (i == 0) {
                    //主对象跳过
                    masterDescribe = serviceFacade.findObject(actionContext.getTenantId(), apiNames.get(0));
                    continue;
                }
                IObjectDescribe describe = serviceFacade.findObject(actionContext.getTenantId(), apiNames.get(i));
                detailDescribeList.add(describe);
                DetailArg detailArg = new DetailArg();
                detailArg.setDetailApiName(describe.getApiName());
                detailArg.setDetailRecordType(arg.getDetailRecordType());
                detailArgList.add(detailArg);
            }
            if (CollectionUtils.notEmpty(detailArgList)) {
                arg.setDetailArg(detailArgList);
            }
        }
    }

    @Override
    protected Result doAct(BaseImportTemplateAction.Arg arg) {
        // 主对象
        Workbook workbook = createWorkbookWithMasterObjectDescribe();
        // 从对象
        addSheetToWorkbookWithDetailDescribes(workbook);
        // 对sheet进行排序
        serviceFacade.orderSheets(workbook);
        String path = null;
        try {
            String ea = serviceFacade.getEAByEI(actionContext.getTenantId());
            path = serviceFacade.generatePath(workbook, ea, actionContext.getUser().getUserId());
        } catch (IOException e) {
            log.error("create template path error: tenantId:{}, userId:{}.", actionContext.getTenantId(),
                    actionContext.getUser().getUserId(), e);
            throw new MetaDataException(SystemErrorCode.METADATA_ERROR);
        }

        return Result.builder().path(path).build();
    }

    /**
     * 在workbook中生成从对象的sheet
     */
    private void addSheetToWorkbookWithDetailDescribes(Workbook workbook) {
        for (IObjectDescribe describe : detailDescribeList) {
            String detailRecordType;
            if (CollectionUtils.notEmpty(arg.getDetailArg())) {
                detailRecordType = arg.getDetailArg().stream()
                        .filter(x -> Objects.equals(x.getDetailApiName(), describe.getApiName()))
                        .map(DetailArg::getDetailRecordType)
                        .filter(Objects::nonNull)
                        .findFirst().orElse("");
            } else {
                detailRecordType = arg.getDetailRecordType();
            }
            List<IFieldDescribe> detailFieldList = Objects.equals(arg.getImportType(), IMPORT_TYPE_ADD) ?
                    infraServiceFacade.getTemplateField(actionContext.getUser(), describe, detailRecordType) :
                    infraServiceFacade.getUpdateImportTemplateField(actionContext.getUser(), describe, detailRecordType);
            // 联合导入暂时不支持相关团队导入
            filterRelevantTeam(detailFieldList);
            // 从对象支持业务类型下拉选项
            recordTypeOptionList = serviceFacade.findValidRecordTypeList(describe.getApiName(), actionContext.getUser());
            // 过滤需要导出的字段
            customHeader(detailFieldList);
            // 过滤从对象的"主从关系"或者指定字段
            customDetailHeader(detailFieldList);
            // 对字段排序
            List<IFieldDescribe> sortedFieldList = sortHeader(detailFieldList);
            // 补充多语字段
            ImportExportExt.supportMultiLangField(describe.getTenantId(), sortedFieldList, describe.getApiName());
            // 增加"关联标识"，位于第一列
            supportRelatedMark(sortedFieldList);
            //从对象增加"主对象ID"字段，位于最后一列
            supportUniqueID(sortedFieldList);
            // 支持按照唯一性ID导入
            supportID(sortedFieldList);    // exclude是主从关联的那个字段
            createAndFillWorkbook(sortedFieldList, describe, workbook);
        }
    }

    protected void supportUniqueID(List<IFieldDescribe> sortedFieldList) {
        ImportExportExt.supportUniqueID(sortedFieldList);
    }

    /**
     * 过滤从对象指定字段
     */
    protected void customDetailHeader(List<IFieldDescribe> detailFieldList) {
        ListIterator<IFieldDescribe> lit = detailFieldList.listIterator();
        while (lit.hasNext()) {
            IFieldDescribe field = lit.next();
            if (IFieldType.MASTER_DETAIL.equals(field.getType())) {
                //"主从关系"字段至多有一个
                lit.remove();
            } else if (IFieldType.OBJECT_REFERENCE.equals(field.getType())) {
                // 关联关系
                ObjectReferenceFieldDescribe f = (ObjectReferenceFieldDescribe) field;
                if (f.getTargetApiName().equals(objectDescribe.getApiName())) {
                    lit.remove();
                }
            } else if (IFieldType.OBJECT_REFERENCE_MANY.equals(field.getType())) {
                ObjectReferenceManyFieldDescribe f = (ObjectReferenceManyFieldDescribe) field;
                if (f.getTargetApiName().equals(objectDescribe.getApiName())) {
                    lit.remove();
                }
            }
        }
    }


    /**
     * 生成workbook，只包含主对象sheet
     */
    private Workbook createWorkbookWithMasterObjectDescribe() {
        List<IFieldDescribe> masterFieldList = Objects.equals(arg.getImportType(), IMPORT_TYPE_ADD) ?
                infraServiceFacade.getTemplateField(actionContext.getUser(), masterDescribe, arg.getRecordType()) :
                infraServiceFacade.getUpdateImportTemplateField(actionContext.getUser(), masterDescribe, arg.getRecordType());
        // 联合导入暂时不支持相关团队导入
        filterRelevantTeam(masterFieldList);
        // 处理业务类型选项，以支持主对象业务类型下拉选项
        recordTypeOptionList = serviceFacade.findValidRecordTypeList(masterDescribe.getApiName(), actionContext.getUser());
        // 过滤需要导出的字段
        customHeader(masterFieldList);
        // 定制主对象字段处理
        customMasterHeader(masterFieldList);
        // 对字段排序
        List<IFieldDescribe> sortedFieldList = sortHeader(masterFieldList);
        // 补充多语字段
        ImportExportExt.supportMultiLangField(actionContext.getTenantId(), sortedFieldList, masterDescribe.getApiName());
        // 增加"关联标识"，位于第一列
        supportRelatedMark(sortedFieldList);
        // 支持按照唯一性ID导入
        supportID(sortedFieldList);
        return createAndFillWorkbook(sortedFieldList, masterDescribe, null);
    }

    protected void supportRelatedMark(List<IFieldDescribe> sortedFieldList) {
        ImportExportExt.supportRelatedMark(sortedFieldList);
    }

    /**
     * 补充唯一性ID列
     *
     * @param sortedFieldList 原字段列表
     */
    protected void supportID(List<IFieldDescribe> sortedFieldList) {
        handleIDAndReferenceField(sortedFieldList);
    }

    protected void customMasterHeader(List<IFieldDescribe> masterFieldList) {
    }

    /**
     * 生成workbook
     */
    protected Workbook createAndFillWorkbook(List<IFieldDescribe> sortedFieldList, IObjectDescribe describe, Workbook workbook) {
        //key: 标题名称， value: 字段类型
        List<Pair<String, IFieldDescribe>> labelList = Lists.newArrayList();
        List<List<String>> sampleRows = Lists.newArrayList();
        List<String> sampleList = Lists.newArrayList();
        for (IFieldDescribe field : sortedFieldList) {
            //业务类型字段改为必填
            if (field.getType().equals(IFieldType.RECORD_TYPE)) {
                //业务类型只有一个选项模版不下发
                RecordTypeFieldDescribe recordTypeFieldDescribe = (RecordTypeFieldDescribe) field;
                if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.IMPORT_RECORD_TYPE, actionContext.getTenantId())
                        && recordTypeFieldDescribe.getRecordTypeOptions().size() == 1) {
                    continue;
                }
                field.setRequired(Boolean.TRUE);
            }
            String sampleValue = getFieldSampleValue(field);
            // 从对象模板中，主对象ID字段不填值
            // 关联标识提示
            if (Objects.nonNull(field.getExtendInfo())) {
                if (Objects.equals(ImportExportExt.UNION_IMPORT_ID_MARK, field.getExtendInfo().get(IMPORT_TYPE))) {
                    sampleValue = "";
                }
                if (Objects.equals(ImportExportExt.EXT_INFO_RELATED_MARK, field.getExtendInfo().get(IMPORT_TYPE))) {
                    sampleValue = I18N.text(I18NKey.IMPORT_RELATED_MARK);
                }
            }
            sampleList.add(sampleValue);

            String title = field.getLabel();
            if (IFieldType.PERCENTILE.equals(field.getType())) {
                //百分比类型要在表头加（%）
                title = title + PERCENTAGE_SYMBOL;
            }
            // ID字段不加"（必填）"符号
            if (Objects.equals(Boolean.TRUE, field.isRequired())) {
                title = title + I18N.text(REQUIRED_SYMBOL);
            }
            labelList.add(Pair.of(title, field));
        }
        sampleRows.add(sampleList);
        sampleRows = customSampleList(sampleRows);

        return serviceFacade.generateUnionTemplate(actionContext.getUser(), describe.getDisplayName(), labelList, sampleRows, workbook);
    }

    /**
     * 联合导入不支持相关团队
     */
    private void filterRelevantTeam(List<IFieldDescribe> fieldDescribeList) {
        fieldDescribeList.removeIf(f -> {
            Map map = f.getExtendInfo();
            return Objects.nonNull(map) && Objects.equals(map.get(ImportExportExt.IMPORT_TYPE), ImportExportExt.TEAM_MEMBER_MARK);
        });
    }
}
