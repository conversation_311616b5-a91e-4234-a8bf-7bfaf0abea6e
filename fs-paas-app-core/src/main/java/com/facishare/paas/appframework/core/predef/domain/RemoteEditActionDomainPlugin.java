package com.facishare.paas.appframework.core.predef.domain;

import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.domain.DomainProvider;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created by zhouweirong on 2021/10/18.
 */
@Component
@DomainProvider(name = "_remote_Edit")
public class RemoteEditActionDomainPlugin implements EditActionDomainPlugin {

    @Autowired
    private EditActionDomainPluginProxy proxy;

    @Override
    public Result before(ActionContext context, Arg arg) {
        return doPost(context, arg, BEFORE);
    }

    @Override
    public Result preAct(ActionContext context, Arg arg) {
        return doPost(context, arg, PRE_ACT);
    }

    @Override
    public Result postAct(ActionContext context, Arg arg) {
        return doPost(context, arg, POST_ACT);
    }

    @Override
    public Result after(ActionContext context, Arg arg) {
        return doPost(context, arg, AFTER);
    }

    @Override
    public Result finallyDo(ActionContext context, Arg arg) {
        return doPost(context, arg, FINALLY_DO);
    }

    private Result doPost(ActionContext context, Arg arg, String method) {
        Map<String, String> header = RestUtils.buildHeaders(context.getUser());
        String url = arg.getPluginDescribe().getRestApiUrl(method);
        if (Strings.isNullOrEmpty(url)) {
            return new Result();
        }
        return proxy.post(url, arg, header).getData();
    }
}
