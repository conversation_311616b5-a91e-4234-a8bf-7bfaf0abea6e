package com.facishare.paas.appframework.core.util;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/10/26
 */
public interface UdobjGrayConfigKey {

    String UDOBJ = "udobj";

    String DEFAULT = "default";

    String OUT_USER_NULL_GRAY = "outUser.null.gray";

    String TEST_TRANSLATE_ERROR = "paas.udobj.translate_fail";
    /**
     * 优化增量更新时，待更新的数据不包含查重规则中的字段,不需要调用查重服务
     */
    String INCREMENT_UPDATE_DUPLICATE_SEARCH = "increment_update_duplicate_search";

    /**
     * 聚合查询非count场景字段类型检查，只允许数字金额字段
     */
    String AGGREGATE_QUERY_FIELD_TYPE_CHECK = "aggregate_query_field_type_check";

    String OUT_USER_FILL_DEPT_ORG = "out_user_fill_dept_org";

    String FIELD_GROUP_EARTH_TRANS = "field_group_earth_trans";

    String IMPORT_VALIDATE_FUNCTION_SUPPORT_RELEVANT_TEAM = "import_validate_function_support_relevant_team_field";

    String SAVE_TEMP_FILE_FILE_NAME = "save_temp_file_file_name";

    String TIME_ZONE_NO_CONVERT_GRAY_EI = "time_zone_no_convert_gray_ei";

    //允许下游人员新建数据时指定负责人的企业
    String ALLOW_OWNER_FROM_OUT_USER_IN_ADD_ACTION_EI = "allow_owner_from_out_user_in_add_action_ei";

    // 下游互联用户创建对象数据时，对象数据负责人（owner_id）获取
    String OUT_USER_CREATE_DATA_OWNER_ID_CALC_EI = "out_user_create_data_owner_id_calc_ei";

    /**
     * 字段类型资源的灰度
     */
    String WEB_DETAIL_LAYOUT_FIELD_GRAY = "webDetailLayoutFieldGray";

    /**
     * 关联按钮支持函数过滤的灰度
     */
    String RELATED_WHERE_FUNCTION_FILTER_GRAY_EI = "related_where_function_filter_gray_ei";

    /**
     * 选项集灰度
     */
    String OPTION_SET_GRAY_EI = "option_set_gray_ei";

    /**
     * List接口 真 select 部分字段
     */
    String LIST_SELECT_PARTLY_GRAY_EI = "list_select_partly_gray_ei";

    /**
     * 对象列表灰度放开部门对象
     */
    String OBJECT_LIST_SUPPORT_DEPARTMENT_GRAY_EI = "object_list_support_department_gray_ei";

    /**
     * 描述拓展灰度
     */
    String DESCRIBE_EXTRA_GRAY_EI = "describe_extra_gray_ei";

    //支持1000个字段的企业id
    String SUPPORT_THOUSAND_FIELDS_EI = "support_thousand_fields_ei";

    //支持1500个字段的企业id
    String SUPPORT_1500_FIELDS_EI = "support_1500_fields_ei";

    String FILL_DEPT_AND_ORG = "fill_dept_and_org";
    /**
     * 联合导入触发审批流程
     */
    String UNION_INSERT_IMPORT_TRIGGER_APPROVAL_FLOW = "union_insert_import_trigger_approval_flow";

    /**
     * 部门名称支持可重复
     */
    String DEPARTMENT_SUPPORT_REPEAT = "department_support_repeat";

    //在布局设计器中支持外部人员字段类型的企业ids
    String SUPPORT_OUT_EMPLOYEE_FIELD_TYPE_IN_LAYOUT_RESOURCE_EI = "support_out_employee_field_type_in_layout_resource_ei";

    String IMPORT_VALIDATE_FUNC_ARG_DOCUMENT = "import_validate_func_arg_document";

    //支持移动端布局详情页配置三流组件的企业
    String SUPPORT_FLOW_COMPONENTS_IN_MOBILE_LAYOUT = "support_flow_components_in_mobile_layout";

    String UNIQUE_RULE_KEY = "unique_rule_key";

    /**
     * 全局搜索时是否排除从对象
     */
    String EXCLUDE_DETAIL_IN_SEARCH = "exclude_detail_in_search";
    String H5_REQUEST_SOURCE = "h5_request_source";

    //计算接口针对从对象删除以后计算错误走优化代码的企业id
    String BATCH_CALCULATE_DEL_DETAIL_FIX_EI = "batch_calculate_del_detail_fix_ei";

    //允许通过接口更新计算字段的灰度对象
    String ALLOW_UPDATE_FORMULA_GRAY_OBJECTS = "allow_update_formula_gray_objects";
    String THREAD_LOCAL_CACHE_GRAY = "thread_local_cache_gray";

    //在EditAction中不更新未变化的从对象的灰度企业
    String SKIP_NO_CHANGE_DETAILS_IN_EDIT_ACTION_EI = "skip_no_change_details_in_edit_action_ei";

    String FIELD_ALIGN = "gray_field_align";

    String EXPORT_FILE_NAME_SUPPORT_RENAME = "export_file_name_support_rename";

    String AUTO_NUMBER_LOCK_GRAY = "auto_number_lock_gray_2";
    // 列表页布局灰度组件
    String LIST_LAYOUT_COMPONENT = "gray_list_layout_component";

    String SUPPORT_BATCH_UPDATE_SUPERIOR_FIELD_GRAY_EI = "support_batch_update_superior_field_gray_ei";

    String UPDATE_IMPORT_SUPPORT_OWNER = "update_import_support_owner";
    String EFFECTIVE_FILTER = "is_effective_filter_gray_ei";

    String TRIGGER_REMOTE_ACTION_GRAY_OBJECT = "trigger_remote_action_gray_object";

    String NOT_NEED_DIFF_SYSTEM_FIELD_NAME = "not_need_diff_system_field_name";
    String IMPORT_INCREMENT_UPDATE_FIELD = "import_Increment_Update_field";
    String IMPORT_INCREMENT_UPDATE_FIELD_GRAY = "import_increment_update_field_gray";

    //更新数据的接口在查询数据时不查询相关团队的企业id
    String SKIP_QUERY_RELATE_TEAM_WHEN_UPDATE_EI = "skip_query_relate_team_when_update_ei";

    //查数据时不拷贝对象描述的企业id
    String NOT_COPY_DESCRIBE_WHEN_QUERY_DATA_EI = "not_copy_describe_when_query_data_ei";

    String OPTION_DEPENDENCE_GRAY_EI = "option_dependence_gray_ei";

    String DUPLICATED_SEARCH_EMPTY_POLICY = "duplicated_search_empty_policy";
    String DUPLICATED_SEARCH_REDIS_TEMP_KEY_PRE_GRAY = "duplicated_search_redis_temp_key_pre_gray";
    String DUPLICATED_SEARCH_REDIS_LUA_LOG_GRAY = "duplicated_search_redis_lua_log_gray";

    // 根据 fs-device-type 判断h5请求是否来自 mobile
    String DEVICE_TYPE_GRAY_EI = "device_type_gray_ei";
    String EXPORT_SUPPORT_WHAT_FIELD = "export_support_what_field";

    // 支持发送附件按钮的对象
    String SUPPORT_SEND_ATTACHMENT_BUTTON_OBJECT = "support_send_attachment_button_object";
    // 支持以模板发送按钮的对象
    String SUPPORT_SEND_AS_TEMPLATE_BUTTON_OBJECT = "support_send_as_template_button_object";

    // 不需要校验license(筛选支持或)的对象
    String NO_NEED_CHECK_OBJECT_OF_CUSTOM_OBJECT_LIST_FILTERS_OR_APP_LICENSE = "no_need_check_object_of_custom_object_list_filters_or_app_license";

    //导入模板优化
    String IMPORT_TEMPLATE_ORDER_EI = "import_template_order_ei";
    //灰度按数据过滤按钮的远程调用灰度
    String FILTER_BUTTONS_BY_DATA_GRAY_REMOTE_OBJECT = "filter_buttons_by_data_gray_remote_object";

    //在Controller接口中不拷贝对象描述的企业id
    String NOT_COPY_DESCRIBE_IN_CONTROLLER_EI = "not_copy_describe_in_controller_ei";

    //在Controller接口中不拷贝对象描述的对象apiName
    String NOT_COPY_DESCRIBE_IN_CONTROLLER_OBJ = "not_copy_describe_in_controller_obj";

    //在Action接口中不拷贝对象描述的企业id
    String NOT_COPY_DESCRIBE_IN_ACTION_EI = "not_copy_describe_in_action_ei";

    //在Action接口中不拷贝对象描述的对象apiName
    String NOT_COPY_DESCRIBE_IN_ACTION_OBJ = "not_copy_describe_in_action_obj";

    //在内部代码中不拷贝对象描述的企业id
    String NOT_COPY_DESCRIBE_IN_INNER_METHOD_EI = "not_copy_describe_in_inner_method_ei";

    // 可选功能开关灰度企业
    String OPTIONAL_FEATURES_SWITCH_EI = "optional_features_switch_ei";
    String ENTERPRISE_RELATION_SUPPORT_TEAM_MEMBER = "enterprise_relation_support_team_member";

    // 对象多语字段的灰度
    String OBJECT_MULTI_LANG_GRAY = "object_multi_lang_gray";

    String TEMPLATE_FIND_BY_ID_GRAY = "template_find_by_id_gray";

    // 功能权限支持数据权限的灰度
    String EDIT_AND_VIEW_ALL_DATA_PRIVILEGE = "edit_and_view_all_data_privilege_gray";

    String FUNC_EXECUTE_CONTEXT_GRAY = "func_execute_context_gray";

    // 下游移动端新建编辑页ui按钮灰度
    String OUTER_MOBILE_EDIT_UI_ACTION_GRAY = "outer_mobile_edit_ui_action_gray";
    // 优先走es的灰度
    String ES_REDIS_RECENT_UPDATE_CHECK_GRAY = "es_redis_recent_update_check_gray";

    String SUBMIT_RETURN_DATA = "submit_return_data_gray";

    //主从一起新建/编辑删除从对象没有计算主对象的lookup对象的统计字段的bugfix灰度
    String MASTER_LOOKUP_COUNT_CALCULATE_WHEN_DELETE_DETAIL_FIX_GRAY = "master_lookup_count_calculate_when_delete_detail_fix_gray";

    String TENANT_SCENE_CREATE_FIELD_LIST_CONFIG_GRAY = "tenant_scene_create_field_list_config_gray";

    String SUPPORT_CURRENT_LOGIN_USER_VARIABLE_GRAY = "support_current_login_user_variable_gray";

    String IMPORT_LOCATION_FIELD_GRAY = "import_location_field_gray";

    String IMPORT_SUPPORT_FILE_GRAY = "import_support_file_gray";

    String SCENE_COPY_WHERE_CONDITIONS_GRAY_OBJECT = "scene_copy_where_conditions_gray_object";

    String FUNCTION_VALUE_CONSISTENT_GRAY_EI = "function_value_consistent_gray_ei";

    String MANAGE_GROUP_GRAY_EI = "manage_group_gray_ei";

    String MANAGE_GROUP_GRAY = "manage_group_gray";

    String H5_UI_PAAS_ACTION_GRAY_EI = "h5_ui_paas_action_gray_ei";

    String SOCIAL_OBJ_GRAY_EI = "social_obj_gray_ei";

    String IMPORT_SUPPORT_COUNTRY_AREA_FORM_DB = "import_support_country_area_form_db";
    /**
     * 不支持配置支付组件的企业
     */
    String UN_SUPPORT_PAYMENT_GROUP_GRAY_EI = "un_support_payment_group_gray_ei";
    String VALIDATE_LOOKUP_FIELD_ONLY_CHANGED_GRAY_EI = "validate_lookup_field_only_changed_gray_ei";
    String MULTI_TIME_ZONE_UN_SHOW_TIME_ZONE_INFO = "multi_time_zone_un_show_time_zone_info";

    String EXCEL_IMPORT_NORMAL_BUTTON_GRAY = "excel_import_normal_button_gray";
    /**
     * 日期范围组件灰度企业
     */
    String DATE_TIME_RANGE_GRAY_EI = "date_time_range_gray_ei";
    String H5_MOBILE_REQUEST_LOGIC_GRAY = "h5_mobile_request_logic_gray";
    //快速编辑页面字段按照布局里的字段顺序排序的灰度企业
    String ORDER_FIELDS_BY_LAYOUT_IN_QUICK_EDIT_PAGE = "order_fields_by_layout_in_quick_edit_page";

    /**
     * bi 筛选器组件渲染灰度
     */
    String BI_FILTER_COMPONENT = "bi_filter_component";
    String BI_FILTER_COMPONENT_GRAY = "bi_filter_component_gray";
    String BI_FILTER_FILTER_TRANSFER_COMPONENT_GRAY = "bi_filter_filter_transfer_component_gray";

    String PHONE_NUMBER_INFO_V2 = "phone_number_info_v2";

    /**
     * 联合导入完成后触发工作流程
     */
    String UNION_INSERT_IMPORT_COMPLETE_TRIGGER_WORK_FLOW_GRAY = "union_insert_import_complete_trigger_work_flow";

    //通过函数新建数据时先处理计算字段再入库
    String CALCULATE_FORMULA_FOR_FUNCTION_CREATE = "calculate_formula_for_function_create";

    /**
     * 表达式校验明细提醒灰度
     */
    String EXPRESSION_CHECK_DETAIL_REMIND_GRAY = "expression_check_detail_remind_gray";

    String DUPLICATE_RULE_SUPPORT_DATA_SCOPE = "duplicate_rule_support_data_scope";
    /**
     * web详情页,下发三流组件
     */
    String SUPPORT_FLOW_COMPONENTS_IN_WEB_LAYOUT = "support_flow_components_in_web_layout";
    String UN_SUPPORT_FLOW_COMPONENTS_OBJ = "un_support_flow_components_obj";

    /**
     * 选项集变更灰度聚合框架
     */
    String OPTION_SET_CHANGE_GRAY_DISPATCHER = "option_set_change_gray_dispatcher_gray";

    String MULTI_DUPLICATE_RULE_SUPPORT_PRE_OBJ_GRAY = "multi_duplicate_rule_support_pre_obj_gray";
    // 转换规则所有对象都放开灰度企业
    String CONVERT_RULE_SUPPORT_ALL_OBJ_GRAY = "convert_rule_support_all_obj_gray";

    String FLOW_BUTTON_GRAY = "flow_start_bpm_start_stage_propellor_button_gray";

    //新建导入数据时计算默认值
    String CALCULATE_DEFAULT_VALUE_FOR_INSERT_IMPORT = "calculateDefaultValueForInsertImport";
    //UI事件支持计算从对象的默认值
    String CALCULATE_DETAIL_OBJECT_IN_UI_EVENT_ACTION = "calculateDetailObjectInUIEventAction";

    // 受管控配置包
    String CONFIGURATION_PACKAGE = "configuration_package";
    // 转换规则灰度名单
    String CONVERT_RULE_EI_GRAY = "convert_rule_ei_gray";
    // 按钮入参中的where条件，支持使用函数
    String BUTTON_PARAM_FUNCTION_WHERES_GRAY = "button_param_function_wheres_gray";
    // 按钮入参中的where条件，支持三角函数
    String BUTTON_PARAM_REF_WHERES_GRAY = "button_param_ref_wheres_gray";
    // 不需要补充归属部门的对象
    String NOT_NEED_FILL_DATA_OWN_DEPT_OBJ = "not_need_fill_data_own_dept_obj";
    // 计算默认值
    String CALCULATE_DEFAULT_VALUE_AND_REPORT_LOG = "calculate_default_value_and_report_log";
    String CALCULATE_DEFAULT_VALUE_AND_REPORT_LOG_FOR_UI_EVENT = "calculate_default_value_and_report_log_for_ui_event";
    // 不支持标签筛选灰度配置
    String NOT_SUPPORT_TAG_FILTER_GRAY_EI = "not_support_tag_filter_gray_ei";
    // #灰度引用部门类型字段的计算字段支持筛选的企业
    String GRAY_FILTER_BY_DEPARTMENT_FORMULA_TENANTS = "grayFilterByDepartmentFormulaTenants";
    // 协同富文本灰度配置
    String RICH_TEXT_GRAY_EI = "rich_text_gray_ei";
    // 自定义团队角色灰度企业
    String CUSTOM_TEAM_ROLE_GRAY_EI = "custom_team_role_gray_ei";

    // 选数据列表支持全字段搜索
    String SELECT_DATA_GRAY_EI = "select_data_gray_ei";
    // 列表支持全字段搜索
    String LIST_DATA_GRAY_EI = "list_data_gray_ei";

    String BATCH_CALCULATE = "batch_calculate";
    // 计算指定字段
    String CALCULATE_PROJECTION_FIELDS = "calculate_projection_fields";
    //是否校验ChangeOwner接口的数据个数
    String VALIDATE_CHANGE_OWNER_DATA_NUM = "validateChangeOwnerDataNum";
    // 校验恢复按钮的功能权限
    String RECOVER_FUN_PRIVILEGE_GRAY = "recover_fun_privilege_gray";
    //新建编辑同步更新从对象生命状态的企业
    String SYNC_UPDATE_DETAIL_OBJECT_DATA_LIFE_STATUS = "syncUpdateDetailObjectDataLifeStatus";
    //批量删除数据接口限制
    String BULK_DELETE_LIMIT_GRAY = "bulk_delete_limit_gray";
    //清理百分比、数字、金额类型引用字段的小数位末尾0
    String STRIP_TRAILING_ZEROS_FOR_NUMERIC_QUOTE_FIELD = "stripTrailingZerosForNumericQuoteField";
    //编辑校验数据
    String BATCH_VALIDATE_EDIT_DATA_CONSTRAINT_BY_FIELDS = "batchValidateEditDataConstraintByFields";
    // 移动端按钮混排灰度
    String MOBILE_BUTTON_MIX_GRAY = "mobile_button_mix_gray";
    /**
     * 数据归属部门字段根据部门类型进行过滤
     */
    String DATA_OWN_DEPARTMENT_FILTER_BY_DEPT_TYPE = "data_own_department_filter_by_dept_type";
    String DEPT_TEAM_MEMBER_REMIND_GRAY = "dept_team_member_remind_gray";
    String NOT_SUPPORT_OR_FILTER_WHEN_HAS_PATTERN_GRAY = "not_support_or_filter_when_has_pattern_gray";

    // 流程完成回调接口支持统计计算企业灰度
    String FLOW_COMPLETED_SUPPORT_SYNC_CALCULATE_EI = "flow_completed_support_sync_calculate_ei";

    // 图片转换接口调整灰度企业
    String UNIFIED_IMAGE_CONVERSION_EI = "unified_image_conversion_ei";

    /**
     * 字段列表 -> 查询引用
     * 查询字段用于何处？
     */
    String FIND_WHERE_A_FIELD_IS_USED_GRAY_KEY = "find_where_a_field_is_used";

    String SYNC_CHANGE_ORDER_MASTER_DETAIL_FIELD_GRAY = "sync_change_order_master_detail_field_gray";
    String CALCULATE_FOR_UPDATE = "calculate_for_update";
    String CALCULATE_FOR_CREATE = "calculate_for_create";
    //计算公式不允许使用最后修改时间字段的灰度企业
    String FILTER_LAST_MODIFIED_TIME_FIELD_FOR_FORMULA = "filter_last_modified_time_field_for_formula";

    String FUNCTION_BATCH_UPDATE_SKIP_CALCULATE_FIELD = "function_batch_update_skip_calculate_field";

    String REPORT_DUPLICATE_GRAY_EI = "report_duplicate_gray_ei";

    /**
     * 维度名称支持可重复
     */
    String DIMENSION_SUPPORT_REPEAT = "dimension_support_repeat";
    String CUSTOM_TEAM_TYPE_GRAY_EI = "custom_team_type_gray_ei";

    /**
     * 流程待办布局灰度
     */
    String FLOW_TASK_LAYOUT_GRAY = "flow_task_layout_gray";

    String PUBLIC_DATA_FLAG_GRAY = "public_data_flag_gray";

    //支持按对象灰度自动合并数据冲突的企业id
    String DATA_CONFLICT_MERGE_OBJECTS_GRAY_EI = "data_conflict_merge_objects_gray_ei";
    //支持自动合并数据冲突的对象apiName
    String DATA_CONFLICT_MERGE_OBJECTS_GRAY = "data_conflict_merge_objects_gray";

    String OUT_OWNER_GRAY = "out_owner_gray";

    //AiUserLicense灰度开关
    String AI_USER_LICENSE_GRAY = "ai_user_license_gray";

    /**
     * 人员名称支持可重复
     */
    String EMPLOYEE_SUPPORT_REPEAT = "employee_support_repeat";
    // 高级开关不可编对象辑黑名单
    String OBJECT_OPTIONAL_FEATURES_DISABLED = "object_optional_features_disabled";

    String GRAY_BUILD_UNIQUE_KEY_SET = "gray_build_unique_key_set";

    String CARD_TEMPLATE_GRAY_EI = "card_template_gray_ei";

    String CHECK_OUT_USER_DATA_PRIVILEGE_GRAY = "check_out_user_data_privilege_gray";
    String SEARCH_TEMPLATE_QUERY_RECORD_TYPE_GRAY = "search_template_query_record_type_gray";
    String SENTINEL_GRAY = "sentinel_gray";

    //在线文档db切换灰度
    String PACKAGE_PLUGIN_DB = "PACKAGE_PLUGIN_DB";
    String DEFAULT_ENABLE_EDIT_PAGE_LAYOUT_OBJECT_GRAY = "default_enable_edit_page_layout_object_gray";

    //编辑页支持修改币种的企业
    String SUPPORT_EDIT_CURRENCY_EI = "support_edit_currency_ei";
    // 默认值支持配置负责人
    String DEFAULT_VALUE_SUPPORT_OWNER_GRAY = "default_value_support_owner_gray";

    String FILTER_COMPONENTS_BY_FUNCTION_CODE_GRAY = "filter_components_by_function_code_gray_ei";
    String BUTTON_VALIDATE_RULE_GRAY_EI = "button_validate_rule_gray_ei";

    // 企信卡片消息查询移动端摘要布局，优先使用 FieldSection 覆盖 IncludeFields
    String LAYOUT_SYNC_FIELD_SECTION_TO_INCLUDE_FIELD_GRAY = "layout_sync_field_section_to_include_field_gray_ei";

    String LIST_LAYOUT_GRAY = "list_layout_gray_rule";

    //查询数据时校验filter总数和单个filter的field_value数量的企业id
    String VALIDATE_FILTER_NUM_AND_FIELD_VALUE_NUM_GRAY_EI = "validate_filter_num_and_field_value_num_gray_ei";

    // 删除wheres中禁用的字段
    String REMOVE_WHERES_UN_ACTIVE_FILTER_FIELD = "remove_wheres_un_active_filter_field";
    String UN_REMOVE_WHERES_UN_ACTIVE_FILTER_FIELD_OBJECT = "un_remove_wheres_un_active_filter_field_object";
    String GRAY_REMOVE_MASTER_AND_NATIVE_FILTER = "grayRemoveMasterAndNativeFilter";

    String FIND_CUSTOMER_WIDGET_LOCAL_CONFIG_GRAY = "find_customer_widget_local_config_gray";

    /**
     * 字段表达式校验跳过灰度企业
     */
    String SKIP_FIELD_EXPRESSION_VALIDATION_GRAY = "skip_field_expression_validation_gray";

    /**
     * 预设对象支持更换图标
     */
    String CHANGE_ICON_PRESET_OBJECT_GRAY = "change_icon_preset_object_gray";

    String BUTTON_CLICK_RECORD_EI = "button_click_record_modification_record_ei";
    // 相关列表页按钮支持外露
    String RELATED_LIST_SINGLE_EXPOSED_GRAY = "related_list_single_exposed_gray";
    String BUSINESS_COMPONENT_TRANSLATE = "business_component_translate";

    // 详情页性能优化的部分灰度
    String FORM_COMPONENT_RELATED_FIELD_DISPLAY_NAME_GRAY = "form_component_related_field_display_name_gray";
    String DESCRIBE_EXPANSION_FILL_BIG_OBJECT_FLAG_GRAY = "describe_expansion_fill_big_object_flag_gray";
    String SELECT_FIELD_DEPENDENCE_LOCAL_CACHE_GRAY = "select_field_dependence_local_cache_gray";
    String DETAIL_ASYNC_FILL_FIELD_INFO_GRAY = "detail_async_fill_field_info_gray";
    String DETAIL_SKIP_RELEVANT_TEAM_GRAY = "detail_skip_relevant_team_gray";
    String FORM_COMPONENT_RESET_WHEN_CHANGE_GRAY = "form_component_reset_when_change_gray";
    String CUSTOM_BUTTON_FIND_DESCRIBE_EXTRA_GRAY = "custom_button_find_describe_extra_gray";
    String CUSTOM_BUTTON_FILTER_FUN_PRIVILEGE_GRAY = "custom_button_filter_fun_privilege_gray";
    String ADD_TEAM_MEMBER_LIMIT_GRAY = "add_team_member_limit_gray";

    //计算统计字段走pg查询的灰度
    String CALCULATE_COUNT_FIELD_FROM_DB = "calculate_count_field_from_db";

    String IMPORT_RECORD_TYPE = "import_record_type";

    String SUPPORT_CALCULATION_PROGRESS = "support_calculation_progress";
    String SUPPORT_CALCULATION_START_TIME = "support_calculation_start_time";

    String FILE_EQUAL_WITH_FILE_NAME_GRAY = "file_equal_with_file_name_gray";

    String IGNORE_GOOGLE_MAP_LICENSE_GRAY = "ignore_google_map_license_gray";

    //只有cep请求查询手机归属地的灰度
    String QUERY_PHONE_INFORMATION_ONLY_CEP = "query_phone_information_only_cep";

    String INVITATION_MESSAGE_SUPPORT_I18N_GRAY = "invitation_message_support_i18n_gray";
    String FIND_ROLE_CODE_BY_APP_ID_GRAY = "find_role_code_by_app_id_gray";

    String CREATE_OPTION_REFERENCE_BY_CHILD_DESCRIBE_GRAY = "create_option_reference_by_child_describe_gray";

    String REMOVE_TEMPORARY_ID_BY_UI_EVENT_COMPUTE_GRAY = "remove_temporary_id_by_ui_event_compute_gray";
    String QUOTE_SUPPORT_MULTI_LANG = "quote_support_multi_lang";

    /**
     * 附件字段根据 signature 生成 signedUrl 灰度
     */
    String FILE_ATTACHMENT_SIGNED_URL_GRAY = "file_attachment_signed_url_gray";
    String ADD_AND_EDIT_FILL_MULTI_LANG__R = "add_and_edit_fill_multi_lang__r";

    /**
     * 对象数据执行计算逻辑后渲染模版灰度
     */
    String OBJ_DATA_CALC_FOR_RENDER_TEMPLATE_GRAY = "calc_for_render_template_gray";

    String OBJECT_LIST_MANAGE_SOURCE_INFO_DEFINE_GRAY = "object_list_manage_source_info_define_gray";

    /**
     * 给函数提供的对象映射接口，查询从对象数据按 order_by 排序
     */
    String OBJECT_MAPPING_FIND_DETAIL_DATA_ORDER_BY_GRAY = "object_mapping_find_detail_data_order_by_gray";

    String FILTER_LIST_BUTTON_GRAY = "filter_list_button_gray";

    String SALE_LOG_COMPONENT_GRAY = "sale_log_component_gray";

    String PARALLEL_PROCESS_DATA_TEMP_FILE_GRAY = "parallel_process_data_temp_file_gray";

    String FROM_COMPONENT_RENDER_REMOVE_FIELDS_GRAY = "from_component_render_remove_fields_gray";
    String USER_DEFAULT_ROLE_CODE_LOCAL_CACHE_GRAY = "user_default_role_code_local_cache_gray";
    String GET_FUNCTIONAL_CURRENCY_CODE_LOCAL_CACHE_GRAY = "get_functional_currency_code_local_cache_gray";
    String CHECK_HAS_FIELD_DEPENDENCE_GRAY = "check_has_field_dependence_gray";
    String DESCRIBE_LAYOUT_FIND_DATA_IGNORE_RELEVANT_TEAM_GRAY = "describe_layout_find_data_ignore_relevant_team_gray";
    String FIELD_NAME_RECORD_TYPE_FIELD_PACKET = "field_name_record_type_field_packet";
    String BACK_FILL_COUNTRY_PROVINCE_CITY_DISTRICT = "back_fill_country_province_city_district";
    String ENCRYPT_FILE_SIGNATURE_EI = "encrypt_file_signature_ei";

    String SYNCHRONIZE_DATA_GRAY = "synchronize_data_gray";
    String SET_FIELD_PUBLIC_FLAG_BEFORE_ADD = "set_field_public_flag_before_add_gray";
    String FIELD_RELATION_GRAPH_GRAY = "field_relation_graph_gray";
    String FIELD_RELATION_VALIDATOR_GRAY = "field_relation_validator_gray";

    String OUTER_IMPORT_SUPPORT_OUT_OWNER_GRAY = "outer_import_support_out_owner_gray";

    /**
     * 自定义字段强制设置为私有字段的灰度开关
     */
    String FORCE_CUSTOM_FIELD_TO_PRIVATE = "force_custom_field_to_private_gray";

    // 互联部门补充填充到相关团队中
    String INTERCONNECT_DEPT_TEAM_MEMBER_GRAY = "interconnect_dept_team_member_gray";
    // 计算时查询数据忽略所有额外信息
    String CALCULATE_IGNORE_ALL_AND_EXTRA_INFO = "calculate_ignore_all_and_extra_info";

    String SUPPORT_CROSS_OBJECT_GRAY = "support_cross_object_gray";
    String OPEN_SIDEBAR_LAYOUT_GRAY = "open_sidebar_layout_gray";
    String OPEN_SIDEBAR_LAYOUT_BLACK_OBJ = "open_sidebar_layout_black_obj";

    String OUTER_USER_CHART_ACCESS_GRAY = "outer_user_chart_access_gray_ei";

    // 编辑变更单对象的灰度
    String RE_CHANGE_ORDER_GRAY = "re_change_order_gray";
    // 管理后台 对象 tab "扩展"
    String OBJECT_EXTENSION = "function_plugin_conf";

    // 发邮件按钮升级为标准按钮
    String SEND_EMAIL_EI = "send_email_ei";

    String FILE_PATH_EXTENSION_GRAY = "file_path_extension_gray";

    // 描述布局变更不走polling实时推送的企业id
    String NO_REALTIME_DESCRIBE_LAYOUT_CHANGE_POLLING_EI = "no_realtime_describe_layout_change_polling_ei";
    String DUPLICATED_SEARCH_SUPPORT_DISTANCE = "duplicated_search_support_distance";
    String DUPLICATED_SEARCH_NOT_USE_REDIS = "duplicated_search_not_use_redis";

    String APPROVAL_OR_LOCK_WHITE_LIST_PEOPLE_GRAY = "approval_or_lock_white_list_people_gray";

    String CREATOR_TO_TEAM_MEMBER_QUERY_CONFIG_GRAY = "creator_to_team_member_query_config_gray";

    String STAT_ON_EMPTY_RESULT_GRAY = "stat_on_empty_result_gray_ei";

    //开启了幂等灰度的Action的灰度企业
    String ACTION_IDEMPOTENT_GRAY_EI = "%s.idempotent_gray_ei";

    /**
     * 文件批量打包新接口灰度
     */
    String FILE_BATCH_PACK_NEW_API = "file_batch_pack_new_api";

    String ONE_FLOW_GRAY_EI = "one_flow_gray_ei";

    String MAX_QUERY_LIMIT_GRAY = "max_query_limit_gray";

    // 布局更新并行处理灰度配置
    String PARALLEL_LAYOUT_UPDATE_GRAY = "parallel_layout_update_gray";

    String IMAGE_EXPORT_EMBED_ONLY = "image_export_embed_only";

    String IMAGE_EXPORT_PREVIEW_AND_LINK = "image_export_preview_and_link";

    String AI_SEARCH_GRAY = "ai_search_gray";
    String IMPORT_DUPLICATED_FILL_PARTNERID = "import_duplicated_fill_partnerid";
    String NOT_VALIDATE_DETAIL_OBJECT_LOGIC_GRAY_EI = "not_validate_detail_object_logic_gray_ei";
}
