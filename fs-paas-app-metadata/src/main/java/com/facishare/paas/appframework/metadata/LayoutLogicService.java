package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.ref.RefMessage;
import com.facishare.paas.appframework.metadata.dto.DescribeDetailResult;
import com.facishare.paas.appframework.metadata.dto.LayoutResult;
import com.facishare.paas.appframework.metadata.dto.LayoutRoleInfo;
import com.facishare.paas.appframework.metadata.layout.LayoutAgentType;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ICustomComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Strings;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 定义layout的业务逻辑 Created by zhouwr on 2017/10/26
 */
public interface LayoutLogicService {

    @Deprecated
    ILayout createLayout(User user, ILayout layout);

    ILayout createLayout(LayoutContext context, ILayout layout);

    @Deprecated
    @Transactional
    ILayout createLayout(User user, ILayout layout, boolean checkLayout);

    ILayout createLayout(LayoutContext context, ILayout layout, boolean checkLayout);

    ILayout updateLayout(User user, ILayout layout);

    ILayout updateLayout(User user, ILayout layout, IObjectDescribe describeDraft);

    @Transactional
    ILayout updateLayout(User user, ILayout layout, boolean checkLayout);

    @Transactional
    ILayout updateLayout(User user, ILayout layout, boolean checkLayout, IObjectDescribe describeDraft);

    @Deprecated
    void removeFieldInLayout(String tenantId, String describeApiName, String fieldApiName);

    void removeFieldInLayout(LayoutContext context, String describeApiName, String fieldApiName);

    ILayout deleteLayout(User user, String layoutId);

    void deleteLayout(User user, ILayout layout);

    @Deprecated
    LayoutResult createLayoutAndUpdateDescribe(User user, ILayout layout, IObjectDescribe objectDescribe);

    LayoutResult createLayoutAndUpdateDescribe(LayoutContext context, ILayout layout, IObjectDescribe objectDescribe);

    @Deprecated
    LayoutResult createLayoutAndUpdateDescribe(User user, ILayout layout, IObjectDescribe objectDescribe, String persistentDataCalc);

    LayoutResult createLayoutAndUpdateDescribe(LayoutContext context, ILayout layout, IObjectDescribe objectDescribe, String persistentDataCalc);

    LayoutResult updateLayoutAndUpdateDescribe(User user, ILayout layout, IObjectDescribe objectDescribe);

    LayoutResult updateLayoutAndUpdateDescribe(User user, ILayout layout, IObjectDescribe objectDescribe, String persistentDataCalc);

    @Deprecated
    LayoutResult createLayoutAndCreateDescribe(User user, ILayout layout, ILayout listLayout, IObjectDescribe objectDescribe);

    LayoutResult createLayoutAndCreateDescribe(LayoutContext context, ILayout layout, ILayout listLayout, IObjectDescribe objectDescribe);

    @Deprecated
    ILayout createListLayout(User user, ILayout layout);

    ILayout createListLayout(LayoutContext context, ILayout layout);

    @Deprecated
    ILayout createAbstractLayout(User user, ILayout layout);

    ILayout createAbstractLayout(LayoutContext context, ILayout layout);

    @Deprecated
    ILayout createFLowTaskListLayout(User user, ILayout layout);

    ILayout createFLowTaskListLayout(LayoutContext context, ILayout layout);

    ILayout updateListLayout(User user, ILayout layout);

    ILayout updateAbstractLayout(User user, ILayout layout);

    ILayout updateFLowTaskListLayout(User user, ILayout layout);

    ILayout findObjectLayoutByApiNameAndType(User user, String layoutApiName, String layoutType, IObjectDescribe describe, IObjectData data);

    @Deprecated
    ILayout findObjectLayout(User user, String recordType, String objectApiName);

    ILayout findObjectLayout(LayoutContext context, String recordType, String objectApiName);

    @Deprecated
    List<ILayout> findByTypes(String tenantId, String describeApiName, List<String> layoutTypes);

    List<ILayout> findByTypes(LayoutContext context, String describeApiName, List<String> layoutTypes);

    @Deprecated
    List<ILayout> findByTypesIncludeFlowLayout(String tenantId, String describeApiName, List<String> layoutTypes);

    List<ILayout> findByTypesIncludeFlowLayout(LayoutContext context, String describeApiName, List<String> layoutTypes);

    /**
     * 查询指定类型的布局，包括流程布局，支持多语处理
     *
     * @param context 布局上下文
     * @param describeApiName 对象API名称
     * @param layoutTypes 布局类型列表
     * @param needLayoutLang 是否需要多语处理
     * @return 布局列表
     */
    List<ILayout> findByTypesIncludeFlowLayout(LayoutContext context, String describeApiName, List<String> layoutTypes, Boolean needLayoutLang);

    /**
     * <pre>
     * 获取所有移动端摘要布局（layout_type: list）
     * </pre>
     *
     * @param user 请求用户（使用了租户ID）
     * @param objectDescribe 对象描述
     * @param includeButtons 是否包含按钮
     * @return 布局列表
     */
    @Deprecated
    List<ILayout> findMobileListLayout(User user, IObjectDescribe objectDescribe, boolean includeButtons);

    List<ILayout> findMobileListLayout(LayoutContext context, IObjectDescribe objectDescribe, boolean includeButtons);

    /**
     * layout_type: list_layout
     *
     * @param user 请求用户（使用了租户ID）
     * @param objectDescribe 对象描述
     * @return 布局列表
     */
    @Deprecated
    List<ILayout> findListLayout(User user, IObjectDescribe objectDescribe);

    List<ILayout> findListLayout(LayoutContext context, IObjectDescribe objectDescribe);

    @Deprecated
    List<ILayout> getDetailLayouts(String tenantId, IObjectDescribe describe);

    List<ILayout> getDetailLayouts(LayoutContext context, IObjectDescribe describe);

    /**
     * @param user
     * @param describeApiName 流程任务对象
     * @param whatDescribeApiName 业务对象
     * @param layoutType 布局类型 flow_task_list 流程待办列表页布局 和 what_list 流程待办摘要布局
     * @return
     */
    @Deprecated
    List<ILayout> findFlowTaskListLayoutsByType(User user, String describeApiName, String whatDescribeApiName, String layoutType);

    List<ILayout> findFlowTaskListLayoutsByType(LayoutContext context, String describeApiName, String whatDescribeApiName, String layoutType);

    ILayout findFlowTaskListLayoutsByLayoutNameAndType(User user, IObjectDescribe describe, IObjectDescribe whatDescribe, String layoutName, String layoutType);

    @Deprecated
    List<ILayout> findWhatListLayout(User user, IObjectDescribe describe, IObjectDescribe whatDescribe);

    List<ILayout> findWhatListLayout(LayoutContext context, IObjectDescribe describe, IObjectDescribe whatDescribe);

    @Deprecated
    List<ILayout> findWhatListLayouts(User user, IObjectDescribe describe, List<IObjectDescribe> whatDescribe);

    List<ILayout> findWhatListLayouts(LayoutContext context, IObjectDescribe describe, List<IObjectDescribe> whatDescribe);

    /**
     * <pre>
     * layout_type: list
     * 当某对象存在多个此类型布局时，返回数据排序后的最后一条。
     * 该类型布局已经实行了多布局，当查询该类型布局返回给端上使用时，需要根据业务类型配合。
     * 此接口给已经不再适用了。
     * </pre>
     *
     * @param tenantId       企业Id
     * @param objectApiNames 对象 APIs
     * @return 对象 API -> 布局
     */
    @Deprecated
    Map<String, ILayout> findListLayoutByDescribeApiNames(String tenantId, List<String> objectApiNames);

    /**
     * 根据多个对象返回对应移动端摘要布局（根据用户主角色和业务类型分配的）
     * 没有分配是为默认布局
     *
     * @param user                用户
     * @param descApis            对象API列表
     * @param recordTypeOptionMap 对象的业务类型
     * @param includeButtons      是否包括按钮信息
     * @return 对象：布局列表
     */
    @Deprecated
    Map<String, Map<String, ILayout>> findMobileListLayoutByDescApis(User user, List<IObjectDescribe> descApis,
                                                                     Map<String, List<IRecordTypeOption>> recordTypeOptionMap,
                                                                     boolean includeButtons);

    Map<String, Map<String, ILayout>> findMobileListLayoutByDescApis(LayoutContext context, List<IObjectDescribe> descApis,
                                                                     Map<String, List<IRecordTypeOption>> recordTypeOptionMap,
                                                                     boolean includeButtons);

    ILayout findLayoutByApiName(User user, String layoutApiName, String objectApiName);

    ILayout findListLayoutByApiName(User user, String layoutApiName, String objectApiName);

    ILayout findAbstractLayoutByApiName(User user, String layoutApiName, IObjectDescribe objectDescribe);

    @Deprecated
    ILayout findDefaultListLayout(User user, String objectApiName);

    @Deprecated
    ILayout findDefaultListLayout(User user, IObjectDescribe describe);

    ILayout findDefaultListLayout(LayoutContext context, IObjectDescribe describe);

    @Deprecated
    Map<String, Layout> findLayoutByApiNames(String tenantId, List<String> layoutApiNames);

    ILayout findObjectLayoutByType(User user, String recordType, String objectApiName, String layoutType);

    ILayout findObjectLayoutByType(LayoutContext context, String recordType, String objectApiName, String layoutType);

    @Deprecated
    ILayout findObjectLayoutByRole(User user, String recordType, String objectApiName, String role);

    @Deprecated
    ILayout findObjectLayoutByRole(User user, String recordType, String objectApiName, String role, String layoutType);

    ILayout findObjectLayoutByRole(LayoutContext context, String recordType, String objectApiName, String role, String layoutType);

    @Deprecated
    ILayout findObjectLayoutWithType(User user, String recordType, IObjectDescribe describe, String layoutType, IObjectData data);

    ILayout findObjectLayoutWithType(LayoutContext context, String recordType, IObjectDescribe describe, String layoutType, IObjectData data);

    @Deprecated
    ILayout findObjectLayoutWithType(User user, String recordType, IObjectDescribe describe, String layoutType, IObjectData data, boolean isIncludeFormTable);

    ILayout findObjectLayoutWithType(LayoutContext context, String recordType, IObjectDescribe describe, String layoutType, IObjectData data, boolean isIncludeFormTable);

    @Deprecated
    Map<String, ILayout> findObjectLayoutWithType(User user, List<String> recordTypes, IObjectDescribe describe, String layoutType, IObjectData data);

    Map<String, ILayout> findObjectLayoutWithType(LayoutContext context, List<String> recordTypes, IObjectDescribe describe, String layoutType, IObjectData data);

    @Deprecated
    ILayout findObjectLayoutWithTypeIncludeAllComponent(User user, String recordType, IObjectDescribe describe, String layoutType, IObjectData data);

    ILayout findObjectLayoutWithTypeIncludeAllComponent(LayoutContext context, String recordType, IObjectDescribe describe, String layoutType, IObjectData data);

    Map<String, Layout> findLayoutByApiNames(String tenantId, List<String> layoutApiNames, String describeApiName);

    @Deprecated
    List<ILayout> findLayoutByObjectApiName(String tenantId, String objectApiName);

    List<ILayout> findLayoutByObjectApiName(LayoutContext context, String objectApiName);

    @Deprecated
    List<ILayout> findLayoutByObjectApiNameAndLayoutType(String tenantId, String objectApiName, String layoutType);

    List<ILayout> findLayoutByObjectApiNameAndLayoutType(LayoutContext context, String objectApiName, String layoutType);

    @Deprecated
    List<ILayout> findLayoutByObjectApiNameAndLayoutTypeIncludeFlowLayout(String tenantId, String objectApiName, String layoutType);

    List<ILayout> findLayoutByObjectApiNameAndLayoutTypeIncludeFlowLayout(LayoutContext context, String objectApiName, String layoutType);

    @Deprecated
    ILayout findDefaultLayout(User user, String layoutType, String objectApiName);

    ILayout findDefaultLayout(LayoutContext context, String layoutType, String objectApiName);

    @Deprecated
    int findCountByType(String tenantId, List<String> layoutTypeList, String describeApiName);

    int findCountByType(LayoutContext context, List<String> layoutTypeList, String describeApiName);

    @Deprecated
    void checkLayoutCountLimit(User user, String describeApiName, String layoutType);

    void checkLayoutCountLimit(LayoutContext context, String describeApiName, String layoutType);

    boolean checkNeedShowRelatedObjs(User user, String objectDataId, String objectDescribeApiName, List<String> needHideFields);

    @Deprecated
    ILayout getLayoutWithComponents(User user, String recordType, IObjectDescribe describe, IObjectData objectData,
                                    List<RelatedObjectDescribeStructure> relatedObjectList, List<RelatedObjectDescribeStructure> detailObjectList,
                                    PageType pageType);

    ILayout getLayoutWithComponents(LayoutContext context, String recordType, IObjectDescribe describe, IObjectData objectData,
                                    List<RelatedObjectDescribeStructure> relatedObjectList, List<RelatedObjectDescribeStructure> detailObjectList,
                                    PageType pageType);

    @Deprecated
    ILayout getLayoutWithComponents(User user, String recordType, IObjectDescribe describe, IObjectData objectData,
                                    List<RelatedObjectDescribeStructure> relatedObjectList, List<RelatedObjectDescribeStructure> detailObjectList,
                                    Collection<String> unauthorizedFields, PageType pageType);

    ILayout getLayoutWithComponents(LayoutContext context, String recordType, IObjectDescribe describe, IObjectData objectData,
                                    List<RelatedObjectDescribeStructure> relatedObjectList, List<RelatedObjectDescribeStructure> detailObjectList,
                                    Collection<String> unauthorizedFields, PageType pageType);

    @Deprecated
    ILayout getLayoutWithComponents(User user, String recordType, IObjectDescribe describe,
                                    IObjectData objectData, List<RelatedObjectDescribeStructure> relatedObjectList,
                                    List<RelatedObjectDescribeStructure> detailObjectList,
                                    Collection<String> unauthorizedFields, PageType pageType,
                                    boolean fromRecycleBin, boolean excludeButton);

    ILayout getLayoutWithComponents(LayoutContext context, String recordType, IObjectDescribe describe,
                                    IObjectData objectData, List<RelatedObjectDescribeStructure> relatedObjectList,
                                    List<RelatedObjectDescribeStructure> detailObjectList,
                                    Collection<String> unauthorizedFields, PageType pageType,
                                    boolean fromRecycleBin, boolean excludeButton);

    ILayout getLayoutByApiNameWithComponents(User user, String layoutApiName, IObjectDescribe describe, IObjectData objectData);

    ILayout getLayoutByApiNameWithComponents(User user, String layoutApiName, IObjectDescribe describe, IObjectData objectData,
                                             List<RelatedObjectDescribeStructure> relatedObjectList,
                                             List<RelatedObjectDescribeStructure> detailObjectList, boolean excludeButton);

    @Deprecated
    ILayout getListLayoutWitchComponents(User user, IObjectDescribe describe, PageType pageType, String renderPageType);

    ILayout getListLayoutWitchComponents(LayoutContext context, IObjectDescribe describe, PageType pageType, String renderPageType);

    @Deprecated
    ILayout getListLayoutWitchComponents(User user, IObjectDescribe describe, PageType pageType, String renderPageType,
                                         String recordType);

    ILayout getListLayoutWitchComponents(LayoutContext context, IObjectDescribe describe, PageType pageType, String renderPageType,
                                         String recordType);

    @Deprecated
    ILayout getFlowTaskListLayoutWitchComponents(User user, IObjectDescribe describe, IObjectDescribe whatObjectDescribe,
                                                 PageType pageType, LayoutAgentType layoutAgentType, String recordType);

    ILayout getFlowTaskListLayoutWitchComponents(LayoutContext context, IObjectDescribe describe, IObjectDescribe whatObjectDescribe,
                                                 PageType pageType, LayoutAgentType layoutAgentType, String recordType);

    /**
     * 在 whatListHeader 接口，查询用户渲染流程待办列表页表头字段的布局
     *
     * @param user
     * @param describe
     * @param whatDescribe
     * @return
     */
    ILayout findObjectLayoutWithWhatDescribe(User user, IObjectDescribe describe, IObjectDescribe whatDescribe);

    ILayout getListLayoutByApiNameWitchComponents(User user, String layoutApiName, IObjectDescribe describe,
                                                  PageType pageType, String renderPageType);

    @Deprecated
    ILayout findListLayoutByRecordType(User user, IObjectDescribe describe, String recordType);

    ILayout findListLayoutByRecordType(LayoutContext context, IObjectDescribe describe, String recordType);

    ILayout findLayoutById(String layoutId, String tenantId);

    List<ICustomComponent> findCustomComponents(String tenantId, String objectApiName);

    List<ICustomComponent> findCustomComponentsByApiNames(String tenantId, List<String> customApiNames);

    List<ICustomComponent> findCustomComponents(String tenantId, ILayout layout);

    List<IComponent> getBusinessComponents(String tenantId, String objectApiName, String layoutType);

    List<IComponent> getBusinessComponents(String tenantId, IObjectDescribe describe, String layoutType);

    List<String> filterComponentsByFunctionCode(User user, IObjectDescribe objectDescribe, PageType pageType, boolean isMobile, List<IComponent> components);

    Map<String, String> findComponentPreKeys(List<IComponent> components);

    List<LayoutRoleInfo> findAssignedListLayout(String describeApiName, Set<String> listLayouts, User user, String sourceInfo);

    @Deprecated
    List<LayoutRoleInfo> findAssignedListLayout(User user, String describeApiName, String whatDescribeApiName, Set<String> listLayouts,
                                                String defaultLayoutName, String listLayout, String sourceInfo);

    List<LayoutRoleInfo> findAssignedListLayout(LayoutContext context, String describeApiName, String whatDescribeApiName, Set<String> listLayouts,
                                                String defaultLayoutName, String layoutType, String sourceInfo);

    List<LayoutRoleInfo> findAssignedListLayout(String describeApiName, Set<String> listLayouts, User user);

    @Deprecated
    void saveListLayoutAssign(String describeApiName, List<LayoutRoleInfo> roleList, User user);

    @Deprecated
    void saveListLayoutAssign(User user, String describeApiName, String whatDescribeApiName, List<LayoutRoleInfo> roleList, String layoutType);

    void saveListLayoutAssign(LayoutContext context, String describeApiName, String whatDescribeApiName, List<LayoutRoleInfo> roleList, String layoutType);

    @Deprecated
    byte getEditLayoutStatus(String tenantId, String describeApiName);

    byte getEditLayoutStatus(LayoutContext context, String describeApiName);

    @Deprecated
    boolean isEditLayoutEnable(String tenantId, String describeApiName, boolean useCache);

    boolean isEditLayoutEnable(LayoutContext context, String describeApiName, boolean useCache);

    @Deprecated
    void deleteEditLayoutConfig(User user, String describeApiName);

    void deleteEditLayoutConfig(LayoutContext context, String describeApiName);

    @Deprecated
    void enableEditLayout(User user, String describeApiName);

    @Deprecated
    void enableEditLayout(User user, String describeApiName, boolean detailPageReferenceFieldConfig);

    void enableEditLayout(LayoutContext context, String describeApiName, boolean detailPageReferenceFieldConfig);

    @Transactional
    void disableEditLayout(User user, String describeApiName);

    void disableEditLayout(LayoutContext context, String describeApiName);

    ILayout convertDetailToEditLayout(User user, String describeApiName, String layoutApiName);

    ILayout convertEditToDetailLayout(User user, String describeApiName, String layoutApiName, boolean isRefFieldConfig);

    ILayout convertLayoutByPageType(User user, IObjectDescribe describe, ILayout layout, String pageType);

    void handleSummaryKeyComponents(String tenantId, IObjectDescribe describe, ILayout layout);

    void processLayoutByDomainPlugin(String tenantId,
                                     IObjectDescribe describe,
                                     ILayout layout,
                                     List<String> recordTypeList,
                                     String pageType);

    void processLayoutByDomainPlugin(String tenantId,
                                     String objectApiName,
                                     DescribeDetailResult describeDetailResult,
                                     List<String> recordTypeList,
                                     String pageType);

    List<Map<String, Object>> findLayoutTemplates(User user, String objectApiName, String business, Integer cardStyle);

    void filterRelatedObjectStructure(User user, ILayout layout, List<RelatedObjectDescribeStructure> relatedObjectList);

    void bulkCreate(List<ILayout> layouts);

    void createOrUpdateLayoutForDevTools(String tenantId, String describeApiName, List<ILayout> layouts);

    ManageGroup queryLayoutManageGroup(User user, String describeApiName, String sourceInfo);

    void deletedLayoutManageGroupByParentApiName(User user, String parentApiName);

    void executeReferenceByLayout(RefMessage.ActionType actionType, IObjectDescribe sourceObjDesc, List<ILayout> sourceLayoutList);

    void enableApplicationLayerLayoutByAppId(User user, String appId, IObjectDescribe objectDescribe);

    void disableApplicationLayerLayoutByAppId(User user, String appId, IObjectDescribe objectDescribe);

    void updateLayoutVersion(User user, String describeApiName, String layoutApiName);

    @Setter
    class LayoutContext {
        @Getter
        private final User user;
        private final String appId;

        private LayoutContext(@NonNull User user, String appId) {
            this.user = user;
            this.appId = appId;
        }

        public static LayoutContext of(User user, String appId) {
            return new LayoutContext(user, appId);
        }

        public static LayoutContext of(User user) {
            return of(user, null);
        }

        public String getTenantId() {
            return user.getTenantId();
        }

        public String getAppId() {
            return appId;
        }

        public String getAppIdByLayoutType(String layoutType) {
            String result = null;
            if (Strings.isNullOrEmpty(layoutType)) {
                result = appId;
            }
            if (LayoutTypes.DETAIL.equals(layoutType) || LayoutTypes.EDIT.equals(layoutType)) {
                result = appId;
            }
            return Strings.isNullOrEmpty(result) ? null : result;
        }

    }
}