package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.describe.ImageFieldDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Component
public class ImageImportDataConverter extends AbstractFileImportDataConverter {

    @Override
    public List<String> getSupportedFieldTypes() {
        return Lists.newArrayList(IFieldType.IMAGE);
    }

    @Override
    public ConvertResult convertFieldData(IObjectData objectData, IFieldDescribe fieldDescribe, User user) {
        ImageFieldDescribe imageFieldDescribe = (ImageFieldDescribe) fieldDescribe;
        ObjectDataExt dataExt = ObjectDataExt.of(objectData);
        String fieldApiName = fieldDescribe.getApiName();
        //保存图片结构
        List<Map<String, String>> result = Lists.newArrayList();
        String tempData = dataExt.get(fieldApiName, String.class).trim();
        if (Strings.isNullOrEmpty(tempData)) {
            //兼容图片类型：元数据保存图片/附件类型时不能传"", 必须是空列表
            return ConvertResult.buildSuccess(result);
        }
        String[] arr = tempData.split("\\|");
        List<String> images = Arrays.asList(arr);
        //为空时
        if (CollectionUtils.empty(images)) {
            return ConvertResult.buildSuccess(result);
        }
        //如果只允许水印图片，则不能导入
        if (imageFieldDescribe.getIsWaterMark()) {
            return ConvertResult.buildError(I18NExt.text(I18NKey.IMPORT_IMAGE_ERROR1));
        }
        // 校验导入图片数量，不能超过设置的最大数量
        if (images.size() > imageFieldDescribe.getFileAmountLimit()) {
            return ConvertResult.buildError("[" + imageFieldDescribe.getLabel() + "]" +
                    I18NExt.getOrDefault(I18NKey.IMAGE_EXCEED_COUNT, "导入图片数量不能超过{0}张", imageFieldDescribe.getFileAmountLimit()));// ignoreI18n
        }
        if (!validateImageMinSize(imageFieldDescribe, images)) {
            return ConvertResult.buildError("[" + imageFieldDescribe.getLabel() + "]" +
                    I18NExt.getOrDefault(I18NKey.IMAGE_EXCEED_MIN_COUNT, "导入图片数量不能少于{0}张", imageFieldDescribe.getFileAmountMinLimit()));// ignoreI18n
        }
        //通过接口查询导入图片信息(大小、扩展名等)，不能查出代表不存在
        String error = checkImportFilesOrConvertFiles(user, images, imageFieldDescribe.getLabel(), I18NKey.IMAGE, 1024 * 1024 * 20L, result);
        if (!Strings.isNullOrEmpty(error)) {
            return ConvertResult.buildError(error);
        }
        return ConvertResult.buildSuccess(result);
    }

    /**
     * 校验图片最小个数
     *
     * @return true 符合要求 false 不符合要求
     */
    private boolean validateImageMinSize(ImageFieldDescribe fieldDescribe, List<String> images) {
        Integer fileAmountMinLimit = fieldDescribe.getFileAmountMinLimit();
        if (ObjectUtils.isEmpty(fileAmountMinLimit)) {
            return true;
        }

        //图片为空也不校验长度，这个是业务侧特殊需求
        if (ObjectUtils.isEmpty(images)) {
            return true;
        }
        return images.size() >= fileAmountMinLimit;
    }
}
