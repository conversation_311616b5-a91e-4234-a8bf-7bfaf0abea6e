package com.facishare.paas.appframework.metadata.expression;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.expression.ExpressionService;
import com.facishare.paas.expression.Type;
import com.facishare.paas.expression.util.FunctionUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.impl.describe.DateTimeFieldDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Builder;
import lombok.NonNull;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.codehaus.groovy.control.CompilationFailedException;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.expression.ExpressionVariableFactory.*;

/**
 * 表达式对象
 * <p>
 * Created by liyiguang on 2018/4/13.
 */
@Slf4j
public class Expression {

    public static final List<String> DYNAMIC_GLOBAL_VARIABLE_NAMES = Lists.newArrayList("currentDate__g",
            "currentTime__g", "currentDateTime__g", "tenantName__g", "current_intercon_enterprise__g");

    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$([a-zA-Z0-9_.#]+)\\$");

    public static final List<String> UNSTORED_TYPE = ImmutableList.of("globalVariable", "timeFunction", "role", "department");

    public static final String GLOBAL_VARIABLE = "globalVariable";

    public static final String TIME_FUNCTION = "timeFunction";

    public static final String EMPLOYEE = "role";

    public static final String DEPARTMENT = "department";

    public static final String VALUE = "._value";
    public static final String LABEL = "._label";

    /**
     * 所属对象
     */
    private final IObjectDescribe describe;

    /**
     * 表达式
     */
    @NonNull
    private final String expression;

    /**
     * 返回值类型
     */
    @NonNull
    private final ExpressionDataType returnType;

    /**
     * 空值是否处理为零
     */
    private final boolean nullAsZero;

    /**
     * 小数位
     */
    private final int decimalPlaces;

    /**
     * 对于单选字段是否使用value参与计算
     */
    private final boolean useValue;

    /**
     * 是否计算字段
     */
    private final boolean isFormula;

    /**
     * 计算字段的apiName
     */
    private final String fieldName;

    /**
     * 表达式的展示名称
     */
    private final String expressionLabel;

    /**
     * 是否忽略公式中的非法变量（对于已禁用或删除的变量不抛异常）
     */
    private final boolean ignoreInvalidVariable;

    @Setter
    private String dataId;

    private final boolean isDefaultValue;

    /**
     * 该数据所属币种的最新汇率
     */
    @Setter
    private String latestExchangeRate;
    /**
     * 该数据所属币种的最新汇率版本
     */
    @Setter
    private String latestExchangeRateVersion;

    /**
     * 本位币
     */
    @Setter
    private String functionalCurrency;

    /**
     * 汇率集合(key：原始币种-目标币种，value：原始币种转目标币种的汇率)
     */
    @Setter
    private Map<String, String> exchangeRateMap;

    /**
     * 绑定变量，从表达式中解析处理
     */
    private final List<ExpressionVariable> variables = Lists.newArrayList();

    //是否试算
    private final boolean debug;

    public static Expression of(String expression) {
        return Expression.builder().expression(expression).build();
    }

    public static String clearValueAndLabel(String variableName) {
        return variableName.replace(VALUE, "").replace(LABEL, "");
    }

    @Builder
    public Expression(@NonNull String expression,
                      String returnType,
                      boolean nullAsZero,
                      int decimalPlaces,
                      boolean useValue,
                      IObjectDescribe describe,
                      boolean isFormula,
                      String fieldName,
                      String expressionLabel,
                      boolean ignoreInvalidVariable,
                      boolean isDefaultValue,
                      boolean debug) {
        this.expression = expression;
        this.returnType = ExpressionDataType.of(returnType);
        this.nullAsZero = nullAsZero;
        this.decimalPlaces = decimalPlaces;
        this.useValue = useValue;
        this.describe = describe;
        this.isFormula = isFormula;
        this.fieldName = fieldName;
        this.expressionLabel = expressionLabel;
        this.ignoreInvalidVariable = ignoreInvalidVariable;
        this.isDefaultValue = isDefaultValue;
        this.debug = debug;
    }

    public void init() {
        parseVariable();
    }

    /**
     * TODO: 编译优化，字符串中的变量会被误解析
     */
    private void parseVariable() {
        //只有新建编辑页面实时计算接口才需要计算汇率等多货币字段
        if (isMultiCurrencyField()) {
            ExpressionVariable variable = ExpressionVariableFactory.createExpressionVariable(FieldDescribeExt.CURRENCY_FIELD,
                    describe, expressionLabel);
            variables.add(variable);
            return;
        }
        if (Strings.isNullOrEmpty(expression)) {
            return;
        }
        Set<String> variableNames = Sets.newHashSet();
        Matcher matcher = VARIABLE_PATTERN.matcher(expression);
        while (matcher.find()) {
            String name = matcher.group(1);
            try {
                if (variableNames.add(name)) {
                    variables.add(ExpressionVariableFactory.createExpressionVariable(name, describe, expressionLabel));
                }
            } catch (AppBusinessException e) {
                log.warn("found invalid variable in expression,tenantId:{},objectApiName:{},fieldName:{},expression:{},invalidVariable:{},errMsg:{}",
                        describe.getTenantId(), describe.getApiName(), fieldName, expression, name, e.getMessage());
                if (!isIgnoreInvalidVariable(name)) {
                    throw e;
                }
            }
        }
    }

    private boolean isIgnoreInvalidVariable(String name) {
        return ignoreInvalidVariable || ObjectDescribeExt.isSystemField(name);
    }

    private boolean isMultiCurrencyField() {
        return isDefaultValue && FieldDescribeExt.isMultiCurrencyCalculateFields(fieldName);
    }

    public boolean dependExchangeRateFunction() {
        return !Strings.isNullOrEmpty(expression) && expression.contains("EXCHANGERATE");
    }

    public List<String> parseVariableNames() {
        List<String> apiNameList = Lists.newArrayList();
        if (Strings.isNullOrEmpty(expression)) {
            return apiNameList;
        }
        Matcher matcher = VARIABLE_PATTERN.matcher(expression);
        while (matcher.find()) {
            String apiName = matcher.group(1);
            if (!apiNameList.contains(apiName)) {
                apiNameList.add(apiName);
            }
        }
        return apiNameList;
    }

    public Set<String> getDependentObjectAPINames() {
        return getRefObjectFieldVariables().stream().map(x -> x.getTargetObjectAPIName()).collect(Collectors.toSet());
    }

    public Set<String> getDependentGlobalVariableAPINames() {
        return getGlobalVariables().stream().map(x -> x.getName()).collect(Collectors.toSet());
    }

    public Set<String> getDependentFormVariableAPINames() {
        return getFormVariables().stream().map(x -> x.getName()).collect(Collectors.toSet());
    }

    /**
     * 三种数据源
     * 全局变量，对象变量，表单变量
     *
     * @param globalVariableDescribes
     * @param describes
     * @param extVariableTypes
     */
    public void bindingVariableType(
            Map<String, IGlobalVariableDescribe> globalVariableDescribes,
            Map<String, IObjectDescribe> describes,
            Map<String, String> extVariableTypes) {
        variables.forEach(x -> {
            try {
                if (x instanceof RefObjectFieldVar) {
                    ((RefObjectFieldVar) x).bindingType(describes);
                } else if (x instanceof GlobalVariable) {
                    ((GlobalVariable) x).bindingType(globalVariableDescribes);
                } else if (x instanceof FormVariable) {
                    ((FormVariable) x).bindingType(extVariableTypes);
                } else if (x instanceof ExtVariable) {
                    ((ExtVariable) x).bindingType(extVariableTypes);
                }
            } catch (AppBusinessException e) {
                log.warn("bindingVariableType failed,tenantId:{},objectApiName:{},variableName:{},errMsg:{}",
                        describe.getTenantId(), describe.getApiName(), x.getName(), e.getMessage());
                if (!ignoreInvalidVariable) {
                    throw e;
                }
            }
        });
    }

    /**
     * 三种数据源
     *
     * @param data
     * @param globalVariableData
     * @param objectDataMap
     * @param extData
     */
    public void bindingVariableData(IObjectData data,
                                    Map<String, Object> globalVariableData,
                                    Map<String, Map<String, IObjectData>> objectDataMap,
                                    Map<String, Object> extData) {
        variables.forEach(x -> {
            //先把上一条数据绑定的值清空
            x.setValue(null);
            String variableName = x.getName();
            if (x instanceof GlobalVariable) {
                x.setValue(globalVariableData.get(variableName));
            } else if (x instanceof RefObjectFieldVar) {
                RefObjectFieldVar fieldVar = (RefObjectFieldVar) x;
                String targetApiName = fieldVar.getTargetObjectApiNameForCalculate();
                if (!Strings.isNullOrEmpty(fieldVar.getTargetObjectId()) && objectDataMap.containsKey(targetApiName)) {
                    IObjectData objectData = objectDataMap.get(targetApiName).get(fieldVar.getTargetObjectId());
                    if (objectData != null) {
                        x.setValue(getVariableValue(objectData, fieldVar.getTargetFieldAPIName()));
                    }
                }
            } else if (x instanceof ObjectFieldVar) {
                ObjectFieldVar fieldVar = (ObjectFieldVar) x;
                if (data != null) {
                    x.setValue(getVariableValue(data, fieldVar.getFieldName()));
                }
            } else if (x instanceof FormVariable) {
                x.setValue(extData.get(variableName));
            } else if (x instanceof ExtVariable) {
                //自定义变量优先从data取值，data没有这个key才从extData取值
                if (data != null && data.containsField(variableName)) {
                    x.setValue(data.get(variableName));
                } else if (extData != null && extData.containsKey(variableName)) {
                    x.setValue(extData.get(variableName));
                }
            }
        });
    }

    //汇率字段的值为空的话，默认为1（本位币）
    private Object getVariableValue(IObjectData data, String variableName) {
        Object value = data.get(variableName);
        if (FieldDescribeExt.EXCHANGE_RATE_FIELD.equals(variableName) && ObjectDataExt.isValueEmpty(value)) {
            return "1";
        }
        return value;
    }

    public void compileCheck(ExpressionService service, String tenantId) {
        if (isDebugMode()) {
            log.info("compile check:on:{},fn:{},expression:{},binding types:{},return type:{}",
                    describe.getApiName(), fieldName, formatExpression(), getBindingTypes(), returnType.getExpressionType());
        } else if (log.isDebugEnabled()) {
            log.debug("compile check:on:{},fn:{},expression:{},binding types:{},return type:{}",
                    describe.getApiName(), fieldName, formatExpression(), getBindingTypes(), returnType.getExpressionType());
        }
        checkDateTimeField(tenantId);
        service.compile(formatExpression(), getBindingTypes(), returnType.getExpressionType());
    }

    private void checkDateTimeField(String tenantId) {
        if (!AppFrameworkConfig.isDateTimeSupportNotUseMultitimeZoneTenant(tenantId)) {
            return;
        }
        HashSet<Boolean> checkNotUseMultiTimeZone = Sets.newHashSet();
        if (StringUtils.replace(expression, " ", "").contains("DATETIMEVALUE(")) {
            checkNotUseMultiTimeZone.add(false);
        }
        for (ExpressionVariable expressionVariable : variables) {
            if (ExpressionDataType.DateTime.equals(expressionVariable.getDataType())) {
                if (expressionVariable instanceof GlobalVariable || expressionVariable instanceof FormVariable) {
                    checkNotUseMultiTimeZone.add(false);
                    continue;
                }
                DateTimeFieldDescribe fieldDescribe = null;
                if (expressionVariable instanceof RefObjectFieldVar) {
                    fieldDescribe = (DateTimeFieldDescribe) ((RefObjectFieldVar) expressionVariable).getTargetField();
                } else {
                    IFieldDescribe iFieldDescribe = ((ObjectFieldVar) expressionVariable).getFieldDescribe();
                    if (IFieldType.DATE_TIME.equals(iFieldDescribe.getType())) {
                        fieldDescribe = (DateTimeFieldDescribe) ((ObjectFieldVar) expressionVariable).getFieldDescribe();
                    } else {
                        checkNotUseMultiTimeZone.add(false);
                    }
                }
                if (Objects.nonNull(fieldDescribe)) {
                    checkNotUseMultiTimeZone.add(fieldDescribe.getNotUseMultiTimeZone());
                }
            }
        }
        if (checkNotUseMultiTimeZone.size() > 1) {
            throw new ExpressionCalculateException(I18N.text(I18NKey.EXPRESSION_DATE_TIME_ZONE_ATTRIBUTE_INCONSISTENT));
        }
    }

    public Object doCalculate(ExpressionService service) {
        //特殊处理多币种字段
        if (isMultiCurrencyField()) {
            return getMultiCurrencyFieldValue();
        }
        //打印包含List类型变量的表达式
        logIfExistsListVariable();
        String formatExpression = formatExpression();
        Map<String, Object> bindingData = getBindingData();
        if (isDebugMode()) {
            log.info("on:{},fn:{},di:{},expression:{},bindingData:{}", describe.getApiName(), fieldName, dataId,
                    formatExpression, bindingData);
        } else if (log.isDebugEnabled()) {
            log.debug("on:{},fn:{},di:{},expression:{},bindingData:{}", describe.getApiName(), fieldName, dataId,
                    formatExpression, bindingData);
        }
        try {
            Object result = service.evaluate(formatExpression, bindingData);
            //除数为0的Double转成null
            if (result == null || isNaN(result)) {
                logCalculateResult(result);
                return null;
            }
            result = returnType.toPaaSObject(result);
            if (result instanceof BigDecimal) {
                result = ((BigDecimal) result).setScale(this.decimalPlaces, BigDecimal.ROUND_HALF_UP);
            }
            logCalculateResult(result);
            return result;
        } catch (Throwable e) {
            if (isDebugMode() || StringUtils.startsWith(fieldName, "rule_") || e instanceof CompilationFailedException) {
                log.warn("calc failed,ei:{},on:{},fn:{},ex:{},bd:{},di:{}",
                        describe.getTenantId(), describe.getApiName(), fieldName, formatExpression, bindingData, dataId, e);
            } else {
                log.debug("calc failed:{},ei:{},on:{},fn:{},ex:{},bd:{},di:{}",
                        e.getMessage(), describe.getTenantId(), describe.getApiName(), fieldName, formatExpression, bindingData, dataId);
            }
            if (debug) {
                if (e instanceof RuntimeException) {
                    throw (RuntimeException) e;
                }
                throw new RuntimeException(e);
            }
            return null;
        }
    }

    private void logIfExistsListVariable() {
        try {
            if (CollectionUtils.empty(variables)) {
                return;
            }
            for (ExpressionVariable variable : variables) {
                if (Objects.nonNull(variable.getDataType())
                        && Type.LIST.equals(variable.getDataType().getExpressionType())
                        && !Objects.equals(returnType, variable.getDataType())) {
                    log.warn("expression contains list variable,ei:{},oan:{},fan:{},variable:{},expression:{}",
                            Optional.ofNullable(describe).map(IObjectDescribe::getTenantId).orElse(null),
                            Optional.ofNullable(describe).map(IObjectDescribe::getApiName).orElse(null),
                            fieldName, variable.getName(), expression);
                    break;
                }
            }
        } catch (Exception e) {
            log.warn("logIfExistsListVariable failed,expression:{} ", expression, e);
        }
    }

    private String getMultiCurrencyFieldValue() {
        String result = null;
        if (FieldDescribeExt.EXCHANGE_RATE_FIELD.equals(fieldName)) {
            result = latestExchangeRate;
        } else if (FieldDescribeExt.EXCHANGE_RATE_VERSION_FIELD.equals(fieldName)) {
            result = latestExchangeRateVersion;
        } else if (FieldDescribeExt.FUNCTIONAL_CURRENCY_FIELD.equals(fieldName)) {
            result = functionalCurrency;
        }
        logCalculateResult(result);
        return result;
    }

    private boolean isNaN(Object result) {
        if (!(result instanceof Double)) {
            return false;
        }
        Double doubleValue = (Double) result;
        return doubleValue.isNaN() || doubleValue.isInfinite();
    }

    private boolean isDebugMode() {
        return debug || RequestUtil.isDebugMode();
    }

    private void logCalculateResult(Object result) {
        if (isDebugMode()) {
            log.info("calc done,on:{},fn:{},di:{},r:{}", describe.getApiName(), fieldName, dataId, result);
        } else if (log.isDebugEnabled()) {
            log.debug("calc done,on:{},fn:{},di:{},r:{}", describe.getApiName(), fieldName, dataId, result);
        }
    }

    public List<RefObjectFieldVar> getRefObjectFieldVariables() {
        return variables.stream()
                .filter(x -> x instanceof RefObjectFieldVar)
                .map(x -> (RefObjectFieldVar) x)
                .collect(Collectors.toList());
    }

    public List<ObjectFieldVar> getObjectFieldVariables() {
        return variables.stream()
                .filter(x -> x instanceof ObjectFieldVar)
                .map(x -> (ObjectFieldVar) x)
                .collect(Collectors.toList());
    }

    public List<GlobalVariable> getGlobalVariables() {
        return variables.stream()
                .filter(x -> x instanceof GlobalVariable)
                .map(x -> (GlobalVariable) x)
                .collect(Collectors.toList());
    }

    public List<FormVariable> getFormVariables() {
        return variables.stream()
                .filter(x -> x instanceof FormVariable)
                .map(x -> (FormVariable) x)
                .collect(Collectors.toList());
    }

    public List<ExtVariable> getExtVariablesByType(String type) {
        return variables.stream()
                .filter(ExtVariable.class::isInstance)
                .map(x -> (ExtVariable) x)
                .filter(x -> Objects.equals(type, x.getVariableBizType()))
                .collect(Collectors.toList());
    }

    public List<ExtVariable> getExtVariables() {
        return variables.stream()
                .filter(ExtVariable.class::isInstance)
                .map(x -> (ExtVariable) x)
                .collect(Collectors.toList());
    }

    public boolean noFieldInExpression() {
        return CollectionUtils.empty(getRefObjectFieldVariables()) && CollectionUtils.empty(getObjectFieldVariables());
    }

    public boolean containsDynamicGlobalVariable() {
        return CollectionUtils.notEmpty(getDynamicGlobalVariables());
    }

    public List<String> getDynamicGlobalVariables() {
        return getGlobalVariables().stream().map(x -> x.getName())
                .filter(x -> DYNAMIC_GLOBAL_VARIABLE_NAMES.contains(x)).collect(Collectors.toList());
    }

    public boolean needRealTimeCalculate(String tenantId) {
        if (containsDynamicGlobalVariable()) {
            return true;
        }
        return getRefObjectFieldVariables().stream()
                .anyMatch(y -> !AppFrameworkConfig.isGrayFilterEmployeeFormula(tenantId)
                        && Objects.equals(IFieldType.EMPLOYEE, y.getFieldType()));
    }

    public List<IFieldDescribe> getDependentCalcFields(String fieldApiName, boolean includeFormulaDefault) {
        List<IFieldDescribe> result = Lists.newArrayList();
        List<IFieldDescribe> dependentCalcFields = getObjectFieldVariables().stream().map(x -> x.getField())
                .filter(x -> {
                    if (includeFormulaDefault) {
                        return FieldDescribeExt.of(x).isCalculateField();
                    } else {
                        return FieldDescribeExt.of(x).isFormula();
                    }
                }).collect(Collectors.toList());

        if (includeFormulaDefault) {
            List<IFieldDescribe> lookupFields = getRefObjectFieldVariables().stream()
                    .map(x -> describe.getFieldDescribe(x.getFieldName()))
                    .filter(x -> (FieldDescribeExt.of(x).isEmployeeField() || FieldDescribeExt.of(x).isDepartmentField() || FieldDescribeExt.of(x).isLookupField())
                            && FieldDescribeExt.of(x).hasFormulaDefaultValue())
                    .collect(Collectors.toList());
            dependentCalcFields.addAll(lookupFields);
        }

        if (CollectionUtils.empty(dependentCalcFields)) {
            return result;
        }

        checkRecycleDependency(fieldApiName, this.fieldName, dependentCalcFields);
        addDependencyToResult(result, dependentCalcFields);
        for (IFieldDescribe fieldDescribe : Lists.newArrayList(dependentCalcFields)) {
            Expression expr;
            if (FieldDescribeExt.of(fieldDescribe).isFormula()) {
                expr = FormulaFieldCalculator.createExpression(describe, (Formula) fieldDescribe);
            } else {
                expr = DefaultValueCalculator.createExpression(describe, fieldDescribe);
            }
            expr.init();
            dependentCalcFields = expr.getDependentCalcFields(fieldDescribe.getApiName(), includeFormulaDefault);

            checkRecycleDependency(fieldApiName, fieldDescribe.getApiName(), dependentCalcFields);
            addDependencyToResult(result, dependentCalcFields);
        }

        return result;
    }

    private void checkRecycleDependency(String fieldApiName, String variableFieldName, List<IFieldDescribe> dependentCalcFields) {
        dependentCalcFields.stream().filter(x -> x.getApiName().equals(fieldApiName)).findFirst().ifPresent(x -> {
            throw new ExpressionCalculateException(I18N.text(I18NKey.OBJECT_FIELD_RING_FORMING, describe.getDisplayName(), describe.getFieldDescribe(fieldApiName).getLabel()
                    + "|" + describe.getFieldDescribe(variableFieldName).getLabel()));
        });

    }

    private void addDependencyToResult(List<IFieldDescribe> result, List<IFieldDescribe> dependentFields) {
        Set<String> resultFieldNames = Sets.newHashSet(result.stream().map(x -> x.getApiName()).collect(Collectors.toSet()));
        dependentFields.forEach(x -> {
            if (!resultFieldNames.contains(x.getApiName())) {
                result.add(x);
                resultFieldNames.add(x.getApiName());
            }
        });
    }

    public List<String> getDependentCountFieldNames(IObjectDescribe masterDescribe) {
        Map<String, Set<String>> fieldNodeMap = Maps.newHashMap();
        fieldNodeMap.put(describe.getApiName(), Sets.newHashSet());
        fieldNodeMap.get(describe.getApiName()).add(fieldName);
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(describe.getApiName(), describe);
        describeMap.put(masterDescribe.getApiName(), masterDescribe);

        return getDependentCountFieldNames(describeMap, fieldNodeMap, masterDescribe);
    }

    private List<String> getDependentCountFieldNames(Map<String, IObjectDescribe> describeMap, Map<String, Set<String>> fieldNodeMap,
                                                     IObjectDescribe masterDescribe) {
        Set<String> countFieldNames = Sets.newHashSet();
        if (masterDescribe.getApiName().equals(describe.getApiName())) {
            getObjectFieldVariables().stream().map(x -> FieldDescribeExt.of(x.getField())).forEach(x -> {
                countFieldNames.addAll(getDependentCountFieldNames(describeMap, fieldNodeMap, masterDescribe, masterDescribe, x));
            });
        } else {
            getObjectFieldVariables().stream().map(x -> FieldDescribeExt.of(x.getField())).filter(x -> x.isCalculateField()).forEach(x -> {
                countFieldNames.addAll(getDependentCountFieldNames(describeMap, fieldNodeMap, describe, masterDescribe, x));
            });
            getRefObjectFieldVariables().stream().filter(x -> masterDescribe.getApiName().equals(x.getTargetObjectAPIName()))
                    .map(x -> FieldDescribeExt.of(masterDescribe.getFieldDescribe(x.getTargetFieldAPIName()))).forEach(x -> {
                        countFieldNames.addAll(getDependentCountFieldNames(describeMap, fieldNodeMap, masterDescribe, masterDescribe, x));
                    });
        }

        return Lists.newArrayList(countFieldNames);
    }

    private Set<String> getDependentCountFieldNames(Map<String, IObjectDescribe> describeMap, Map<String, Set<String>> fieldNodeMap,
                                                    IObjectDescribe dependentDescribe, IObjectDescribe masterDescribe, FieldDescribeExt fieldExt) {
        checkRecycleDependency(describeMap, fieldNodeMap, fieldExt);

        Set<String> countFieldNames = Sets.newHashSet();
        if (fieldExt.isCountField()) {
            countFieldNames.add(fieldExt.getApiName());
        } else if (fieldExt.hasFormulaDefaultValue()) {
            Expression dependentExpression = DefaultValueCalculator.createExpression(dependentDescribe, fieldExt.getFieldDescribe());
            dependentExpression.init();
            countFieldNames.addAll(dependentExpression.getDependentCountFieldNames(describeMap, fieldNodeMap, masterDescribe));
        } else if (fieldExt.isFormula()) {
            Expression dependentExpression = FormulaFieldCalculator.createExpression(dependentDescribe, fieldExt.getFieldDescribe());
            dependentExpression.init();
            countFieldNames.addAll(dependentExpression.getDependentCountFieldNames(describeMap, fieldNodeMap, masterDescribe));
        }

        fieldNodeMap.get(fieldExt.getDescribeApiName()).remove(fieldExt.getApiName());

        return countFieldNames;
    }

    private void checkRecycleDependency(Map<String, IObjectDescribe> describeMap, Map<String, Set<String>> fieldNodeMap, FieldDescribeExt fieldExt) {
        if (fieldNodeMap.getOrDefault(fieldExt.getDescribeApiName(), Sets.newHashSet()).contains(fieldExt.getApiName())) {
            throw new ExpressionCalculateException(I18N.text(I18NKey.OBJECT_FIELD_RING_FORMING,
                    describeMap.get(fieldExt.getDescribeApiName()).getDisplayName(), fieldExt.getLabel()));
        }
        fieldNodeMap.putIfAbsent(fieldExt.getDescribeApiName(), Sets.newHashSet());
        fieldNodeMap.get(fieldExt.getDescribeApiName()).add(fieldExt.getApiName());
    }

    /**
     * TODO: 编译优化，这里会误操作字符串中的 & 符
     *
     * @return
     */
    private String formatExpression() {
        String formatted = expression;
        for (ExpressionVariable variable : variables) {
            formatted = formatted.replace("$" + variable.getName() + "$", variable.getBindingName());
        }
        //formatted = formatted.replace("$", "");
        formatted = formatted.replace("&&", "$$") // 防止&& 一同被转换的
                .replace("&", "+").replace("$$", "&&");

        return formatted;
    }

    private Map<String, Object> getBindingData() {
        Map<String, Object> data = Maps.newHashMap();
        String tenantId = Objects.nonNull(describe) ? describe.getTenantId() : null;
        variables.forEach(x -> data.put(x.getBindingName(), x.getBindingValue(nullAsZero, isFormula, returnType, useValue, tenantId)));
        if (CollectionUtils.notEmpty(exchangeRateMap)) {
            data.put(FunctionUtil.EXCHANGE_RATE_KEY, exchangeRateMap);
        }
        return data;
    }

    private Map<String, Type> getBindingTypes() {
        Map<String, Type> bindingTypes = Maps.newHashMap();
        variables.forEach(x -> bindingTypes.putIfAbsent(x.getBindingName(),
                x.getDataType() == null ? returnType.getExpressionType() : x.getDataType().getExpressionType()));
        return bindingTypes;
    }

    public boolean isTimeFunctionUnStoredType() {
        String expr = StringUtils.replace(expression, " ", "");
        if (StringUtils.isNotEmpty(expr) && (expr.contains("TODAY()") || expr.contains("NOW()") || expr.contains("DATE()"))) {
            return true;
        }
        return false;
    }

    public boolean isContainOwnerDepartment() {
        if (StringUtils.isNotEmpty(expression) && expression.contains("owner_department")) {
            return true;
        }
        return false;
    }

    public boolean isDependentField(String fieldName) {
        if (getObjectFieldVariables().stream().anyMatch(x -> x.getFieldName().equals(fieldName))) {
            return true;
        }
        return getRefObjectFieldVariables().stream().anyMatch(x -> x.getFieldName().equals(fieldName));
    }

    @Override
    public String toString() {

        return "Expression{" +
                "describe=" + (describe != null ? (describe.getApiName() + "-" + describe.getDisplayName()) : null) +
                ", expression='" + expression + '\'' +
                ", returnType=" + returnType +
                ", nullAsZero=" + nullAsZero +
                ", decimalPlaces=" + decimalPlaces +
                ", isFormula=" + isFormula +
                ", fieldName='" + fieldName + '\'' +
                ", expressionLabel='" + expressionLabel + '\'' +
                ", variables=" + variables +
                '}';
    }
}
