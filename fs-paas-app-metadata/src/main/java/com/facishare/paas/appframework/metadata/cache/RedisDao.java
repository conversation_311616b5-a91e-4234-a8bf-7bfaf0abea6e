package com.facishare.paas.appframework.metadata.cache;

import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import redis.clients.jedis.Response;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * Created by linqiuying on 17/6/29.
 */
@Slf4j
@Component
public class RedisDao {
    @Autowired
    protected MergeJedisCmd redisCache;

    public void set(String key, String value, int expire) {
        redisCache.setex(key, expire, value);
    }

    public long getExpireTime(String key) {
        return redisCache.ttl(key);
    }

    public String set(String key, Map<String, String> map) {
        return redisCache.hmset(key, map);
    }

    public List<String> get(String jobId, String... fields) {
        return redisCache.hmget(jobId, fields);
    }

    public long del(String key, String... fields) {
        return redisCache.hdel(key, fields);
    }

    public void set(String key, String value) {
        redisCache.set(key, value);
    }

    public String getStrCache(String token) {
        return redisCache.get(token);
    }

    public long lpush(String token, String... value) {
        return redisCache.lpush(token, value);
    }

    public List<String> getlRange(String token, long start, long end) {
        return redisCache.lrange(token, start, end);
    }

    public long getLen(String token) {
        return redisCache.llen(token);
    }

    public long delKey(String token) {
        return redisCache.del(token);
    }

    public long batchDelKey(List<String> tokenList) {
        String[] tokens = tokenList.toArray(new String[]{});
        return redisCache.del(tokens);
    }

    public long expire(String token, int time) {
        return redisCache.expire(token, time);
    }

    public boolean putStrCache(String token, String info, int expire) {
        try {
            redisCache.set(token, info);
            redisCache.expire(token, expire);

        } catch (Exception e) {
            return false;
        }
        return true;
    }

    public String getKey(String... strings) {
        StringBuilder key = new StringBuilder();
        for (String property : strings) {
            key.append(property).append("_");
        }
        return key.toString();
    }

    public void pipelinesadd(Map<String, ? extends Collection<String>> argMap, int seconds) {
        redisCache.pipeline(p -> argMap.forEach((key, value) -> {
            p.sadd(key, value.toArray(new String[0]));
            p.expire(key, seconds);
        }));
    }

    public Map<String, Set<String>> pipelinesmembers(Collection<String> keys) {
        Map<String, Response<Set<String>>> responseMap = Maps.newHashMap();
        redisCache.pipeline(p -> keys.forEach(key -> {
            Response<Set<String>> response = p.smembers(key);
            responseMap.put(key, response);
        }));

        Map<String, Set<String>> result = responseMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, x -> x.getValue().get()));
        return result;
    }

    public Set<String> smembers(String key) {
        return redisCache.smembers(key);
    }

    public String setNx(String key, String value, int expire) {
        return redisCache.set(key, value, "NX", "EX", expire);
    }

    public List<String> mget(String... keys) {
        return redisCache.mget(keys);
    }


    /**
     * sortSet 使用List的顺序作为score
     */
    public long zadd(String key, List<String> content, int timeout) {
        if (CollectionUtils.isEmpty(content)) {
            return -1;
        }

        Map<String, Double> scoreMap = new HashMap<>();
        for (int i = 0; i < content.size(); i++) {
            scoreMap.put(content.get(i), Double.valueOf(i));
        }

        Long result = redisCache.zadd(key, scoreMap);
        redisCache.expire(key, timeout);

        return result;
    }

    public List<String> zrangeByScore(String key, long start, long end) {
        Set<String> set = redisCache.zrangeByScore(key, start, end);
        if (CollectionUtils.isEmpty(set)) {
            return new ArrayList<>();
        }
        return set.stream().collect(Collectors.toList());
    }

    public long zcard(String key) {
        return redisCache.zcard(key);
    }

    public void pipelineSet(Map<String, String> keyMap, int seconds) {
        redisCache.pipeline(p -> keyMap.forEach((key, value) -> {
            p.set(key, value);
            p.expire(key, seconds);
        }));
    }

    /**
     * Sets 操作
     * 返回指定所有的集合的成员的交集
     *
     * @param keys keys
     * @return 结果集成员的列表
     */
    public Set<String> sinter(Collection<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return Collections.emptySet();
        }
        return redisCache.sinter(keys.toArray(new String[0]));
    }

    public Set<String> sunion(Collection<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return Collections.emptySet();
        }
        return redisCache.sunion(keys.toArray(new String[0]));
    }

    public Object evalScript(String script, List<String> searchKeys) {
        return redisCache.eval(script, searchKeys, Collections.singletonList("temp_key"));
    }

    public <T> T evalScript(String script, List<String> keys, String... arg) {
        log.debug("evalScript keys：{}, arg:{}, script:{}", keys, arg, script);
        String sha512Hex = DigestUtils.sha256Hex(script);
        Boolean exists = redisCache.scriptExists(sha512Hex);
        if (!exists) {
            log.warn("script not exists!, sha512Hex:{}", sha512Hex);
            sha512Hex = redisCache.scriptLoad(script);
        }
        return (T) redisCache.evalsha(sha512Hex, keys, Lists.newArrayList(arg));
    }

    public void hscanAndConsumer(String key, int limit, Consumer<List<Map.Entry<String, String>>> consumer) {
        ScanParams scanParams = new ScanParams();
        scanParams.count(limit);
        String cursor = "0";
        do {
            // 防止循环过程中,redis 过期失效
            if (getExpireTime(key) < 15) {
                expire(key, 100);
            }
            ScanResult<Map.Entry<String, String>> result = redisCache.hscan(key, cursor, scanParams);
            cursor = result.getCursor();
            List<Map.Entry<String, String>> resultResult = result.getResult();
            consumer.accept(resultResult);
        } while (!"0".equals(cursor));

    }

    public boolean exists(String key) {
        return BooleanUtils.isTrue(redisCache.exists(key));
    }
}
