package com.facishare.paas.appframework.metadata.expression;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by zhouwr on 2018/8/22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimpleExpression {
    private String id;
    private String expression;
    private String returnType;
    private boolean nullAsZero;
    private int decimalPlaces;
    private String label;

    //是否试算
    private Boolean debug;

    private List<VariableInfo> extVariables;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class VariableInfo {
        private String id;
        private String type;

        public static VariableInfo of(String id, String type) {
            return VariableInfo.builder().id(id).type(type).build();
        }
    }
}
