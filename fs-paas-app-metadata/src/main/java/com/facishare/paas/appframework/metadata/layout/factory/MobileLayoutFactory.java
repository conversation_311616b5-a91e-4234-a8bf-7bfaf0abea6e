package com.facishare.paas.appframework.metadata.layout.factory;

import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.layout.MobileLayoutBuilder;
import com.facishare.paas.appframework.metadata.layout.MobileLayoutProcessorImpl;
import org.springframework.stereotype.Component;

@Component
public class MobileLayoutFactory implements ExtraLayoutFactory {

    @Override
    public String getLayoutType() {
        return LayoutTypes.MOBILE;
    }

    @Override
    public LayoutExt getLayoutExt(LayoutProcessorContext context) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OPEN_SIDEBAR_LAYOUT_GRAY, RequestContextManager.getContext().getTenantId())) {
            return new MobileLayoutProcessorImpl(context.getPageType(), context.getWebLayout(),
                    context.getDescribeExt(), context.getObjectData(), context.getComponentConfig())
                    .processLayout();
        }
        return MobileLayoutBuilder
                .builder()
                .webLayout(context.getWebLayout())
                .describeExt(context.getDescribeExt())
                .componentConfig(context.getComponentConfig())
                .pageType(context.getPageType())
                .objectData(context.getObjectData())
                .build()
                .getMobileLayout();
    }
}
