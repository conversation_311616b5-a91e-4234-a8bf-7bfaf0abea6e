package com.facishare.paas.appframework.metadata.dto;

import lombok.*;

import java.util.Arrays;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ImportTenantSetting {
    private ImportSetting insertImport;
    private ImportSetting updateImport;


    @Getter
    public enum ImportMethod {
        NORMAL("normal"),
        ADD_ACTION("addAction");

        private final String method;

        ImportMethod(String method) {
            this.method = method;
        }

        public static ImportMethod of(String importMethod) {
            return Arrays.stream(ImportMethod.values()).filter(x -> Objects.equals(x.getMethod(), importMethod)).findFirst().orElse(NORMAL);
        }
    }
}
