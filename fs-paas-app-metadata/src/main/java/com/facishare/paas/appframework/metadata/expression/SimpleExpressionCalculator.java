package com.facishare.paas.appframework.metadata.expression;

import com.facishare.paas.expression.ExpressionService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.Optional;
import java.util.Set;

/**
 * Created by zhouwr on 2018/8/22
 */
public class SimpleExpressionCalculator extends AbstractFieldCalculator {

    private SimpleExpression simpleExpression;

    public SimpleExpressionCalculator(ExpressionService service, SimpleExpression simpleExpression, IObjectDescribe objectDescribe) {
        super(service, createExpression(objectDescribe, simpleExpression), objectDescribe);
        this.simpleExpression = simpleExpression;
    }

    @Override
    public String key() {
        return simpleExpression.getId();
    }

    public static Expression createExpression(IObjectDescribe describe, SimpleExpression simpleExpression) {
        return Expression.builder()
                .expression(simpleExpression.getExpression() == null ? "" : simpleExpression.getExpression())
                .describe(describe)
                .returnType(simpleExpression.getReturnType())
                .nullAsZero(simpleExpression.isNullAsZero())
                .decimalPlaces(simpleExpression.getDecimalPlaces())
                .expressionLabel(Optional.ofNullable(simpleExpression.getLabel()).orElse(simpleExpression.getId()))
                .fieldName(simpleExpression.getId())
                .debug(Boolean.TRUE.equals(simpleExpression.getDebug()))
                .build();
    }
}
