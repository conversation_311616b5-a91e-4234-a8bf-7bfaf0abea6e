package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.ObjectDataSnapshot;
import com.facishare.paas.appframework.metadata.relation.CalculateFields;
import com.facishare.paas.appframework.metadata.relation.CalculateRelation.RelateField;
import com.facishare.paas.appframework.metadata.relation.EditCalculateParam;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 元数据服务Action的接口
 * 包括:
 * <p>
 * Created by yusb on 2017/12/14.
 */
public interface MetaDataComputeService {

    void calculateAndUpdateByOriginalData(User user, String objectApiName, String dataId, Map<String, Object> beforeData);

    Map<String, Map<String, Object>> calculateAndUpdateFormulaFields(User user, String objectApiName, List<String> dataIds,
                                                                     List<String> fieldApiNames, boolean calculateRelateField);

    Map<String, Object> calculateCountField(User user, String masterObjectApiName, String masterObjectDataId, List<Count> countFields);

    void calculateCountField(List<IObjectData> objectDataList, IObjectDescribe objectDescribe, List<Count> countList);

    void calculateCountField(IObjectData masterObjectData, IObjectDescribe masterDescribe, IObjectDescribe detailDescribe, List<Count> countFields);

    void calculateCountField(IObjectData masterObjectData, IObjectDescribe masterDescribe, IObjectDescribe detailDescribe,
                             List<Count> countFields, IActionContext context);

    void calculateCountFieldFromDB(IObjectData masterObjectData, IObjectDescribe masterDescribe,
                                   IObjectDescribe detailDescribe, List<Count> countFields);

    void calculateCountFieldFromDB(IObjectData masterObjectData, IObjectDescribe masterDescribe,
                                   IObjectDescribe detailDescribe, List<Count> countFields, IActionContext context);

    Object getCountValue(String tenantId, Count count, ISearchTemplateQuery query);

    Object getCountValueWithoutFormat(String tenantId, Count count, ISearchTemplateQuery query);

    /**
     *
     * @param tenantId
     * @param count
     * @param query
     * @param statOnEmptyResult 统计时结果为空期望的返回值 "null"、 "zero"、 "default"，默认为"default"，即 db 返回 null， es 返回 0
     * @return
     */
    Object getCountValueWithoutFormat(String tenantId, Count count, ISearchTemplateQuery query, String statOnEmptyResult);

    Object getCountValue(String tenantId, Count count, ISearchTemplateQuery query, IActionContext context, boolean needDataAuth, IObjectDescribe describe);

    @Deprecated
    Object getCountValueWithFunctionalCurrency(User user, Count count, ISearchTemplateQuery query);

    Object getCountValue(User user, Count count, ISearchTemplateQuery query);

    BigDecimal getAggregateResult(User user, String describeApiName, String countFieldApiName, String countType,
                                  int decimalPlaces, ISearchTemplateQuery query);

    IObjectData calculateExpressionForCreateData(User user, String recordType, IObjectDescribe describe);

    IObjectData calculateExpressionForCreateData(User user, String recordType, IObjectDescribe describe,
                                                 Consumer<IObjectData> modifyDataBeforeCalculate);

    void calculateAndUpdateCountFields(User user, List<String> masterDataIds, IObjectDescribe masterDescribe, List<Count> countFieldList);

    void calculateAndUpdateMasterCountFields(IActionContext context, List<String> masterDataIds, IObjectDescribe masterDescribe,
                                             IObjectDescribe detailDescribe, List<Count> countFieldList);

    void calculateAndUpdateMasterCountFieldsWithData(IActionContext context, IObjectDescribe masterDescribe, List<IObjectData> masterDataList,
                                                     IObjectDescribe detailDescribe, List<Count> countFieldList);

    void calculateCountFieldsInMemory(IObjectData masterData, Map<String, List<IObjectData>> detailDataMap, Map<String, IObjectDescribe> describeMap);

    void calculateCountFieldsInMemory(IObjectData masterData, Map<String, List<IObjectData>> detailDataMap,
                                      Map<String, IObjectDescribe> describeMap, List<Count> countFields);

    void calculateCountFieldsWithDetailDataAndDbValue(IObjectData masterData, List<IObjectData> detailDataList, IObjectDescribe masterDescribe,
                                                      IObjectDescribe detailDescribe, List<Count> countFieldList);

    void calculateCountFieldsWithDbDetailDataAndDbValue(IObjectData masterData, List<IObjectData> detailDataList, IObjectDescribe masterDescribe,
                                                        IObjectDescribe detailDescribe, List<Count> countFieldList, List<IObjectData> dbDetailDataList);

    List<String> batchCalculate(User user, List<IObjectData> dataList, IObjectDescribe describe, List<String> calculateFieldNames,
                                boolean calculateFormulaOnly, boolean mergeData);

    void batchCalculateBySortFields(User user,
                                    List<IObjectData> dataList,
                                    Map<String, IObjectDescribe> describeMap,
                                    Map<String, List<RelateField>> calculateFieldMap);

    //按照构图排序计算
    void batchCalculateBySortFieldsWithDetailData(User user,
                                                  IObjectData masterData,
                                                  Map<String, List<CalculateObjectData>> detailCalculateDataMap,
                                                  Map<String, IObjectDescribe> describeMap,
                                                  Map<String, List<RelateField>> calculateFieldMap);

    void batchCalculateBySortFieldsWithDetailData(User user,
                                                  CalculateObjectData masterCalculateData,
                                                  Map<String, List<CalculateObjectData>> detailCalculateDataMap,
                                                  Map<String, IObjectDescribe> describeMap,
                                                  Map<String, List<RelateField>> calculateFieldMap,
                                                  boolean calculateSeparately);

    void batchCalculateBySortFields(User user,
                                    IObjectData masterData,
                                    Map<String, List<IObjectData>> detailDataMap,
                                    CalculateFields calculateFields);

    void calculateForClone(User user, IObjectDescribe objectDescribe, IObjectData objectData, List<IObjectDescribe> detailDescribes,
                           Map<String, List<IObjectData>> detailDataMap);

    /**
     * UI事件触发计算
     *
     * @param user                            用户信息
     * @param masterData                      主对象的数据
     * @param detailDataMap                   从对象的数据
     * @param masterChangedFields             主对象变更的字段
     * @param detailChangedFieldMap           从对象变更的字段
     * @param detailApiNameHasAddOrDeleteData 新增或删除了数据的从对象
     * @return 主对象上重新计算的字段列表
     */
    List<String> calculateForUIEvent(User user,
                                     IObjectData masterData,
                                     Map<String, List<IObjectData>> detailDataMap,
                                     List<String> masterChangedFields,
                                     Map<String, List<String>> detailChangedFieldMap,
                                     List<String> detailApiNameHasAddOrDeleteData);

    /**
     * 草稿箱计算
     *
     * @param user
     * @param objectDescribe
     * @param objectData
     * @param detailDescribes
     * @param detailDataMap
     */
    void calculateForDrafts(User user, IObjectDescribe objectDescribe, IObjectData objectData, List<IObjectDescribe> detailDescribes,
                            Map<String, List<IObjectData>> detailDataMap);

    void calculateForDrafts(User user, IObjectDescribe objectDescribe, IObjectData objectData, List<IObjectDescribe> detailDescribes,
                            Map<String, List<IObjectData>> detailDataMap, Map<String, List<String>> skipCalculateFields);

    void calculateForDrafts(User user, IObjectDescribe objectDescribe, IObjectData objectData, List<IObjectDescribe> detailDescribes,
                            Map<String, List<IObjectData>> detailDataMap, Map<String, List<String>> skipCalculateFields,
                            Boolean skipCalculateDVField);

    IObjectData calculateForSnapshot(User user, IObjectDescribe describe, IObjectData dbData, ObjectDataSnapshot snapshot);

    IObjectData calculateDefaultValueWithConstant(User user, IObjectData objectData, IObjectDescribe objectDescribe);

    void calculateForBatchCreate(User user, List<IObjectData> dataList, IObjectDescribe describe, boolean excludeDefaultValue);

    void calculateForAddAction(User user, IObjectData masterData, Map<String, List<IObjectData>> detailDataMap,
                               Map<String, IObjectDescribe> describeMap, boolean excludeDefaultValue, boolean excludeLookupRelateField);

    void calculateForEditData(User user, EditCalculateParam editCalculateParam);

    CalculateFields calculateForUIEventWithGray(User user, EditCalculateParam editCalculateParam, boolean doCalculate);

    Map<String, Map<String, Object>> calculateForBatchEditData(User user, List<IObjectData> objectData, List<IObjectData> dbObjectData, IObjectDescribe objectDescribe);
}
