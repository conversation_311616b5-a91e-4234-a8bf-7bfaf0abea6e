package com.facishare.paas.appframework.metadata.domain;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.domain.DomainPluginDefinition.Action;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by zhouwr on 2021/10/19.
 */
@Data
public class SimpleDomainPluginDescribe {
    private String apiName;
    private Map<String, Action> actions;
    private DomainPluginParam params;
    private List<String> recordTypeList;

    public String getRestApiUrl(String key) {
        if (CollectionUtils.empty(actions)) {
            return null;
        }
        Action action = actions.get(key);
        return action == null ? null : action.getRestApiUrl();
    }

    public List<String> masterFields(List<String> fieldKeys) {
        if (Objects.isNull(params)) {
            return Lists.newArrayList();
        }
        return params.masterFields(fieldKeys);
    }
}
