package com.facishare.paas.appframework.metadata.relation;

import com.facishare.paas.appframework.common.graph.DirectedAcyclicGraphExt;
import com.facishare.paas.appframework.common.graph.Graphs;
import com.facishare.paas.appframework.common.graph.ValueGraph;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.*;
import lombok.Getter;
import lombok.experimental.Delegate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2018/11/15
 */
public class FieldRelationGraph {

    public static final ImmutableSet<RelateType> RELATE_TYPES = ImmutableSet.of(RelateType.D2M, RelateType.M2D, RelateType.S2S);
    public static final ImmutableSet<RelateType> D2M_S2S_RELATE_TYPES = ImmutableSet.of(RelateType.D2M, RelateType.S2S);
    @Getter
    @Delegate
    private final ValueGraph<FieldNode, RelateEdge> graph;

    @Getter
    private final Map<String, IObjectDescribe> describeMap;

    private final Table<String, String, FieldNode> nodeTable = HashBasedTable.create();

    private FieldRelationGraph(ValueGraph<FieldNode, RelateEdge> graph, Map<String, IObjectDescribe> describeMap) {
        this.graph = graph;
        this.describeMap = describeMap;
        graph.nodes().forEach(node -> nodeTable.put(node.getObjectApiName(), node.getFieldApiName(), node));
    }

    public static FieldRelationGraph of(ValueGraph<FieldNode, RelateEdge> graph, Map<String, IObjectDescribe> describeMap) {
        return new FieldRelationGraph(graph, describeMap);
    }

    public FieldRelationGraph transpose() {
        return FieldRelationGraph.of(Graphs.transpose(graph), describeMap);
    }

    public Set<FieldNode> reachableNodes(FieldNode node) {
        //Graphs.reachableNodes的返回值包含node本身，返回之前需要remove掉
        Set<FieldNode> reachableNodes = Sets.newLinkedHashSet(Graphs.reachableNodes(graph, node));
        reachableNodes.remove(node);
        return reachableNodes;
    }

    public Optional<FieldNode> getNode(String objectApiName, String fieldApiName) {
        return Optional.ofNullable(nodeTable.get(objectApiName, fieldApiName));
    }

    public Set<FieldNode> getRelateNodes(FieldNode node, Set<String> expectedObjectApiNames) {
        Deque<NodeEdgePair> nodeEdgePairs = Lists.newLinkedList();
        Set<NodeEdgePair> result = fetchNodesFromGraph(nodeEdgePairs, node, expectedObjectApiNames);
        return result.stream().map(NodeEdgePair::getFieldNode).collect(Collectors.toSet());
    }

    public Set<FieldNode> getRelateNodes(FieldNode node, Set<String> describeApiNames, String tenantId) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FIELD_RELATION_GRAPH_GRAY, tenantId)) {
            Set<FieldNode> fieldNodes = reachableNodes(node);
            // 过滤掉不在describeMap中且无法回到新建页面对象的节点
            return filterRelateNodesByPageObjects(fieldNodes, describeApiNames);
        }
        return getRelateNodes(node, describeApiNames);
    }

    private Set<FieldNode> filterRelateNodesByPageObjects(Set<FieldNode> fieldNodes, Set<String> pageObjectApiNames) {
        Set<FieldNode> needRemove = fieldNodes.stream()
                .filter(fieldNode -> !pageObjectApiNames.contains(fieldNode.getObjectApiName()))
                .filter(fieldNode -> reachableNodes(fieldNode).stream().noneMatch(it -> pageObjectApiNames.contains(it.getObjectApiName())))
                .collect(Collectors.toSet());
        fieldNodes.removeAll(needRemove);
        return fieldNodes;
    }

    private Set<NodeEdgePair> fetchNodesFromGraph(Deque<NodeEdgePair> nodeEdgePairs,
                                                  FieldNode node,
                                                  Set<String> expectedObjectApiNames) {
        Set<NodeEdgePair> result = Sets.newHashSet();
        Set<FieldNode> successors = graph.successors(node);
        if (CollectionUtils.empty(successors)) {
            result.addAll(NodeEdgePair.filterNodeEdgePair(nodeEdgePairs, expectedObjectApiNames));
            return result;
        }

        for (FieldNode rNode : successors) {
            RelateEdge relateEdge = graph.edgeValue(node, rNode).orElse(null);
            // 当前节点为默认值，且不在主从新建页面展示的对象范围内
            if (isEndNode(node, expectedObjectApiNames, rNode, relateEdge)) {
                result.addAll(NodeEdgePair.filterNodeEdgePair(nodeEdgePairs, expectedObjectApiNames));
                continue;
            }
            nodeEdgePairs.offer(NodeEdgePair.of(rNode, relateEdge));
            Set<NodeEdgePair> nodeEdgePairSet = fetchNodesFromGraph(nodeEdgePairs, rNode, expectedObjectApiNames);
            result.addAll(nodeEdgePairSet);
            // 清除本次加入的节点，避免对下一次循环的影响
            nodeEdgePairs.pollLast();
        }
        return result;
    }

    private boolean isEndNode(FieldNode node, Set<String> expectedObjectApiNames, FieldNode rNode, RelateEdge relateEdge) {
        //没有关联关系的异常节点
        if (Objects.isNull(relateEdge)) {
            return true;
        }
        //关联对象的默认值
        if (rNode.isDefaultValue() && !expectedObjectApiNames.contains(rNode.getObjectApiName())) {
            return true;
        }
        //从当前页面对象出发，跨了两级对象的节点
        return !expectedObjectApiNames.contains(node.getObjectApiName())
                && !expectedObjectApiNames.contains(rNode.getObjectApiName())
                && !relateEdge.allMatchRelateType(RelateType.S2S);
    }

    private boolean isNeedCalculateDefaultValue(Set<String> expectedObjectApiNames, RelateEdge relateEdge, FieldNode node) {
        if (node.isDefaultValue()
                && expectedObjectApiNames.contains(node.getObjectApiName())
                && !FieldDescribeExt.isMultiCurrencyFields(node.getFieldApiName())) {
            return relateEdge.getRelateEdgeNodes().stream().anyMatch(it -> D2M_S2S_RELATE_TYPES.contains(it.getRelateType()));
        }
        return false;
    }

    public boolean hasRoute(FieldNode nodeU, FieldNode nodeV) {
        return DirectedAcyclicGraphExt.hasRoute(graph, nodeU, nodeV);
    }

    public void fetchRelateNodes(List<FieldNode> nodeList, List<String> allRelateNodes, List<FieldNode> relateNodes) {
        List<String> formulaNodes = nodeList.stream().map(FieldNode::getFieldApiName).filter(fieldApiName -> !allRelateNodes.contains(fieldApiName)).collect(Collectors.toList());
        List<FieldNode> newRelateNodes = Lists.newArrayList();
        relateNodes.forEach(x -> {
            Set<FieldNode> predecessors = graph.predecessors(x).stream().filter(y -> formulaNodes.contains(y.getFieldApiName())).filter(FieldNode::isFormula).collect(Collectors.toSet());
            if (CollectionUtils.notEmpty(predecessors)) {
                newRelateNodes.addAll(predecessors);
            }
        });
        if (CollectionUtils.notEmpty(newRelateNodes)) {
            List<String> newRelateApiNames = newRelateNodes.stream().map(FieldNode::getFieldApiName).collect(Collectors.toList());
            allRelateNodes.addAll(newRelateApiNames);
            nodeList.removeAll(newRelateNodes);
            fetchRelateNodes(nodeList, allRelateNodes, newRelateNodes);
        }
    }

    public Set<NodeEdgePair> getDependentCalculateNodeEdgePairsWithinLookup(FieldNode node, Set<String> expectedObjectApiNames) {
        Set<NodeEdgePair> dependentNodeSet = Sets.newHashSet();
        return fetchDependentNodeEdgePairsFromGraphWithinLookup(dependentNodeSet, node, expectedObjectApiNames);
    }

    /**
     * @param dependentNodeSet       上一级依赖字段链路
     * @param node                   临时根节点
     * @param expectedObjectApiNames 当前页面对象
     * @return 从临时根节点出发，经过查找关联对象的计算和统计字段，最后回到当前页面对象的链路
     */
    private Set<NodeEdgePair> fetchDependentNodeEdgePairsFromGraphWithinLookup(Set<NodeEdgePair> dependentNodeSet, FieldNode node, Set<String> expectedObjectApiNames) {
        Set<NodeEdgePair> result = Sets.newHashSet();
        graph.predecessors(node).forEach(lNode -> {
            Optional<RelateEdge> relateEdgeOptional = graph.edgeValue(lNode, node);
            if (!relateEdgeOptional.isPresent()) {
                return;
            }

            //使用上一层的依赖字段列表构造临时依赖字段链路
            Set<NodeEdgePair> tmpDependentNodeSet = Sets.newHashSet(dependentNodeSet);
            if (lNode.isFormula() || isNeedCalculateDefaultValue(expectedObjectApiNames, relateEdgeOptional.get(), lNode)) {
                //计算字段或当前页面对象的默认值加入临时依赖字段链路
                tmpDependentNodeSet.add(NodeEdgePair.of(lNode, relateEdgeOptional.get()));
                //计算字段或当前页面对象的默认值需要递归查找依赖字段
                Set<NodeEdgePair> nodeEdgePairs = fetchDependentNodeEdgePairsFromGraphWithinLookup(tmpDependentNodeSet, lNode, expectedObjectApiNames);
                result.addAll(nodeEdgePairs);
            } else if (lNode.isCountField()) {
                if (expectedObjectApiNames.contains(lNode.getObjectApiName())) {
                    return;
                }
                Count count = (Count) describeMap.get(lNode.getObjectApiName()).getFieldDescribe(lNode.getFieldApiName());
                if (!expectedObjectApiNames.contains(count.getSubObjectDescribeApiName())) {
                    return;
                }
                //直到链路上遇到统计当前页面对象的统计字段，将该统计字段以及整个临时依赖字段列表加入返回值中
                relateEdgeOptional.map(relateEdge -> NodeEdgePair.of(lNode, relateEdge)).ifPresent(tmpDependentNodeSet::add);
                result.addAll(tmpDependentNodeSet);
            }
        });
        return result;
    }

    public Optional<IFieldDescribe> getFieldDescribeByFieldNode(FieldNode fieldNode) {
        return Optional.ofNullable(describeMap.get(fieldNode.getObjectApiName()))
                .map(describe -> describe.getFieldDescribe(fieldNode.getFieldApiName()));
    }

}
