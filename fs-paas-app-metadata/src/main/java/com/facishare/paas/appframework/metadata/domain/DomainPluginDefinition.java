package com.facishare.paas.appframework.metadata.domain;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.model.RequestType;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.layout.LayoutAgentType;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2021/11/9.
 */
@Data
public class DomainPluginDefinition {
    private String apiName;
    private String type;
    private String label;
    private String description;
    //插件的执行顺序，按照从大到小排序
    private int order;
    //是否支持后台管理
    private boolean supportManagement;
    private Map<String, ResourceList> resources;
    private Map<String, Map<String, Action>> actions;
    private Map<String, Map<String, Action>> controllers;
    private PrivilegeConfig privilegeConfig;
    private List<ParamDefinition> paramDefinition;
    private Map<String, Object> managementConfig;
    //是否可选插件，如果为true，则表示该插件不是必须执行，旧版本的客户端可忽略该插件。
    private Boolean optional;

    // 插件中下发给前端的自定义业务配置
    private Map<String, List<CustomBizConfig>> customBizConfig;

    public String type() {
        DomainPluginType pluginType = DomainPluginType.of(type);
        return Objects.isNull(pluginType) ? DomainPluginType.Domain.getCode() : pluginType.getCode();
    }

    public boolean supportDetailObj() {
        if (Objects.isNull(managementConfig)) {
            return true;
        }
        Object value = managementConfig.get("supportDetailObj");
        if (Objects.isNull(value)) {
            return true;
        }
        return !Objects.equals(value, 0);
    }

    public String i18nLabel() {
        return I18NExt.getOrDefault(getLabelI18NKey(apiName), label);
    }

    public String i18nDescription() {
        return I18NExt.getOrDefault(getDescriptionI18NKey(apiName), description);
    }

    private String getLabelI18NKey(String pluginApiName) {
        return String.format("paas.domain_plugin.%s.label", pluginApiName);
    }

    private String getDescriptionI18NKey(String pluginApiName) {
        return String.format("paas.domain_plugin.%s.description", pluginApiName);
    }

    public List<Map<String, Object>> masterFieldDefinitions() {
        if (CollectionUtils.empty(paramDefinition)) {
            return Lists.newArrayList();
        }
        return paramDefinition.stream().filter(ParamDefinition::typeIsFieldMapping).findFirst().map(ParamDefinition::getFields).orElse(Lists.newArrayList());
    }

    public Map<String, List<Map<String, Object>>> detailFieldDefinitionMap() {
        if (CollectionUtils.empty(paramDefinition)) {
            return Maps.newHashMap();
        }
        return paramDefinition.stream().filter(ParamDefinition::typeIsDetailMapping).collect(Collectors.toMap(ParamDefinition::getDetailKey, ParamDefinition::getFields));
    }

    public List<String> masterReadOnlyFields() {
        if (CollectionUtils.empty(paramDefinition)) {
            return Lists.newArrayList();
        }
        return paramDefinition.stream().filter(ParamDefinition::typeIsFieldMapping).findFirst().map(ParamDefinition::readOnlyFields).orElse(Lists.newArrayList());
    }

    public Map<String, List<String>> detailReadOnlyFieldMap() {
        if (CollectionUtils.empty(paramDefinition)) {
            return Maps.newHashMap();
        }
        return paramDefinition.stream().filter(ParamDefinition::typeIsDetailMapping).collect(Collectors.toMap(ParamDefinition::getDetailKey, ParamDefinition::readOnlyFields));
    }

    public List<String> masterDisableCloneFields() {
        if (CollectionUtils.empty(paramDefinition)) {
            return Lists.newArrayList();
        }
        return paramDefinition.stream().filter(ParamDefinition::typeIsFieldMapping).findFirst().map(ParamDefinition::disableCloneFields).orElse(Lists.newArrayList());
    }

    public Map<String, List<String>> detailDisableCloneFieldMap() {
        if (CollectionUtils.empty(paramDefinition)) {
            return Maps.newHashMap();
        }
        return paramDefinition.stream().filter(ParamDefinition::typeIsDetailMapping).collect(Collectors.toMap(ParamDefinition::getDetailKey, ParamDefinition::disableCloneFields));
    }

    public List<String> masterHiddenFields(String pageType) {
        if (CollectionUtils.empty(paramDefinition) || Strings.isNullOrEmpty(pageType)) {
            return Lists.newArrayList();
        }
        return paramDefinition.stream().filter(ParamDefinition::typeIsFieldMapping).findFirst().map(x -> x.hiddenFields(pageType)).orElse(Lists.newArrayList());
    }

    public Map<String, List<String>> detailHiddenFieldMap(String pageType) {
        if (CollectionUtils.empty(paramDefinition) || Strings.isNullOrEmpty(pageType)) {
            return Maps.newHashMap();
        }
        return paramDefinition.stream().filter(ParamDefinition::typeIsDetailMapping).collect(Collectors.toMap(ParamDefinition::getDetailKey, x -> x.hiddenFields(pageType)));
    }

    public DomainPluginDefinition copy() {
        DomainPluginDefinition definition = new DomainPluginDefinition();
        definition.setApiName(apiName);
        definition.setType(type);
        definition.setLabel(label);
        definition.setDescription(description);
        definition.setOrder(order);
        definition.setOptional(optional);
        definition.setSupportManagement(supportManagement);
        Map<String, ResourceList> newResources;
        if (Objects.nonNull(resources)) {
            newResources = Maps.newLinkedHashMap();
            resources.forEach((k, v) -> newResources.put(k, v.copy()));
        } else {
            newResources = null;
        }
        definition.setResources(newResources);
        Map<String, Map<String, Action>> newActions = copyActions(actions);
        definition.setActions(newActions);
        Map<String, Map<String, Action>> newControllers = copyActions(controllers);
        definition.setControllers(newControllers);
        definition.setPrivilegeConfig(Objects.isNull(privilegeConfig) ? null : privilegeConfig.copy());
        definition.setParamDefinition(Objects.isNull(paramDefinition) ? null :
                paramDefinition.stream().map(ParamDefinition::copy).collect(Collectors.toList()));
        definition.setManagementConfig(Objects.isNull(managementConfig) ? null : Maps.newHashMap(managementConfig));
        definition.setCustomBizConfig(copyCustomBizConfig());
        return definition;
    }

    private Map<String, Map<String, Action>> copyActions(Map<String, Map<String, Action>> actions) {
        Map<String, Map<String, Action>> newActions;
        if (Objects.nonNull(actions)) {
            newActions = Maps.newLinkedHashMap();
            actions.forEach((k, v) -> {
                Map<String, Action> actionMap = Maps.newLinkedHashMap();
                v.forEach((k1, v1) -> actionMap.put(k1, v1.copy()));
                newActions.put(k, actionMap);
            });
        } else {
            newActions = null;
        }
        return newActions;
    }

    private Map<String, List<CustomBizConfig>> copyCustomBizConfig() {
        if (Objects.isNull(customBizConfig)) {
            return null;
        }
        Map<String, List<CustomBizConfig>> result = Maps.newLinkedHashMap();
        customBizConfig.forEach((k, v) -> {
            if (CollectionUtils.notEmpty(v)) {
                result.put(k, Lists.newArrayList(v));
            } else {
                result.put(k, Collections.emptyList());
            }
        });
        return result;
    }

    public DomainPluginDefinition processI18NProps() {
        label = i18nLabel();
        description = i18nDescription();
        if (CollectionUtils.notEmpty(paramDefinition)) {
            paramDefinition.forEach(x -> x.processI18NProps(apiName));
        }
        return this;
    }

    public List<String> dependencies() {
        List<String> dependencies = Objects.isNull(privilegeConfig) ? new ArrayList<>() :
                (Objects.isNull(privilegeConfig.getDependencies()) ? new ArrayList<>() : privilegeConfig.getDependencies());
        if (CollectionUtils.empty(dependencies)) {
            return dependencies;
        }
        return dependencies.stream().filter(DomainPluginDefinitionHolder::contains).collect(Collectors.toList());
    }

    public List<CustomBizConfig> getCustomBizConfigList() {
        if (CollectionUtils.empty(customBizConfig)) {
            return Collections.emptyList();
        }
        return customBizConfig.values().stream()
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    @Data
    public static class PrivilegeConfig {
        private List<String> supportObjectApiNames;
        private List<String> licenseVersions;
        private List<String> licenseModules;
        private List<BizConfig> bizConfig;
        //依赖插件的apiName集合(只有依赖插件都启用了，才能启用本插件)
        private List<String> dependencies;

        private Boolean checkOnRunTime;

        public PrivilegeConfig copy() {
            PrivilegeConfig config = new PrivilegeConfig();
            config.setSupportObjectApiNames(Objects.isNull(supportObjectApiNames) ? null : Lists.newArrayList(supportObjectApiNames));
            config.setLicenseVersions(Objects.isNull(licenseVersions) ? null : Lists.newArrayList(licenseVersions));
            config.setLicenseModules(Objects.isNull(licenseModules) ? null : Lists.newArrayList(licenseModules));
            config.setBizConfig(Objects.isNull(bizConfig) ? null : bizConfig.stream().map(BizConfig::copy).collect(Collectors.toList()));
            config.setDependencies(Objects.isNull(dependencies) ? null : Lists.newArrayList(dependencies));
            config.setCheckOnRunTime(checkOnRunTime);
            return config;
        }
    }

    @Data
    public static class ParamDefinition {
        public static final String LABEL_I18N_KEY = "label_i18n_key";
        public static final String LABEL_I18N_KEY_TEMPLATE = "paas.udobj.domain_plugin.%s.field.%s.label";
        public static final String DETAIL_LABEL_I18N_KEY_TEMPLATE = "paas.udobj.domain_plugin.%s.%s.field.%s.label";

        private String type;
        private String detailKey;
        private String redirectUrl;
        private List<Map<String, Object>> fields;

        public ParamDefinition copy() {
            ParamDefinition result = new ParamDefinition();
            result.setType(this.type);
            result.setDetailKey(this.detailKey);
            result.setRedirectUrl(this.redirectUrl);
            if (Objects.nonNull(this.fields)) {
                result.setFields(this.fields.stream().map(x -> Maps.newLinkedHashMap(x)).collect(Collectors.toList()));
            }
            return result;
        }

        public boolean typeIsFieldMapping() {
            return "fieldMapping".equals(this.type);
        }

        public boolean typeIsDetailMapping() {
            return "detailMapping".equals(this.type);
        }

        public ParamDefinition processI18NProps(String pluginApiName) {
            if (CollectionUtils.empty(fields)) {
                return this;
            }
            fields.forEach(field -> {
                String labelI18NKey = (String) field.get(LABEL_I18N_KEY);
                if (StringUtils.isEmpty(labelI18NKey)) {
                    String fieldApiName = (String) field.get(IFieldDescribe.API_NAME);
                    if (typeIsDetailMapping()) {
                        //从对象的字段需要把detailKey也拼到多语key里
                        labelI18NKey = String.format(DETAIL_LABEL_I18N_KEY_TEMPLATE, pluginApiName, detailKey, fieldApiName);
                    } else {
                        labelI18NKey = String.format(LABEL_I18N_KEY_TEMPLATE, pluginApiName, fieldApiName);
                    }
                }
                String defaultLabel = (String) field.get(IFieldDescribe.LABEL);
                String label = I18NExt.getOrDefault(labelI18NKey, defaultLabel);
                if (!Objects.equals(defaultLabel, label)) {
                    field.put(IFieldDescribe.LABEL, label);
                }
            });
            return this;
        }

        public List<String> readOnlyFields() {
            if (CollectionUtils.empty(fields)) {
                return Lists.newArrayList();
            }
            return fields.stream()
                    .filter(x -> Boolean.TRUE.equals(x.get(IFormField.IS_READ_ONLY)))
                    .map(x -> (String) x.get(IFieldDescribe.API_NAME))
                    .collect(Collectors.toList());
        }

        public List<String> disableCloneFields() {
            if (CollectionUtils.empty(fields)) {
                return Lists.newArrayList();
            }
            return fields.stream()
                    .filter(x -> Boolean.FALSE.equals(x.get(FieldDescribeExt.ENABLE_CLONE)))
                    .map(x -> (String) x.get(IFieldDescribe.API_NAME))
                    .collect(Collectors.toList());
        }

        public List<String> hiddenFields(String pageType) {
            if (CollectionUtils.empty(fields)) {
                return Lists.newArrayList();
            }
            return fields.stream()
                    .filter(x -> {
                        Object hiddenPages = x.get(FieldDescribeExt.HIDDEN_PAGES);
                        if (!(hiddenPages instanceof List)) {
                            return false;
                        }
                        return (((List) hiddenPages).contains(pageType) || ((List) hiddenPages).contains(AppFrameworkConfig.ALL));
                    })
                    .map(x -> (String) x.get(IFieldDescribe.API_NAME))
                    .collect(Collectors.toList());
        }
    }

    @Data
    public static class BizConfig {
        private String key;
        private String value;

        public BizConfig copy() {
            BizConfig bizConfig = new BizConfig();
            bizConfig.setKey(key);
            bizConfig.setValue(value);
            return bizConfig;
        }
    }

    public List<Resource> getResources(String actionCode, String agentType) {
        if (CollectionUtils.empty(resources)) {
            return null;
        }
        String pageType = getPageTypeByActionCode(actionCode);
        ResourceList resourceList = resources.get(pageType);
        if (resourceList == null) {
            return null;
        }
        return resourceList.getByAgentType(agentType);
    }

    private String getPageTypeByActionCode(String actionCode) {
        if ("Add".equalsIgnoreCase(actionCode) || "Edit".equalsIgnoreCase(actionCode)) {
            return "Form";
        }
        if ("WebDetail".equalsIgnoreCase(actionCode)) {
            return "Detail";
        }
        return actionCode;
    }

    @Data
    public static class ResourceList {
        private List<Resource> web;
        private List<Resource> mobile;

        public List<Resource> getByAgentType(String agentType) {
            if (LayoutAgentType.MOBILE.getCode().equals(agentType)) {
                return mobile;
            }
            return web;
        }

        public ResourceList copy() {
            ResourceList resourceList = new ResourceList();
            resourceList.setWeb(Objects.isNull(web) ? null : web.stream().map(Resource::copy).collect(Collectors.toList()));
            resourceList.setMobile(Objects.isNull(mobile) ? null : mobile.stream().map(Resource::copy).collect(Collectors.toList()));
            return resourceList;
        }
    }

    @Data
    public static class Resource {
        private String pluginType;
        private String resourceUrl;
        private String detailKey;

        public Resource copy() {
            Resource resource = new Resource();
            resource.setPluginType(pluginType);
            resource.setResourceUrl(resourceUrl);
            resource.setDetailKey(detailKey);
            return resource;
        }
    }

    @Data
    public static class Action {
        private String restApiUrl;

        public Action copy() {
            Action action = new Action();
            action.setRestApiUrl(restApiUrl);
            return action;
        }
    }

    public Map<String, Action> getActionsByCode(String actionCode, RequestType requestType) {
        if (RequestType.Action == requestType) {
            if (CollectionUtils.empty(actions)) {
                return null;
            }
            return actions.get(actionCode);
        } else {
            if (CollectionUtils.empty(controllers)) {
                return null;
            }
            return controllers.get(actionCode);
        }
    }

    @Data
    public static class CustomBizConfig {
        private final String key;
        private final String defaultValue;

        @JSONCreator
        private CustomBizConfig(@JSONField(name = "key") String key,
                                @JSONField(name = "defaultValue") String defaultValue) {
            this.key = key;
            this.defaultValue = defaultValue;
        }
    }

}
