package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.RichTextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.social.feeds.model.XTRichTextV2JsonRoot;
import com.facishare.social.feeds.model.XTRichTextV2Root;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RichTextImportFieldDataConverter extends BaseImportFieldDataConverter {

    @Override
    public List<String> getSupportedFieldTypes() {
        return Lists.newArrayList(IFieldType.RICH_TEXT);
    }

    @Override
    public ConvertResult convertFieldData(IObjectData objectData, IObjectDescribe objectDescribe, IFieldDescribe fieldDescribe, User user) {
        if (!AppFrameworkConfig.cooperativeRichTextGray(user.getTenantId(), objectDescribe.getApiName())) {
            return ConvertResult.buildSuccess(objectData.get(fieldDescribe.getApiName()));
        }
        String richTextValue = getStringValue(objectData, fieldDescribe.getApiName());
        JSONObject xtRichTextJsonObj = convertTextToRichText(richTextValue);
        objectData.set(RichTextExt.getRichTextAbstractName(fieldDescribe.getApiName()), richTextValue);
        objectData.set(fieldDescribe.getApiName(), xtRichTextJsonObj);
        return ConvertResult.buildSuccess(xtRichTextJsonObj);
    }

    private JSONObject convertTextToRichText(String richTextValue) {
        JSONObject xtRichTextJsonObj = new JSONObject();
        final XTRichTextV2Root xtRichTextV2Root = new XTRichTextV2Root();
        final XTRichTextV2JsonRoot xtRichTextV2JsonRoot = JSON.parseObject("{\"type\":\"doc\",\"content\":[{\"type\":\"paragraph\",\"content\":[{\"text\":\"\",\"type\":\"text\"}]}]}",
                XTRichTextV2JsonRoot.class);
        ((JSONArray) xtRichTextV2JsonRoot.getContent().get(0).get("content")).getJSONObject(0).put("text", richTextValue);
        xtRichTextV2Root.set__json(xtRichTextV2JsonRoot);
        xtRichTextJsonObj.put("__xt", xtRichTextV2Root);
        xtRichTextJsonObj.put("text", richTextValue);
        return xtRichTextJsonObj;
    }
}