package com.facishare.paas.appframework.metadata.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportSetting {
    @JsonProperty("insert_import_switch")
    private Boolean insertImportSwitch;
    @JsonProperty("update_import_switch")
    private Boolean updateImportSwitch;
    @JsonProperty("open_approval_flow")
    private Boolean openApprovalFlow;
    @JsonProperty("trigger_approval_flow")
    private Boolean triggerApprovalFlow;
    @JsonProperty("open_work_flow")
    private Boolean openWorkFlow;
    @JsonProperty("trigger_work_flow")
    private Boolean triggerWorkFlow;
    @JsonProperty("describe_api_name")
    private String describeApiName;
    @JsonProperty("import_method")
    private String importMethod;
    @JsonProperty("gray_import_add_action")
    private boolean grayImportAddAction;
}
