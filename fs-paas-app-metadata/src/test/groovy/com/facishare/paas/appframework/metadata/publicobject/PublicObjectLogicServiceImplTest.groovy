package com.facishare.paas.appframework.metadata.publicobject

import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobInfo
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobStatus
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobType
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import spock.lang.Specification
import spock.lang.Unroll

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/19
 */
class PublicObjectLogicServiceImplTest extends Specification {

    @Unroll
    def "test filterWithJobType jobType: #jobType, jobStatus: #jobStatus, isPublicObject:#isPublicObject"() {
        setup:
        // Create an instance of the service
        def service = new PublicObjectLogicServiceImpl()

        // Create a mock PublicObjectJobInfo
        def jobInfo = Mock(PublicObjectJobInfo)

        // Create a mock IObjectDescribe
        def objectDescribe = Mock(IObjectDescribe)

        when:
        // Define the behavior of the mock objects
        jobInfo.getJobType() >> jobType
        jobInfo.getJobStatus() >> jobStatus
        objectDescribe.isPublicObject() >> isPublicObject

        // Call the method under test
        def result = service.filterWithJobType(jobInfo, objectDescribe)
        then:
        // Assert the result
        assert result == repectResult

        where:
        jobType                        | jobStatus                     | isPublicObject || repectResult
        PublicObjectJobType.OPEN_JOB   | PublicObjectJobStatus.SUCCESS | true           || true
        PublicObjectJobType.OPEN_JOB   | PublicObjectJobStatus.FAILED  | true           || false
        PublicObjectJobType.OPEN_JOB   | PublicObjectJobStatus.WAITING | true           || false
        PublicObjectJobType.CLOSE_JOB  | PublicObjectJobStatus.FAILED  | true           || true
        PublicObjectJobType.CLOSE_JOB  | PublicObjectJobStatus.SUCCESS | true           || false
        PublicObjectJobType.CLOSE_JOB  | PublicObjectJobStatus.RUNNING | true           || true

        PublicObjectJobType.VERIFY_JOB | PublicObjectJobStatus.SUCCESS | false          || true
        PublicObjectJobType.VERIFY_JOB | PublicObjectJobStatus.FAILED  | false          || true
        PublicObjectJobType.VERIFY_JOB | PublicObjectJobStatus.WAITING | false          || true
        PublicObjectJobType.VERIFY_JOB | PublicObjectJobStatus.RUNNING | false          || true
        PublicObjectJobType.OPEN_JOB   | PublicObjectJobStatus.FAILED  | false          || true
        PublicObjectJobType.OPEN_JOB   | PublicObjectJobStatus.SUCCESS | false          || false
        PublicObjectJobType.OPEN_JOB   | PublicObjectJobStatus.WAITING | false          || true
        PublicObjectJobType.CLOSE_JOB  | PublicObjectJobStatus.SUCCESS | false          || true
        PublicObjectJobType.CLOSE_JOB  | PublicObjectJobStatus.FAILED  | false          || false
        PublicObjectJobType.CLOSE_JOB  | PublicObjectJobStatus.RUNNING | false          || false
    }
}
