package com.facishare.paas.appframework.metadata.expression;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.expression.ExpressionService;
import com.facishare.paas.expression.ExpressionServiceImpl;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.impl.I18nServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ReflectionUtils;

import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ExpressionTest {

    @Mock
    private ExpressionService expressionService;
    
    @Mock
    private IObjectDescribe describe;

    @BeforeEach
    void setUp() {
        // 设置I18nClient单例
        I18nClient i18nClient = mock(I18nClient.class);
        I18nServiceImpl i18nServiceImpl = mock(I18nServiceImpl.class);
        ReflectionTestUtils.setField(i18nClient, "impl", i18nServiceImpl);
        
        // 处理final字段
        Field field = ReflectionUtils.findField(I18nClient.class, "SINGLETON");
        ReflectionUtils.makeAccessible(field);
        ReflectionUtils.setField(field, null, i18nClient);
        
        when(i18nClient.getAllLanguage()).thenReturn(java.util.Collections.emptyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doCalculate方法处理币种分支逻辑
     */
    @ParameterizedTest
    @MethodSource("provideCurrencyTestData")
    @DisplayName("测试doCalculate币种分支逻辑")
    void testDoCalculate_CurrencyBranch(String fieldName, String evaluateResult, String expectedResult) {
        // 准备测试数据
        Expression expression = Expression.builder()
                .expression("AND(NOT(ISNULL(null)),null>100)")
                .fieldName(fieldName)
                .isDefaultValue(true)
                .describe(describe)
                .build();
        expression.setLatestExchangeRate("1.0");
        expression.setFunctionalCurrency("1.0");
        expression.setLatestExchangeRateVersion("8");

        // 配置Mock行为
        when(expressionService.evaluate(anyString(), any())).thenReturn(evaluateResult);

        // 执行被测试方法
        Object result = expression.doCalculate(expressionService);

        // 验证结果
        assertEquals(expectedResult, result);
    }

    /**
     * 提供币种测试数据
     */
    private static Stream<Arguments> provideCurrencyTestData() {
        return Stream.of(
            Arguments.of("mc_exchange_rate", "", "1.0"),
            Arguments.of("mc_exchange_rate_version", "", "8"),
            Arguments.of("mc_functional_currency", "", "1.0")
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doCalculate方法处理Decimal类型的小数位处理
     */
    @ParameterizedTest
    @MethodSource("provideDecimalTestData")
    @DisplayName("测试doCalculate - Decimal小数位处理")
    void testDoCalculate_DecimalPlaces(int decimalPlaces, Object evaluateResult, Object expectedResult) {
        // 准备测试数据
        Expression expression = Expression.builder()
                .expression("AND(NOT(ISNULL(null)),null>100)")
                .decimalPlaces(decimalPlaces)
                .describe(describe)
                .build();

        // 配置Mock行为
        when(expressionService.evaluate(anyString(), any())).thenReturn(evaluateResult);

        // 执行被测试方法
        Object result = expression.doCalculate(expressionService);

        // 验证结果
        assertEquals(expectedResult, result);
    }

    /**
     * 提供Decimal测试数据
     */
    private static Stream<Arguments> provideDecimalTestData() {
        return Stream.of(
            Arguments.of(2, 1.276, java.math.BigDecimal.valueOf(1.28)),
            Arguments.of(2, 1.271, java.math.BigDecimal.valueOf(1.27)),
            Arguments.of(3, 1.271, java.math.BigDecimal.valueOf(1.271))
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doCalculate方法处理异常情况
     */
    @Test
    @DisplayName("测试doCalculate - 异常处理")
    void testDoCalculate_Exception() {
        // 准备测试数据
        Expression expression = Expression.builder()
                .expression("AND(NOT(ISNULL(null)),null>100)")
                .describe(describe)
                .build();

        // 配置Mock行为 - 抛出异常
        when(expressionService.evaluate(anyString(), any())).thenThrow(new NullPointerException());

        // 执行被测试方法
        Object result = expression.doCalculate(expressionService);

        // 验证结果
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doCalculate方法处理不同字段类型的返回值格式化
     */
    @ParameterizedTest
    @MethodSource("provideFieldTypeTestData")
    @DisplayName("测试doCalculate - 不同字段类型")
    void testDoCalculate_DifferentFieldTypes(String returnType, Object evaluateResult, Object expectedResult) {
        // 准备测试数据
        Expression expression = Expression.builder()
                .expression("AND(NOT(ISNULL(null)),null>100)")
                .returnType(returnType)
                .describe(describe)
                .build();

        // 配置Mock行为
        when(expressionService.evaluate(anyString(), any())).thenReturn(evaluateResult);

        // 执行被测试方法
        Object result = expression.doCalculate(expressionService);

        // 验证结果
        assertEquals(expectedResult, result);
    }

    /**
     * 提供字段类型测试数据
     */
    private static Stream<Arguments> provideFieldTypeTestData() {
        return Stream.of(
            Arguments.of("ARRAY", java.util.Arrays.asList("a", "b"), "[a, b]"),
            Arguments.of("EMPLOYEE", java.util.Arrays.asList("a", "b"), java.util.Arrays.asList("a", "b")),
            Arguments.of("EMPLOYEE_MANY", java.util.Arrays.asList("a", "b"), java.util.Arrays.asList("a", "b")),
            Arguments.of("DEPARTMENT", java.util.Arrays.asList("a", "b"), java.util.Arrays.asList("a", "b")),
            Arguments.of("DEPARTMENT_MANY", java.util.Arrays.asList("a", "b"), java.util.Arrays.asList("a", "b"))
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试compileCheck方法处理不同字段类型的编译检查，特别是DateTime字段的时区处理
     */
    @ParameterizedTest
    @MethodSource("provideCompileCheckTestData")
    @DisplayName("测试compileCheck - 不同字段类型编译检查")
    void testCompileCheck_DifferentFieldTypes(String expressionValue, Class<? extends Exception> expectedException) {
        // 准备测试数据 - 使用mock对象替代文件读取
        Map<String, Object> mockDescribeData = java.util.Collections.singletonMap("api_name", "object_2I7ye__c");
        ObjectDescribeExt object_2I7ye__c = ObjectDescribeExt.of(mockDescribeData);
        
        Expression expression = Expression.builder()
                .describe(object_2I7ye__c)
                .expression(expressionValue)
                .fieldName("field_GQi6n__c")
                .returnType("TEXT")
                .build();
        expression.init();
        expression.getGlobalVariables().forEach(x -> x.setDataType(ExpressionDataType.DateTime));

        // 使用MockedStatic模拟静态方法
        try (MockedStatic<AppFrameworkConfig> mockedStatic = mockStatic(AppFrameworkConfig.class)) {
            mockedStatic.when(() -> AppFrameworkConfig.isDateTimeSupportNotUseMultitimeZoneTenant("74255"))
                    .thenReturn(true);

            // 执行并验证异常
            Exception exception = assertThrows(expectedException, () -> {
                expression.compileCheck(expressionService, "74255");
            });

            // 验证异常类型
            assertTrue(expectedException.isInstance(exception));
        }
    }

    /**
     * 提供编译检查测试数据
     */
    private static Stream<Arguments> provideCompileCheckTestData() {
        return Stream.of(
            Arguments.of("$field_nTe6N__c$+$currentDateTime__g$", ExpressionCalculateException.class),
            Arguments.of("$field_nTe6N__c$+DATETIMEVALUE(NOW())", ExpressionCalculateException.class)
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Expression的基本构建和初始化功能
     */
    @Test
    @DisplayName("测试Expression基本构建")
    void testExpressionBuilder_BasicConstruction() {
        // 准备测试数据
        String expressionStr = "AND(NOT(ISNULL(null)),null>100)";
        
        // 执行被测试方法
        Expression expression = Expression.builder()
                .expression(expressionStr)
                .returnType("TEXT")
                .nullAsZero(true)
                .decimalPlaces(2)
                .useValue(false)
                .describe(describe)
                .isFormula(true)
                .fieldName("testField")
                .expressionLabel("Test Expression")
                .ignoreInvalidVariable(false)
                .isDefaultValue(false)
                .debug(false)
                .build();

        // 验证结果
        assertNotNull(expression);
        assertEquals(expressionStr, ReflectionTestUtils.getField(expression, "expression"));
        assertEquals(ExpressionDataType.String, ReflectionTestUtils.getField(expression, "returnType"));
        assertTrue((Boolean) ReflectionTestUtils.getField(expression, "nullAsZero"));
        assertEquals(2, ReflectionTestUtils.getField(expression, "decimalPlaces"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Expression的静态工厂方法
     */
    @Test
    @DisplayName("测试Expression静态工厂方法")
    void testExpressionOf_StaticFactoryMethod() {
        // 准备测试数据
        String expressionStr = "1+1";
        
        // 执行被测试方法
        Expression expression = Expression.of(expressionStr);

        // 验证结果
        assertNotNull(expression);
        assertEquals(expressionStr, ReflectionTestUtils.getField(expression, "expression"));
    }
} 