package com.facishare.paas.appframework.log;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.common.util.TenantUtil;
import com.facishare.paas.appframework.common.util.Tuple;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public abstract class AuditLogConfig {
    public static final String UDOBJ = "UDOBJ";
    public static final String ALL = "ALL";
    private static final Splitter CONFIG_SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();
    private static final Splitter CONFIG_SPLITTER2 = Splitter.on(";").omitEmptyStrings().trimResults();
    public static List<Map<String, String>> log_operation_object = Lists.newArrayList();
    public static Map<String, String> log_action_type = Maps.newHashMap();
    public static Set<String> gray_new_log_tenant = Sets.newHashSet();
    public static Map<String, List<String>> operation_log_relationship = Maps.newHashMap();
    public static Map<String, List<String>> tenantQueryInterval = Maps.newHashMap();
    public static Set<String> gray_query_interval_tenant = Sets.newHashSet();
    public static Map<String, String> log_module_group = Maps.newHashMap();
    public static Set<String> gray_log_module_tenant = Sets.newHashSet();
    public static List<LogTenantInterval> tenant_log_interval_relation = Lists.newArrayList();
    public static Set<String> auditLogChReadGray = Sets.newHashSet();

    static {
        ConfigFactory.getConfig("fs-paas-auditlog-config", iConfig -> {
            log.warn("reload config fs-paas-auditlog-config,content={}", iConfig.toString());
            log_module_group = parseMapFromConfig(iConfig, "log_module_group");
            log_operation_object = getListMapFromConfig(iConfig, "log_operation_object");
            log_action_type = parseMapFromConfig(iConfig, "log_action_type");
            gray_new_log_tenant = getSetFromConfig(iConfig, "gray_new_log_tenant");
            gray_query_interval_tenant = getSetFromConfig(iConfig, "gray_query_interval_tenant");
            tenantQueryInterval = getMapListFromConfig(iConfig, "tenant_query_interval");
            operation_log_relationship = parseMapFromConfig(iConfig, "operation_log_relationship");
            gray_log_module_tenant = getSetFromConfig(iConfig, "gray_log_module_tenant");
            tenant_log_interval_relation = parseListObjectFromConfig(iConfig, "tenant_log_interval_relation", LogTenantInterval.class);
            auditLogChReadGray = getSetFromConfig(iConfig, "auditLogChReadGray");
        });
    }

    public static boolean isGrayAuditLogChRead(String tenantId) {
        return auditLogChReadGray.contains(ALL) || auditLogChReadGray.contains(tenantId);
    }

    public static boolean isGrayQueryIntervalTenant(String tenantId) {
        return AuditLogConfig.gray_query_interval_tenant.contains(ALL) || AuditLogConfig.gray_query_interval_tenant.contains(tenantId);
    }

    private static Set<String> getSetFromConfig(IConfig config, String key) {
        return Sets.newHashSet(CONFIG_SPLITTER.split(config.get(key, "")));
    }

    private static List<Map<String, String>> getListMapFromConfig(IConfig config, String key) {
        String data = config.get(key, "");
        if (Strings.isNullOrEmpty(data)) {
            return Lists.newArrayList();
        }
        return (List<Map<String, String>>) JSONArray.parse(data);
    }

    @SuppressWarnings("unchecked")
    private static <T> Map<String, T> parseMapFromConfig(IConfig config, String key) {
        String data = config.get(key, "");
        if (Strings.isNullOrEmpty(data)) {
            return Maps.newHashMap();
        }
        return JSON.parseObject(data, Map.class);
    }

    private static <T> List<T> parseListObjectFromConfig(IConfig config, String key, Class clazz) {
        String data = config.get(key, "");
        if (StringUtils.isBlank(data)) {
            return Lists.newArrayList();
        }
        return JacksonUtils.fromJson(data, List.class, clazz);
    }

    private static Map<String, List<String>> getMapListFromConfig(IConfig config, String key) {
        String configStr = config.get(key);
        if (StringUtils.isBlank(configStr)) {
            return Maps.newHashMap();
        }
        return CONFIG_SPLITTER2.splitToList(configStr).stream()
                .map(it -> Tuple.of(StringUtils.substringBefore(it, ":"), StringUtils.substringAfter(it, ":")))
                .collect(Collectors.toMap(Tuple::getKey, it -> CONFIG_SPLITTER.splitToList(it.getValue())));
    }

    public static String getTenantQueryInterval(String tenantId, String logType) {
        String interval = null;
        if (AuditLogConfig.isGrayQueryIntervalTenant(tenantId) || TenantUtil.isNewAuditLogTenant(tenantId)) {
            Optional<LogTenantInterval> logTenantInterval = tenant_log_interval_relation.stream()
                    .filter(x -> Objects.equals(x.getLogType(), logType)).findFirst();
            if (logTenantInterval.isPresent()) {
                Map<String, String> tenantAndInterval = LogTenantInterval.of(logTenantInterval.get());
                interval = tenantAndInterval.get(tenantId);
                if (StringUtils.isBlank(interval)) {
                    interval = tenantAndInterval.get(logType);
                }
            }
        }
        return interval;
    }

    @Data
    public static class LogTenantInterval {
        private String logType;
        private Map<String, Set<String>> queryInterval;

        public static Map<String, String> of(LogTenantInterval logTenantInterval) {
            Map<String, String> tenantAndInterval = Maps.newHashMap();
            Map<String, Set<String>> intervalQueryInterval = logTenantInterval.getQueryInterval();
            CollectionUtils.nullToEmpty(intervalQueryInterval).forEach((interval, tenantIds) -> {
                tenantIds.forEach(tenantId -> {
                    tenantAndInterval.put(tenantId, interval);
                });
            });
            return tenantAndInterval;
        }
    }

}
