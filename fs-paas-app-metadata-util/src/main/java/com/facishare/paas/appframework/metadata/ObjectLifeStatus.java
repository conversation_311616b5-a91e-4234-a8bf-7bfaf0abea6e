package com.facishare.paas.appframework.metadata;


import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * IObjectData类的LifeStatus字段的状态机
 */
@Slf4j
public enum ObjectLifeStatus {
    INEFFECTIVE("ineffective", "未生效") {
        @Override
        public boolean startCallBack(IObjectData data, ApprovalFlowTriggerType triggerType, ApprovalFlowStartResult startResult) {
            if (triggerType == ApprovalFlowTriggerType.CREATE) {
                return ObjectDataExt.of(data).createApprovalStartCallback(startResult);
            }
            if (triggerType == ApprovalFlowTriggerType.CHANGE_OWNER || triggerType == ApprovalFlowTriggerType.CUSTOM_BUTTON) {
                return ObjectDataExt.of(data).updateApprovalStartCallback(startResult);
            }
            return false;
        }

        @Override
        public boolean completeCallBack(IObjectData data, ApprovalFlowTriggerType triggerType, boolean isPass) {
            if (triggerType == ApprovalFlowTriggerType.CUSTOM_BUTTON) {
                return true;
            }
            // 增加日志，以定位这种异常的审批流回调
            log.warn("dataStatusAbnormal id:{},apiName:{},triggerType:{},isPass:{}", data.getId(), data.getName(), triggerType, isPass);
            return false;
        }
    },
    UNDER_REVIEW("under_review", "审核中") {
        @Override
        public boolean startCallBack(IObjectData data, ApprovalFlowTriggerType triggerType, ApprovalFlowStartResult startResult) {
            return false;
        }

        @Override
        public boolean completeCallBack(IObjectData data, ApprovalFlowTriggerType triggerType, boolean isPass) {
            if (triggerType == ApprovalFlowTriggerType.CREATE) {
                ObjectDataExt.of(data).createApprovalCompleteCallBack(isPass);
                return true;
            }
            if (triggerType == ApprovalFlowTriggerType.CUSTOM_BUTTON) {
                return true;
            }
            if (triggerType == ApprovalFlowTriggerType.CHANGE_OWNER) {
                return true;
            }
            return false;
        }
    },
    NORMAL("normal", "正常") {
        @Override
        public boolean startCallBack(IObjectData data, ApprovalFlowTriggerType triggerType, ApprovalFlowStartResult startResult) {

            switch (triggerType) {
                case UPDATE:
                case CHANGE_OWNER:
                case CUSTOM_BUTTON:
                case STAGE_CHANGE:
                case RETURN:
                case CHOOSE:
                case CLOSE:
                case CHANGE_PARTNER:
                case DELETE_PARTNER:
                case CHANGE_PARTNER_OWNER:
                case EXTEND_EXPIRETIME:
                case TRANSFER:
                case CLOSE_TPM_ACTIVITY:
                case CLOSE_ACTIVITY_AGREEMENT:
                    return ObjectDataExt.of(data).updateApprovalStartCallback(startResult);
                case INVALID:
                    return ObjectDataExt.of(data).invalidApprovalStartCallback(startResult);
                default:
                    return false;
            }
        }

        @Override
        public boolean completeCallBack(IObjectData data, ApprovalFlowTriggerType triggerType, boolean isPass) {
            return ApprovalFlowTriggerType.CUSTOM_BUTTON.equals(triggerType) && ObjectDescribeExt.isSFAObject(data.getDescribeApiName());
        }
    },
    IN_CHANGE("in_change", "变更中") {
        @Override
        public boolean startCallBack(IObjectData data, ApprovalFlowTriggerType triggerType, ApprovalFlowStartResult startResult) {
            return false;
        }

        @Override
        public boolean completeCallBack(IObjectData data, ApprovalFlowTriggerType triggerType, boolean isPass) {

            switch (triggerType) {
                case UPDATE:
                case CHANGE_OWNER:
                case CUSTOM_BUTTON:
                case STAGE_CHANGE:
                case CHOOSE:
                case RETURN:
                case CLOSE:
                case CHANGE_PARTNER:
                case DELETE_PARTNER:
                case CHANGE_PARTNER_OWNER:
                case EXTEND_EXPIRETIME:
                case TRANSFER:
                case CLOSE_TPM_ACTIVITY:
                case CLOSE_ACTIVITY_AGREEMENT:
                    ObjectDataExt.of(data).updateApprovalCompleteCallBack(isPass);
                    return true;
                case INVALID:
                    ObjectDataExt.of(data).invalidApprovalCompleteCallBack(isPass);
                    return true;
                default:
                    return false;
            }
        }
    },
    INVALID("invalid", "作废") {
        @Override
        public boolean startCallBack(IObjectData data, ApprovalFlowTriggerType triggerType, ApprovalFlowStartResult startResult) {
            return false;
        }

        @Override
        public boolean completeCallBack(IObjectData data, ApprovalFlowTriggerType triggerType, boolean isPass) {
            return false;
        }
    };

    public static ObjectLifeStatus of(String lifeStatusText) {
        return of(lifeStatusText, false);
    }

    public static ObjectLifeStatus of(String lifeStatusText, boolean isInvalid) {
        if (Strings.isNullOrEmpty(lifeStatusText)) {
            return isInvalid ? INVALID : NORMAL;
        }
        for (ObjectLifeStatus lifeStatus : ObjectLifeStatus.values()) {
            if (lifeStatus.getCode().equals(lifeStatusText)) {
                return lifeStatus;
            }
        }
        throw new ValidateException(I18N.text(I18NKey.UNKNOW_LIFE_STATUS, lifeStatusText));
    }

    ObjectLifeStatus(String code, String label) {
        this.code = code;
    }

    private String code;

    public String getCode() {
        return code;
    }

    public abstract boolean startCallBack(IObjectData data, ApprovalFlowTriggerType triggerType, ApprovalFlowStartResult startResult);

    public abstract boolean completeCallBack(IObjectData data, ApprovalFlowTriggerType triggerType, boolean isPass);

    public static final String LIFE_STATUS_API_NAME = "life_status";
    public static final String LIFE_STATUS_BEFORE_INVALID_API_NAME = "life_status_before_invalid";
    public static final String LAST_LIFE_STATUS_API_NAME = "last_life_status";
    public static final List<String> LIFE_STATUS_FIELD = ImmutableList.of(ObjectLifeStatus.LIFE_STATUS_API_NAME);
    public static final List<String> LIFE_STATUS_AND_LAST_LIFE_STATUS_BEFORE_INVALID_FIELD = ImmutableList.of(ObjectLifeStatus.LIFE_STATUS_API_NAME,
            ObjectLifeStatus.LIFE_STATUS_BEFORE_INVALID_API_NAME);
}