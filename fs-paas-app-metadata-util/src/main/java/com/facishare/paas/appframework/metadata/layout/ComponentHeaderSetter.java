
package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.layout.component.ISuspendedComponentInfo;
import com.facishare.paas.metadata.impl.ui.layout.TabSection;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.TabsComponent;
import com.facishare.paas.metadata.ui.layout.*;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.i18n.I18NKey.LAYOUT_BASIC_INFO;
import static com.facishare.paas.appframework.metadata.FormComponentExt.BASE_FIELD_SECTION_API_NAME;
import static com.facishare.paas.metadata.ui.layout.ILayout.I18N_INFO_LIST;

/**
 * Created by zhouwr on 2020/7/29
 */
@Builder
public class ComponentHeaderSetter {

    private String tenantId;
    private String layoutType;
    private LayoutVersion layoutVersion;
    private List<IComponent> components;
    private List<ISuspendedComponentInfo> suspendedComponents;
    private String objectApiName;
    private String layoutApiName;
    private Boolean existMultiLanguage;
    @Builder.Default
    private Map<String, String> componentPreKeyMap = Maps.newHashMap();
    private Map<String, Localization> localizationMap;
    private String sourceType;
    private String refLayoutApiName;

    public static String SOURCE_TYPE_DESIGNER = "designer";

    public void reset() {
        if (StringUtils.isBlank(tenantId)) {
            tenantId = RequestContextManager.getContext().getTenantId();
        }
        if (CollectionUtils.empty(components)) {
            return;
        }
        if (LayoutTypes.DETAIL.equals(layoutType)) {
            refLayoutApiName = components.stream().filter(x -> ComponentExt.of(x).isFormType())
                    .map(x -> FormComponentExt.of((IFormComponent) x).getReferenceFieldConfig())
                    .filter(StringUtils::isNotEmpty)
                    .findFirst()
                    .orElse(null);
        }
        Map<String, IComponent> componentMap = components.stream().collect(Collectors.toMap(IComponent::getName, x -> x, (x, y) -> x));
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.BUSINESS_COMPONENT_TRANSLATE, tenantId) && existMultiLanguage) {
            components.forEach(this::resetBusinessTranslate);
        }
        components.stream().filter(x -> !ComponentExt.of(x).isTabs()).forEach(this::resetComponentHeader);
        components.stream().filter(x -> ComponentExt.of(x).isFormType()).forEach(x -> resetFormHeader((FormComponent) x));
        components.stream().filter(x -> ComponentExt.of(x).isTabs()).forEach(x -> resetTabsHeader((TabsComponent) x, componentMap));
        resetSuspendedComponentsTranslate();
    }

    private void resetSuspendedComponentsTranslate() {
        if (!existMultiLanguage || CollectionUtils.empty(suspendedComponents)) {
            return;
        }
        suspendedComponents.forEach(this::resetBusinessTranslate);
        suspendedComponents.forEach(this::resetComponentHeader);
    }

    private void resetBusinessTranslate(IComponent component) {
        String componentHeaderKey = ComponentExt.getRealKeyWithComponent(component, layoutType, objectApiName, layoutApiName, refLayoutApiName, LayoutExt.getTextComponentNamesInTable(components));
        List<I18nInfo> i18nInfoList = ComponentExt.of(component).getI18nInfoList(objectApiName, layoutApiName,componentHeaderKey);
        if (CollectionUtils.empty(i18nInfoList)) {
            return;
        }
        List<I18nInfo> newI18nInfoList = Lists.newArrayList();
        for (I18nInfo i18nInfo : i18nInfoList) {
            I18nInfo newI18nInfo = i18nInfo.buildByTransValue(localizationMap, SOURCE_TYPE_DESIGNER.equals(sourceType));
            newI18nInfoList.add(newI18nInfo);
        }
        ComponentExt.of(component).setI18nInfo(newI18nInfoList, SOURCE_TYPE_DESIGNER.equals(sourceType));
    }

    //处理布局中分组名称翻译
    private void resetFormHeader(FormComponent x) {
        if (!existMultiLanguage) {
            return;
        }
        Map defaultValueMap = Maps.newHashMap();
        if (CollectionUtils.notEmpty(ComponentExt.of(x).getFieldSectionI18nInfoList())) {
            List<I18nInfo> i18nInfoList = ComponentExt.of(x).getFieldSectionI18nInfoList();
            defaultValueMap = i18nInfoList.stream()
                    .filter(Objects::nonNull) // Filter out null elements
                    .collect(Collectors.toMap(
                            I18nInfo::getApiName,
                            i18nInfo -> Optional.ofNullable(i18nInfo.getDefaultValue()).orElse("") ,
                            (existing, replacement) -> replacement// Handle null default values
                    ));
        }
        List<IFieldSection> fieldSections = x.getFieldSections();
        List<I18nInfo> i18nInfoList = Lists.newArrayList();
        for (IFieldSection fieldSection : fieldSections) {
            I18nInfo i18nInfo = new I18nInfo();
            String apiName = fieldSection.getName();
            String key = "";
            String refLayoutApiName = FormComponentExt.of(x).getReferenceFieldConfig();
            if (LayoutTypes.DETAIL.equals(layoutType)) {
                if (StringUtils.isBlank(refLayoutApiName)) {
                    key = ComponentExt.getDetailLayoutGroupName(objectApiName, layoutApiName, apiName);
                } else {
                    key = ComponentExt.getEditLayoutGroupName(objectApiName, refLayoutApiName, apiName);
                }
            } else {
                key = ComponentExt.getEditLayoutGroupName(objectApiName, layoutApiName, apiName);
            }
            String nameI18nKey = fieldSection.get(ComponentExt.NAME_I18N_KEY, String.class);
            if (StringUtils.isEmpty(nameI18nKey)) {
                if (BASE_FIELD_SECTION_API_NAME.equals(fieldSection.getName())) {
                    nameI18nKey = LAYOUT_BASIC_INFO;
                }
            }

            i18nInfo.setApiName(apiName);

            String header = getTransValue(localizationMap, key, Lists.newArrayList(nameI18nKey), fieldSection.getHeader());

            fieldSection.setHeader(header);
            i18nInfo.setValue(header);
            i18nInfo.setPreKey(nameI18nKey);
            if (defaultValueMap.containsKey(apiName)) {
                i18nInfo.setDefaultValue(String.valueOf(defaultValueMap.get(apiName)));
            } else {
                i18nInfo.setDefaultValue(fieldSection.getHeader());
            }
            i18nInfo.setCustomKey(key);
            i18nInfo.setType(ComponentExt.FIELD_SECTION);
            if (CollectionUtils.notEmpty(localizationMap) && localizationMap.containsKey(key)) {
                i18nInfo.convertLanguageInfo(localizationMap.get(key));
            }
            i18nInfoList.add(i18nInfo.buildByTransValue(localizationMap, SOURCE_TYPE_DESIGNER.equals(sourceType)));
        }
        x.set(I18N_INFO_LIST, i18nInfoList);
    }

    //处理分组基本信息的翻译
    private String dealBasicInfoGroup(IFieldSection fieldSection) {
        String header = fieldSection.getHeader();
        Map<String, String> allLangTextMap = I18NExt.getAllLangText(LAYOUT_BASIC_INFO);
        Collection<String> values = allLangTextMap.values();
        if (values.contains(fieldSection.getHeader())) {
            header = I18NExt.getOnlyText(LAYOUT_BASIC_INFO);
        }
        return header;
    }

    //处理布局中组件名称翻译
    private void resetComponentHeader(IComponent component) {
        ComponentExt componentExt = ComponentExt.of(component);
        //图表组件和嵌入页面保留用户自己配置的名称
        if (componentExt.isChartComponent()
                || componentExt.isIFrameComponent()
                || componentExt.isTabComponent()
                || componentExt.isNavigation()
                || componentExt.isRelatedListForm()) {
            componentExt.setNameI18nKey(null);
        } else {
            //相关对象和从对象列表组件直接从字段取多语
            String i18nKey = getComponentI18NKey(component);
            if (!Strings.isNullOrEmpty(i18nKey) && !componentExt.isRelatedBusinessComponent()) {
                componentExt.setNameI18nKey(i18nKey);
                componentExt.setHeaderByI18nKey(tenantId, i18nKey);
            }
        }
        List<String> nameI18nKeyList = componentExt.getNameI18nKeyList();
        if (StringUtils.isNotEmpty(componentExt.getNameI18nKey())) {
            nameI18nKeyList.add(componentExt.getNameI18nKey());
        } else {
            if (StringUtils.isNotEmpty(componentPreKeyMap.get(component.getName()))) {
                nameI18nKeyList.add(componentPreKeyMap.get(component.getName()));
            }
        }
        String defaultNameI18nKey = component.get("defaultNameI18nKey", String.class);
        if (!Strings.isNullOrEmpty(defaultNameI18nKey)) {
            nameI18nKeyList.add(defaultNameI18nKey);
        }
        String transValue = getTransValueByComponent(component, nameI18nKeyList);
        if (!Strings.isNullOrEmpty(transValue)) {
            componentExt.setComponentHeaderAndCleanUp(transValue);
        }
        if (componentExt.isTextComponent()) {
            String value = getTransValue(localizationMap,
                    ComponentExt.getTextComponentContentKey(objectApiName,
                            StringUtils.isEmpty(refLayoutApiName) ? layoutApiName : refLayoutApiName,
                            componentExt.getName(),
                            StringUtils.isEmpty(refLayoutApiName) ? layoutType : LayoutTypes.EDIT),
                    Lists.newArrayList(), componentExt.getContent());
            componentExt.setContent(value);
        }
    }

//    private String getPreKey(IComponent component, Map<String, String> componentPreKeyMap) {
//        String apiName = component.getName();
//        String nameI18nKey = componentPreKeyMap.get(apiName);
//        if (StringUtils.isEmpty(nameI18nKey)) {
//            nameI18nKey = StringUtils.defaultIfBlank(componentPreKeyMap.get(component.getType()), "");
//        }
//        return nameI18nKey;
//    }

    //获取组件或页签名称的翻译值
    private String getTransValueByComponent(IComponent component, List<String> nameI18nKeyList) {
        //不存在多语则不做翻译处理
        if (!existMultiLanguage) {
            return null;
        }
        String key = ComponentExt.getRealKeyWithComponent(component, layoutType, objectApiName, layoutApiName, refLayoutApiName, LayoutExt.getTextComponentNamesInTable(components));
        return getTransValue(localizationMap, key, nameI18nKeyList, ComponentExt.of(component).getHeader());
    }

    private String getTransValueByTabs(ITabSection tabSection, List<String> nameI18nKeyList, String layoutType) {
        //不存在多语则不做翻译处理
        if (!existMultiLanguage) {
            return null;
        }
        String key = "";
        if (LayoutTypes.LIST_LAYOUT.equals(layoutType)) {
            key = ComponentExt.getListLayoutTabNameKey(objectApiName, layoutApiName, tabSection.getApiName());
        } else if (LayoutTypes.DETAIL.equals(layoutType)) {
            key = ComponentExt.getDetailLayoutTabNameKey(objectApiName, layoutApiName, tabSection.getApiName());
        }
        return getTransValue(localizationMap, key, nameI18nKeyList, tabSection.getHeader());
    }

    //根据key获取翻译值
    private String getTransValue(Map<String, Localization> localizationMap, String key, List<String> nameI18nKeyList, String originalHeader) {
        String header;
        if (CollectionUtils.empty(localizationMap)) {
            header = I18NExt.getOnlyText(key);
        } else {
            Localization localization = localizationMap.get(key);
            header = Objects.nonNull(localization) ? localization.get(I18N.getContext().getLanguage(), null) : null;
        }
        //有新key走新key，没新key走老key，老key也没有返回描述上的值
        if (!Strings.isNullOrEmpty(header)) {
            return header;
        }
        Localization allTransValue = I18NExt.getAllTransValueByOrder(nameI18nKeyList, Integer.valueOf(tenantId));
        // 如果存在则代表没有改过
        if (allTransValue.getData().containsValue(originalHeader)) {
            return allTransValue.get(I18N.getContext().getLanguage(), originalHeader);
        } else {
            return originalHeader;
        }
    }

    //处理页签名称翻译
    private void resetTabsHeader(TabsComponent tabsComponent, Map<String, IComponent> componentMap) {
        //页签容器需要处理各个页签的header
        Iterator<TabSection> tabIterator = CollectionUtils.nullToEmpty(tabsComponent.getTabs()).iterator();
        Iterator<List<String>> childrenIterator = CollectionUtils.nullToEmpty(tabsComponent.getComponents()).iterator();
        while (tabIterator.hasNext()) {
            TabSectionExt tabSectionExt = TabSectionExt.of(tabIterator.next());
            List<String> children = childrenIterator.next();
            //只有V3版本布局的页签才需要nameI18nKey
            if (!Strings.isNullOrEmpty(tabSectionExt.getNameI18nKey()) && !isVersionV3()) {
                tabSectionExt.setNameI18nKey(null);
            }
            String i18nKey = tabSectionExt.getNameI18nKey();
            String transValue = getTransValueByTabs(tabSectionExt.getTabSection(),
                    StringUtils.isBlank(i18nKey) ? Lists.newArrayList() : Lists.newArrayList(i18nKey),
                    layoutType);
            if (!Strings.isNullOrEmpty(transValue)) {
                tabSectionExt.setHeader(transValue);
                continue;
            }
            List<IComponent> childComponents = children.stream().filter(componentMap::containsKey)
                    .map(componentMap::get).collect(Collectors.toList());
            //没有i18nKey并且组件数量不为1的页签不做处理
            if (Strings.isNullOrEmpty(i18nKey) && childComponents.size() != 1) {
                continue;
            }
            for (IComponent childComponent : childComponents) {
                String childI18nKey = getComponentI18NKey(childComponent);
                if (Strings.isNullOrEmpty(childI18nKey)) {
                    continue;
                }
                //开启了多语才需要从多语词条取名称
                Map<String, String> allLangText = I18NExt.getAllLangText(childI18nKey);
                allLangText.put(RequestUtil.getCurrentLang().getValue(), ComponentExt.of(childComponent).getHeader());
                if (Objects.equals(childI18nKey, i18nKey) || allLangText.containsValue(tabSectionExt.getHeader())) {
                    //组件名称已经查询多语，直接使用组件名称即可
                    tabSectionExt.setHeader(ComponentExt.of(childComponent).getHeader());
                    //只有V3版本布局的页签才需要补充nameI18nKey
                    if (isVersionV3()) {
                        tabSectionExt.setNameI18nKey(childI18nKey);
                    }
                    break;
                }
            }
        }
    }


    private String getComponentI18NKey(IComponent component) {
        ComponentExt componentExt = ComponentExt.of(component);

        //组件中已经存在i18nKey的，直接取多语
        String i18nKey = componentExt.getNameI18nKey();
        if (!Strings.isNullOrEmpty(i18nKey)) {
            return i18nKey;
        }
        //通用组件
        i18nKey = ComponentI18NKeys.getI18NKey(component.getName(), layoutType);
        if (!Strings.isNullOrEmpty(i18nKey)) {
            return i18nKey;
        }
        if (componentExt.isRelatedList()) {
            //相关对象
            IRelatedObjectList relatedList = (IRelatedObjectList) component;
            if (Strings.isNullOrEmpty(relatedList.getFieldApiName())) {
                return null;
            }
            i18nKey = FieldDescribeExt.getReferenceLabelI18NKey(relatedList.getRefObjectApiName(), relatedList.getFieldApiName());
        } else if (componentExt.isMasterDetailComponent()) {
            //从对象
            if (!(component instanceof IMultiTableComponent)) {
                return null;
            }
            IMultiTableComponent multiTable = (IMultiTableComponent) component;
            i18nKey = FieldDescribeExt.getReferenceLabelI18NKey(multiTable.getRefObjectApiName(), (String) multiTable.get(ComponentExt.FIELD_API_NAME));
        }
        return i18nKey;
    }

    /**
     * 只有详情页才区分是否为v3版本，其余布局，不区分，直接返回true
     *
     * @return
     */
    private boolean isVersionV3() {
        if (!LayoutTypes.DETAIL.equals(layoutType)) {
            return true;
        }
        return LayoutVersion.V3 == layoutVersion;
    }
}