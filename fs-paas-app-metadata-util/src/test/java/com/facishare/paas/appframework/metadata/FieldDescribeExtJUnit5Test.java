package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GenerateByAI
 * FieldDescribeExt的JUnit 5测试类
 * 测试字段描述扩展类功能
 */
class FieldDescribeExtJUnit5Test {

    private IFieldDescribe mockFieldDescribe;
    private FieldDescribeExt fieldDescribeExt;

    @BeforeEach
    void setUp() {
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("api_name", "test_field");
        fieldMap.put("type", IFieldType.TEXT);
        fieldMap.put("label", "Test Field");
        mockFieldDescribe = FieldDescribeFactory.newInstance(fieldMap);
        fieldDescribeExt = FieldDescribeExt.of(mockFieldDescribe);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of工厂方法 - IFieldDescribe参数
     */
    @Test
    @DisplayName("of - IFieldDescribe参数")
    void testOf_WithFieldDescribe() {
        FieldDescribeExt result = FieldDescribeExt.of(mockFieldDescribe);

        assertNotNull(result);
        assertSame(mockFieldDescribe, result.getFieldDescribe());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of工厂方法 - Map参数
     */
    @Test
    @DisplayName("of - Map参数")
    void testOf_WithMap() {
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("api_name", "map_field");
        fieldMap.put("type", IFieldType.NUMBER);

        FieldDescribeExt result = FieldDescribeExt.of(fieldMap);

        assertNotNull(result);
        assertEquals("map_field", result.getApiName());
        assertEquals(IFieldType.NUMBER, result.getType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLookupNameByFieldName静态方法
     */
    @Test
    @DisplayName("getLookupNameByFieldName - 生成查找字段名")
    void testGetLookupNameByFieldName() {
        String result = FieldDescribeExt.getLookupNameByFieldName("test_field");
        
        assertEquals("test_field__r", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLocalizationNameByFieldName静态方法
     */
    @Test
    @DisplayName("getLocalizationNameByFieldName - 生成本地化字段名")
    void testGetLocalizationNameByFieldName() {
        String result = FieldDescribeExt.getLocalizationNameByFieldName("test_field");
        
        assertEquals("test_field__geo", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFieldMappingFieldName静态方法
     */
    @Test
    @DisplayName("getFieldMappingFieldName - 生成字段映射名")
    void testGetFieldMappingFieldName() {
        String result = FieldDescribeExt.getFieldMappingFieldName("test_field");
        
        assertEquals("test_field__wrap", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getEmployeeNameByFieldName静态方法
     */
    @Test
    @DisplayName("getEmployeeNameByFieldName - 生成员工字段名")
    void testGetEmployeeNameByFieldName() {
        String result = FieldDescribeExt.getEmployeeNameByFieldName("employee_field");
        
        assertEquals("employee_field__l", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getSelectOther静态方法
     */
    @Test
    @DisplayName("getSelectOther - 生成选择其他字段名")
    void testGetSelectOther() {
        String result = FieldDescribeExt.getSelectOther("select_field");
        
        assertEquals("select_field__o", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getQuotedValueNameByFieldName静态方法
     */
    @Test
    @DisplayName("getQuotedValueNameByFieldName - 生成引用值字段名")
    void testGetQuotedValueNameByFieldName() {
        String result = FieldDescribeExt.getQuotedValueNameByFieldName("quote_field");
        
        assertEquals("quote_field__v", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getQuotedFunctionVirtualFieldByFieldName静态方法
     */
    @Test
    @DisplayName("getQuotedFunctionVirtualFieldByFieldName - 生成引用函数虚拟字段名")
    void testGetQuotedFunctionVirtualFieldByFieldName() {
        String result = FieldDescribeExt.getQuotedFunctionVirtualFieldByFieldName("lookup_field");
        
        assertEquals("lookup_field__q", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getShowFieldName静态方法
     */
    @Test
    @DisplayName("getShowFieldName - 生成显示字段名")
    void testGetShowFieldName() {
        String result = FieldDescribeExt.getShowFieldName("field");
        
        assertEquals("field__s", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getMultiLangExtraFieldName静态方法
     */
    @Test
    @DisplayName("getMultiLangExtraFieldName - 生成多语言额外字段名")
    void testGetMultiLangExtraFieldName() {
        String result = FieldDescribeExt.getMultiLangExtraFieldName("field");
        
        assertEquals("field__lang", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getMultiLangTempFieldName静态方法
     */
    @Test
    @DisplayName("getMultiLangTempFieldName - 生成多语言临时字段名")
    void testGetMultiLangTempFieldName() {
        String result = FieldDescribeExt.getMultiLangTempFieldName("field", Lang.zh_CN);
        
        assertEquals("field__lang_zh-CN", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getMaskEncryptFieldName静态方法
     */
    @Test
    @DisplayName("getMaskEncryptFieldName - 生成掩码加密字段名")
    void testGetMaskEncryptFieldName() {
        String result = FieldDescribeExt.getMaskEncryptFieldName("field");
        
        assertEquals("field__encrypt", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getOldFieldName静态方法
     */
    @Test
    @DisplayName("getOldFieldName - 生成旧字段名")
    void testGetOldFieldName() {
        String result = FieldDescribeExt.getOldFieldName("field");
        
        assertEquals("field__old", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getChangeFieldName静态方法
     */
    @Test
    @DisplayName("getChangeFieldName - 生成变更字段名")
    void testGetChangeFieldName() {
        String result = FieldDescribeExt.getChangeFieldName("field");
        
        assertEquals("change_field", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getOriginalFieldName静态方法
     */
    @Test
    @DisplayName("getOriginalFieldName - 生成原始字段名")
    void testGetOriginalFieldName() {
        String result = FieldDescribeExt.getOriginalFieldName("field");
        
        assertEquals("original_field", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getTeamMemberFieldApiName静态方法
     */
    @Test
    @DisplayName("getTeamMemberFieldApiName - 生成团队成员字段API名")
    void testGetTeamMemberFieldApiName() {
        String result = FieldDescribeExt.getTeamMemberFieldApiName("member", "admin", "read");
        
        assertEquals("member_admin_read", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isOriginalFieldName静态方法 - 原始字段
     */
    @Test
    @DisplayName("isOriginalFieldName - 原始字段")
    void testIsOriginalFieldName_True() {
        boolean result = FieldDescribeExt.isOriginalFieldName("original_field");
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isOriginalFieldName静态方法 - 非原始字段
     */
    @Test
    @DisplayName("isOriginalFieldName - 非原始字段")
    void testIsOriginalFieldName_False() {
        boolean result = FieldDescribeExt.isOriginalFieldName("normal_field");
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isOriginalFieldName静态方法 - 特殊原始数据字段
     */
    @Test
    @DisplayName("isOriginalFieldName - 特殊原始数据字段")
    void testIsOriginalFieldName_SpecialOriginalData() {
        boolean result1 = FieldDescribeExt.isOriginalFieldName("original_data");
        boolean result2 = FieldDescribeExt.isOriginalFieldName("original_detail_data");
        
        assertFalse(result1);
        assertFalse(result2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getCurrencyFieldName静态方法
     */
    @Test
    @DisplayName("getCurrencyFieldName - 生成货币字段名")
    void testGetCurrencyFieldName() {
        String result = FieldDescribeExt.getCurrencyFieldName("amount");
        
        assertEquals("currency_amount", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isChangeFieldName静态方法 - 变更字段
     */
    @Test
    @DisplayName("isChangeFieldName - 变更字段")
    void testIsChangeFieldName_True() {
        boolean result = FieldDescribeExt.isChangeFieldName("change_field");
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isChangeFieldName静态方法 - 非变更字段
     */
    @Test
    @DisplayName("isChangeFieldName - 非变更字段")
    void testIsChangeFieldName_False() {
        boolean result = FieldDescribeExt.isChangeFieldName("normal_field");
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFieldNameByChangeField静态方法 - 有效变更字段
     */
    @Test
    @DisplayName("getFieldNameByChangeField - 有效变更字段")
    void testGetFieldNameByChangeField_Valid() {
        String result = FieldDescribeExt.getFieldNameByChangeField("change_field_name");
        
        assertEquals("field_name", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFieldNameByChangeField静态方法 - 无效变更字段
     */
    @Test
    @DisplayName("getFieldNameByChangeField - 无效变更字段")
    void testGetFieldNameByChangeField_Invalid() {
        String result = FieldDescribeExt.getFieldNameByChangeField("normal_field");
        
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getOriginalFieldNameByChangeField静态方法
     */
    @Test
    @DisplayName("getOriginalFieldNameByChangeField - 从变更字段获取原始字段名")
    void testGetOriginalFieldNameByChangeField() {
        String result = FieldDescribeExt.getOriginalFieldNameByChangeField("change_field_name");
        
        assertEquals("original_field_name", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSelectField静态方法 - 单选字段
     */
    @Test
    @DisplayName("isSelectField - 单选字段")
    void testIsSelectField_SelectOne() {
        boolean result = FieldDescribeExt.isSelectField(IFieldType.SELECT_ONE);
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSelectField静态方法 - 多选字段
     */
    @Test
    @DisplayName("isSelectField - 多选字段")
    void testIsSelectField_SelectMany() {
        boolean result = FieldDescribeExt.isSelectField(IFieldType.SELECT_MANY);
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSelectField静态方法 - 非选择字段
     */
    @Test
    @DisplayName("isSelectField - 非选择字段")
    void testIsSelectField_NotSelect() {
        boolean result = FieldDescribeExt.isSelectField(IFieldType.TEXT);
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSelectOtherField静态方法 - 选择其他字段
     */
    @Test
    @DisplayName("isSelectOtherField - 选择其他字段")
    void testIsSelectOtherField_True() {
        boolean result = FieldDescribeExt.isSelectOtherField("field__o");
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSelectOtherField静态方法 - 非选择其他字段
     */
    @Test
    @DisplayName("isSelectOtherField - 非选择其他字段")
    void testIsSelectOtherField_False() {
        boolean result = FieldDescribeExt.isSelectOtherField("normal_field");
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMultiLangExtraField静态方法 - 多语言额外字段
     */
    @Test
    @DisplayName("isMultiLangExtraField - 多语言额外字段")
    void testIsMultiLangExtraField_True() {
        boolean result = FieldDescribeExt.isMultiLangExtraField("field__lang");
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMultiLangExtraField静态方法 - 非多语言额外字段
     */
    @Test
    @DisplayName("isMultiLangExtraField - 非多语言额外字段")
    void testIsMultiLangExtraField_False() {
        boolean result = FieldDescribeExt.isMultiLangExtraField("normal_field");
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMaskEncryptField静态方法 - 掩码加密字段
     */
    @Test
    @DisplayName("isMaskEncryptField - 掩码加密字段")
    void testIsMaskEncryptField_True() {
        boolean result = FieldDescribeExt.isMaskEncryptField("field__encrypt");
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMaskEncryptField静态方法 - 非掩码加密字段
     */
    @Test
    @DisplayName("isMaskEncryptField - 非掩码加密字段")
    void testIsMaskEncryptField_False() {
        boolean result = FieldDescribeExt.isMaskEncryptField("normal_field");
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMaskShowFieldName静态方法 - 掩码显示字段
     */
    @Test
    @DisplayName("isMaskShowFieldName - 掩码显示字段")
    void testIsMaskShowFieldName_True() {
        boolean result = FieldDescribeExt.isMaskShowFieldName("field__s");
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMaskShowFieldName静态方法 - 非掩码显示字段
     */
    @Test
    @DisplayName("isMaskShowFieldName - 非掩码显示字段")
    void testIsMaskShowFieldName_False() {
        boolean result = FieldDescribeExt.isMaskShowFieldName("normal_field");
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getMultiLangFieldFromExtraField静态方法
     */
    @Test
    @DisplayName("getMultiLangFieldFromExtraField - 从多语言额外字段获取原字段")
    void testGetMultiLangFieldFromExtraField() {
        String result = FieldDescribeExt.getMultiLangFieldFromExtraField("field_name__lang");
        
        assertEquals("field_name", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getSelectFieldFromOtherField静态方法
     */
    @Test
    @DisplayName("getSelectFieldFromOtherField - 从其他字段获取选择字段")
    void testGetSelectFieldFromOtherField() {
        String result = FieldDescribeExt.getSelectFieldFromOtherField("field_name__o");
        
        assertEquals("field_name", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFieldNameFromMaskEncryptFieldName静态方法
     */
    @Test
    @DisplayName("getFieldNameFromMaskEncryptFieldName - 从掩码加密字段获取原字段名")
    void testGetFieldNameFromMaskEncryptFieldName() {
        String result = FieldDescribeExt.getFieldNameFromMaskEncryptFieldName("field_name__encrypt");
        
        assertEquals("field_name", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getReferenceLabelI18NKey静态方法
     */
    @Test
    @DisplayName("getReferenceLabelI18NKey - 生成引用标签国际化键")
    void testGetReferenceLabelI18NKey() {
        String result = FieldDescribeExt.getReferenceLabelI18NKey("TestObject", "test_field");
        
        assertEquals("TestObject.field.test_field.reference_label", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试generateTargetRelatedListName静态方法
     */
    @Test
    @DisplayName("generateTargetRelatedListName - 生成目标关联列表名")
    void testGenerateTargetRelatedListName() {
        String result1 = FieldDescribeExt.generateTargetRelatedListName();
        String result2 = FieldDescribeExt.generateTargetRelatedListName();
        
        assertNotNull(result1);
        assertNotNull(result2);
        assertNotEquals(result1, result2); // 应该生成不同的名称
        assertTrue(result1.startsWith("target_related_list_"));
        assertTrue(result1.endsWith("__c"));
        assertTrue(result2.startsWith("target_related_list_"));
        assertTrue(result2.endsWith("__c"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isCurrencyOrNumberType静态方法 - 货币类型
     */
    @Test
    @DisplayName("isCurrencyOrNumberType - 货币类型")
    void testIsCurrencyOrNumberType_Currency() {
        boolean result = FieldDescribeExt.isCurrencyOrNumberType(IFieldType.CURRENCY);
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isCurrencyOrNumberType静态方法 - 数字类型
     */
    @Test
    @DisplayName("isCurrencyOrNumberType - 数字类型")
    void testIsCurrencyOrNumberType_Number() {
        boolean result = FieldDescribeExt.isCurrencyOrNumberType(IFieldType.NUMBER);
        
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isCurrencyOrNumberType静态方法 - 其他类型
     */
    @Test
    @DisplayName("isCurrencyOrNumberType - 其他类型")
    void testIsCurrencyOrNumberType_Other() {
        boolean result = FieldDescribeExt.isCurrencyOrNumberType(IFieldType.TEXT);
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMultiCurrencyFields静态方法 - 多货币字段
     */
    @Test
    @DisplayName("isMultiCurrencyFields - 多货币字段")
    void testIsMultiCurrencyFields_True() {
        boolean result1 = FieldDescribeExt.isMultiCurrencyFields("mc_currency");
        boolean result2 = FieldDescribeExt.isMultiCurrencyFields("mc_exchange_rate");
        
        assertTrue(result1);
        assertTrue(result2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMultiCurrencyFields静态方法 - 非多货币字段
     */
    @Test
    @DisplayName("isMultiCurrencyFields - 非多货币字段")
    void testIsMultiCurrencyFields_False() {
        boolean result = FieldDescribeExt.isMultiCurrencyFields("normal_field");
        
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isMultiCurrencyCalculateFields静态方法
     */
    @Test
    @DisplayName("isMultiCurrencyCalculateFields - 多货币计算字段")
    void testIsMultiCurrencyCalculateFields() {
        boolean result1 = FieldDescribeExt.isMultiCurrencyCalculateFields("mc_exchange_rate");
        boolean result2 = FieldDescribeExt.isMultiCurrencyCalculateFields("mc_exchange_rate_version");
        boolean result3 = FieldDescribeExt.isMultiCurrencyCalculateFields("mc_functional_currency");
        boolean result4 = FieldDescribeExt.isMultiCurrencyCalculateFields("normal_field");
        
        assertTrue(result1);
        assertTrue(result2);
        assertTrue(result3);
        assertFalse(result4);
    }
}
