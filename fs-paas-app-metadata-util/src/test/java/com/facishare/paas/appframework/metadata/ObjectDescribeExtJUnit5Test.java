package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ObjectDescribeExt的JUnit 5测试类
 * 测试对象描述扩展功能
 * 
 * GenerateByAI
 * 测试内容描述：测试对象描述的创建、字段管理和业务逻辑
 */
class ObjectDescribeExtJUnit5Test {

    private ObjectDescribeExt objectDescribeExt;
    private IObjectDescribe testObjectDescribe;

    @BeforeEach
    void setUp() {
        // 创建测试用的对象描述
        testObjectDescribe = new ObjectDescribe();
        testObjectDescribe.setApiName("TestObject");
        testObjectDescribe.setDisplayName("测试对象");
        testObjectDescribe.setTenantId("test_tenant");
        
        // 创建测试字段
        List<IFieldDescribe> fields = Lists.newArrayList();
        
        // 创建ID字段
        IFieldDescribe idField = new TextFieldDescribe();
        idField.setApiName("_id");
        idField.setLabel("ID");
        fields.add(idField);

        // 创建名称字段
        IFieldDescribe nameField = new TextFieldDescribe();
        nameField.setApiName("name");
        nameField.setLabel("名称");
        nameField.setRequired(true);
        fields.add(nameField);

        // 创建描述字段
        IFieldDescribe descField = new TextFieldDescribe();
        descField.setApiName("description");
        descField.setLabel("描述");
        fields.add(descField);
        
        testObjectDescribe.setFieldDescribes(fields);
        objectDescribeExt = ObjectDescribeExt.of(testObjectDescribe);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法 - IObjectDescribe参数
     */
    @Test
    @DisplayName("静态方法 - of工厂方法IObjectDescribe参数")
    void testOf_WithIObjectDescribe() {
        // Act: 使用of方法创建实例
        ObjectDescribeExt result = ObjectDescribeExt.of(testObjectDescribe);
        
        // Assert: 验证工厂方法
        assertNotNull(result);
        assertSame(testObjectDescribe, result.getObjectDescribe());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法 - 避免双重包装
     */
    @Test
    @DisplayName("静态方法 - of工厂方法避免双重包装")
    void testOf_AvoidDoubleWrapping() {
        // Arrange: 创建ObjectDescribeExt实例
        ObjectDescribeExt originalExt = ObjectDescribeExt.of(testObjectDescribe);
        
        // Act: 再次使用of方法包装
        ObjectDescribeExt result = ObjectDescribeExt.of(originalExt);
        
        // Assert: 验证避免双重包装
        assertNotNull(result);
        assertSame(testObjectDescribe, result.getObjectDescribe());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试基本属性访问
     */
    @Test
    @DisplayName("基本属性 - 属性访问")
    void testBasicProperties() {
        // Act & Assert: 验证基本属性
        assertEquals("TestObject", objectDescribeExt.getApiName());
        assertEquals("测试对象", objectDescribeExt.getDisplayName());
        assertEquals("test_tenant", objectDescribeExt.getTenantId());
        assertNotNull(objectDescribeExt.getFieldDescribes());
        assertEquals(3, objectDescribeExt.getFieldDescribes().size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFieldDescribe方法 - 字段存在
     */
    @Test
    @DisplayName("字段查找 - getFieldDescribe方法字段存在")
    void testGetFieldDescribe_FieldExists() {
        // Act: 执行getFieldDescribe方法
        IFieldDescribe result = objectDescribeExt.getFieldDescribe("name");
        
        // Assert: 验证结果
        assertNotNull(result);
        assertEquals("name", result.getApiName());
        assertEquals("名称", result.getLabel());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFieldDescribe方法 - 字段不存在
     */
    @Test
    @DisplayName("字段查找 - getFieldDescribe方法字段不存在")
    void testGetFieldDescribe_FieldNotExists() {
        // Act: 执行getFieldDescribe方法
        IFieldDescribe result = objectDescribeExt.getFieldDescribe("non_existent_field");
        
        // Assert: 验证结果
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFieldDescribeSilently方法 - 字段存在
     */
    @Test
    @DisplayName("字段查找 - getFieldDescribeSilently方法字段存在")
    void testGetFieldDescribeSilently_FieldExists() {
        // Act: 执行getFieldDescribeSilently方法
        Optional<IFieldDescribe> result = objectDescribeExt.getFieldDescribeSilently("name");
        
        // Assert: 验证结果
        assertTrue(result.isPresent());
        assertEquals("name", result.get().getApiName());
        assertEquals("名称", result.get().getLabel());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFieldDescribeSilently方法 - 字段不存在
     */
    @Test
    @DisplayName("字段查找 - getFieldDescribeSilently方法字段不存在")
    void testGetFieldDescribeSilently_FieldNotExists() {
        // Act: 执行getFieldDescribeSilently方法
        Optional<IFieldDescribe> result = objectDescribeExt.getFieldDescribeSilently("non_existent_field");
        
        // Assert: 验证结果
        assertFalse(result.isPresent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试containsField方法
     */
    @ParameterizedTest
    @ValueSource(strings = {"_id", "name", "description"})
    @DisplayName("字段检查 - containsField方法存在的字段")
    void testContainsField_ExistingFields(String fieldName) {
        // Act: 执行containsField方法
        boolean result = objectDescribeExt.containsField(fieldName);
        
        // Assert: 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试containsField方法 - 不存在的字段
     */
    @ParameterizedTest
    @ValueSource(strings = {"non_existent", "invalid_field", "missing_field"})
    @DisplayName("字段检查 - containsField方法不存在的字段")
    void testContainsField_NonExistingFields(String fieldName) {
        // Act: 执行containsField方法
        boolean result = objectDescribeExt.containsField(fieldName);
        
        // Assert: 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isPayment静态方法 - 支付记录
     */
    @Test
    @DisplayName("静态判断 - isPayment方法支付记录")
    void testIsPayment_True() {
        // Act: 执行isPayment方法
        boolean result = ObjectDescribeExt.isPayment("payment_record");
        
        // Assert: 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isPayment静态方法 - 非支付记录
     */
    @Test
    @DisplayName("静态判断 - isPayment方法非支付记录")
    void testIsPayment_False() {
        // Act: 执行isPayment方法
        boolean result = ObjectDescribeExt.isPayment("TestObject");
        
        // Assert: 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildPaymentDescribe静态方法
     */
    @Test
    @DisplayName("静态构建 - buildPaymentDescribe方法")
    void testBuildPaymentDescribe() {
        // Act: 执行buildPaymentDescribe方法
        ObjectDescribeExt result = ObjectDescribeExt.buildPaymentDescribe();
        
        // Assert: 验证结果
        assertNotNull(result);
        assertEquals("payment_record", result.getApiName());
        assertNotNull(result.getFieldDescribes());
        assertFalse(result.getFieldDescribes().isEmpty());
        
        // 验证包含必要的支付字段
        assertTrue(result.containsField("amount"));
        assertTrue(result.containsField("fee"));
        assertTrue(result.containsField("payStatus"));
        assertTrue(result.containsField("name"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试toMap方法
     */
    @Test
    @DisplayName("数据转换 - toMap方法")
    void testToMap() {
        // Act: 执行toMap方法
        Map<String, Object> result = objectDescribeExt.toMap();
        
        // Assert: 验证结果
        assertNotNull(result);
        assertEquals("TestObject", result.get("api_name"));
        assertEquals("测试对象", result.get("display_name"));
        assertEquals("test_tenant", result.get("tenant_id"));
        assertTrue(result.containsKey("field_describes"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试descriptionNullToEmpty方法
     */
    @Test
    @DisplayName("数据处理 - descriptionNullToEmpty方法")
    void testDescriptionNullToEmpty() {
        // Arrange: 设置描述为null
        testObjectDescribe.setDescription(null);
        
        // Act: 执行descriptionNullToEmpty方法
        objectDescribeExt.descriptionNullToEmpty();
        
        // Assert: 验证结果
        assertEquals("", objectDescribeExt.getDescription());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试addExtProperty方法
     */
    @Test
    @DisplayName("扩展属性 - addExtProperty方法")
    void testAddExtProperty() {
        // Act: 执行addExtProperty方法
        assertDoesNotThrow(() -> objectDescribeExt.addExtProperty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试常量值
     */
    @Test
    @DisplayName("常量验证 - 常量值验证")
    void testConstants() {
        // Assert: 验证重要常量
        assertEquals("workflow_instance_id", ObjectDescribeExt.WORKFLOW_INSTANCE_ID);
        assertEquals("payment_record", ObjectDescribeExt.PAYMENT_DESCRIBE_API_NAME);
        assertEquals("PersonnelObj", ObjectDescribeExt.PERSONNEL_OBJ_API_NAME);
        assertEquals("__EmployeeObj", ObjectDescribeExt.EMPLOYEE_OBJ_API_NAME);
        assertEquals("__DepartmentObj", ObjectDescribeExt.CALCULATE_DEPARTMENT_OBJ_API_NAME);
        assertEquals("partner_id", ObjectDescribeExt.PARTNER_ID_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试委托模式的方法调用
     */
    @Test
    @DisplayName("委托模式 - 委托方法调用")
    void testDelegateMethodCalls() {
        // Act & Assert: 验证委托方法调用
        assertEquals("TestObject", objectDescribeExt.getApiName());
        assertEquals("测试对象", objectDescribeExt.getDisplayName());
        assertEquals("test_tenant", objectDescribeExt.getTenantId());
        
        // 测试设置方法
        objectDescribeExt.setDisplayName("新的测试对象");
        assertEquals("新的测试对象", objectDescribeExt.getDisplayName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试边界条件 - 空字段列表
     */
    @Test
    @DisplayName("边界条件 - 空字段列表")
    void testEmptyFieldList() {
        // Arrange: 创建没有字段的对象描述
        IObjectDescribe emptyDescribe = new ObjectDescribe();
        emptyDescribe.setApiName("EmptyObject");
        emptyDescribe.setFieldDescribes(Lists.newArrayList());
        
        ObjectDescribeExt emptyExt = ObjectDescribeExt.of(emptyDescribe);
        
        // Act & Assert: 验证空字段列表
        assertNotNull(emptyExt.getFieldDescribes());
        assertTrue(emptyExt.getFieldDescribes().isEmpty());
        assertFalse(emptyExt.containsField("any_field"));
        assertNull(emptyExt.getFieldDescribe("any_field"));
        assertFalse(emptyExt.getFieldDescribeSilently("any_field").isPresent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试边界条件 - null字段列表
     */
    @Test
    @DisplayName("边界条件 - null字段列表")
    void testNullFieldList() {
        // Arrange: 创建字段列表为null的对象描述
        IObjectDescribe nullFieldsDescribe = new ObjectDescribe();
        nullFieldsDescribe.setApiName("NullFieldsObject");
        nullFieldsDescribe.setFieldDescribes(null);
        
        ObjectDescribeExt nullFieldsExt = ObjectDescribeExt.of(nullFieldsDescribe);
        
        // Act & Assert: 验证null字段列表
        assertNull(nullFieldsExt.getFieldDescribes());
        assertFalse(nullFieldsExt.containsField("any_field"));
        assertNull(nullFieldsExt.getFieldDescribe("any_field"));
        assertFalse(nullFieldsExt.getFieldDescribeSilently("any_field").isPresent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试数据一致性
     */
    @Test
    @DisplayName("一致性验证 - 数据一致性验证")
    void testDataConsistency() {
        // Act: 多次获取相同的数据
        String apiName1 = objectDescribeExt.getApiName();
        String apiName2 = objectDescribeExt.getApiName();
        List<IFieldDescribe> fields1 = objectDescribeExt.getFieldDescribes();
        List<IFieldDescribe> fields2 = objectDescribeExt.getFieldDescribes();
        
        // Assert: 验证数据一致性
        assertEquals(apiName1, apiName2);
        assertSame(fields1, fields2);
        assertSame(objectDescribeExt.getObjectDescribe(), objectDescribeExt.getObjectDescribe());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试线程安全性
     */
    @Test
    @DisplayName("线程安全 - 多线程访问")
    void testThreadSafety() throws InterruptedException {
        // Act: 在多线程中访问
        Thread thread1 = new Thread(() -> {
            objectDescribeExt.setDisplayName("线程1修改");
            assertNotNull(objectDescribeExt.getApiName());
        });
        
        Thread thread2 = new Thread(() -> {
            boolean hasField = objectDescribeExt.containsField("name");
            assertTrue(hasField);
        });
        
        // Assert: 验证线程安全
        assertDoesNotThrow(() -> {
            thread1.start();
            thread2.start();
            thread1.join();
            thread2.join();
        });
    }
}
