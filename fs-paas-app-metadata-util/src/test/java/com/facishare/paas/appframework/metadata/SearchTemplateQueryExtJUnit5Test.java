package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GenerateByAI
 * SearchTemplateQueryExt的JUnit 5测试类
 * 测试搜索模板查询扩展的核心功能
 */
@ExtendWith(MockitoExtension.class)
class SearchTemplateQueryExtJUnit5Test {

    /**
     * GenerateByAI
     * 创建测试用的Filter对象
     */
    private IFilter createFilter(String fieldName, String value, Operator operator) {
        Filter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setFieldValues(Lists.newArrayList(value));
        filter.setOperator(operator);
        return filter;
    }

    // ==================== 工厂方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法 - 正常情况
     */
    @Test
    @DisplayName("工厂方法 - of创建扩展对象")
    void testOf_Success() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(10);
        query.setOffset(0);

        // 执行测试
        SearchTemplateQueryExt result = SearchTemplateQueryExt.of(query);

        // 验证结果
        assertNotNull(result);
        assertEquals(10, result.getLimit());
        assertEquals(0, result.getOffset());
        assertSame(query, result.getQuery());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法 - null参数
     */
    @Test
    @DisplayName("工厂方法 - of处理null参数")
    void testOf_NullQuery() {
        // 执行测试
        SearchTemplateQueryExt result = SearchTemplateQueryExt.of((ISearchTemplateQuery) null);

        // 验证结果
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法 - 验证内部query获取
     */
    @Test
    @DisplayName("工厂方法 - of验证内部query获取")
    void testOf_InternalQueryAccess() {
        // 准备测试数据
        SearchTemplateQuery originalQuery = new SearchTemplateQuery();
        originalQuery.setLimit(20);
        SearchTemplateQueryExt ext = SearchTemplateQueryExt.of(originalQuery);

        // 验证结果 - 验证内部query的访问
        assertNotNull(ext);
        assertEquals(20, ext.getLimit());
        assertSame(originalQuery, ext.getQuery());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ofFilters静态工厂方法
     */
    @Test
    @DisplayName("工厂方法 - ofFilters创建对象")
    void testOfFilters_Success() {
        // 准备测试数据
        List<IFilter> filters = Lists.newArrayList(
                createFilter("field1", "value1", Operator.EQ),
                createFilter("field2", "value2", Operator.NEQ)
        );

        // 执行测试
        SearchTemplateQueryExt result = SearchTemplateQueryExt.ofFilters(filters);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getFilters().size());
        assertEquals("field1", result.getFilters().get(0).getFieldName());
        assertEquals("field2", result.getFilters().get(1).getFieldName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ofWheres静态工厂方法
     */
    @Test
    @DisplayName("工厂方法 - ofWheres创建对象")
    void testOfWheres_Success() {
        // 准备测试数据
        Wheres wheres = new Wheres();
        wheres.setFilters(Lists.newArrayList(createFilter("test_field", "test_value", Operator.EQ)));
        List<Wheres> wheresList = Lists.newArrayList(wheres);

        // 执行测试
        SearchTemplateQueryExt result = SearchTemplateQueryExt.ofWheres(wheresList);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getWheres().size());
        assertEquals(1, result.getWheres().get(0).getFilters().size());
        assertEquals("test_field", result.getWheres().get(0).getFilters().get(0).getFieldName());
    }

    // ==================== 核心业务方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试processFilterGroupByPatternExpression方法 - 正常情况
     */
    @Test
    @DisplayName("过滤器分组 - processFilterGroupByPatternExpression正常场景")
    void testProcessFilterGroupByPatternExpression_Normal() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList(
                createFilter("field1", "value1", Operator.EQ),
                createFilter("field2", "value2", Operator.EQ)
        );
        query.setFilters(filters);
        query.setPattern("1 and 2");
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);

        // 执行测试
        ISearchTemplateQuery result = searchTemplateQueryExt.processFilterGroupByPatternExpression();

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getFilters().size());
        assertEquals("1", result.getFilters().get(0).getFilterGroup());
        assertEquals("1", result.getFilters().get(1).getFilterGroup());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试processFilterGroupByPatternExpression方法 - 空filters
     */
    @Test
    @DisplayName("过滤器分组 - processFilterGroupByPatternExpression空filters")
    void testProcessFilterGroupByPatternExpression_EmptyFilters() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(Lists.newArrayList());
        query.setPattern("1 and 2");
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);

        // 执行测试
        ISearchTemplateQuery result = searchTemplateQueryExt.processFilterGroupByPatternExpression();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getFilters().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试processFilterGroupByPatternExpression方法 - 空pattern
     */
    @Test
    @DisplayName("过滤器分组 - processFilterGroupByPatternExpression空pattern")
    void testProcessFilterGroupByPatternExpression_EmptyPattern() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList(createFilter("field1", "value1", Operator.EQ));
        query.setFilters(filters);
        query.setPattern("");
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);

        // 执行测试
        ISearchTemplateQuery result = searchTemplateQueryExt.processFilterGroupByPatternExpression();

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getFilters().size());
        assertEquals(filters, result.getFilters());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试processFilterGroupByPatternExpression方法 - 复杂pattern
     */
    @Test
    @DisplayName("过滤器分组 - processFilterGroupByPatternExpression复杂pattern")
    void testProcessFilterGroupByPatternExpression_ComplexPattern() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList(
                createFilter("field1", "value1", Operator.EQ),
                createFilter("field2", "value2", Operator.EQ),
                createFilter("field3", "value3", Operator.EQ)
        );
        query.setFilters(filters);
        query.setPattern("(1 and 2) or 3");
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);

        // 执行测试
        ISearchTemplateQuery result = searchTemplateQueryExt.processFilterGroupByPatternExpression();

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.getFilters().size());
        assertEquals("1", result.getFilters().get(0).getFilterGroup());
        assertEquals("1", result.getFilters().get(1).getFilterGroup());
        assertEquals("2", result.getFilters().get(2).getFilterGroup());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parsePatternToSearchQuery方法 - 正常AND场景
     */
    @Test
    @DisplayName("模板解析 - parsePatternToSearchQuery AND场景")
    void testParsePatternToSearchQuery_AndPattern() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList(
                createFilter("field1", "value1", Operator.EQ),
                createFilter("field2", "value2", Operator.EQ)
        );
        query.setFilters(filters);
        query.setPattern("1 and 2");
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);

        // 执行测试
        SearchQuery result = searchTemplateQueryExt.parsePatternToSearchQuery();

        // 验证结果
        assertNotNull(result);
        assertEquals(SearchQuery.Connector.AND, result.getConnector());
        assertEquals(2, result.getSearchQueryContainer().size());
        assertEquals("field1", result.getSearchQueryContainer().get(0).getFilter().getFieldName());
        assertEquals("field2", result.getSearchQueryContainer().get(1).getFilter().getFieldName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parsePatternToSearchQuery方法 - OR场景
     */
    @Test
    @DisplayName("模板解析 - parsePatternToSearchQuery OR场景")
    void testParsePatternToSearchQuery_OrPattern() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList(
                createFilter("field1", "value1", Operator.EQ),
                createFilter("field2", "value2", Operator.NEQ)
        );
        query.setFilters(filters);
        query.setPattern("1 or 2");
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);

        // 执行测试
        SearchQuery result = searchTemplateQueryExt.parsePatternToSearchQuery();

        // 验证结果
        assertNotNull(result);
        assertEquals(SearchQuery.Connector.OR, result.getConnector());
        assertEquals(2, result.getSearchQueryContainer().size());
        assertEquals("field1", result.getSearchQueryContainer().get(0).getFilter().getFieldName());
        assertEquals("field2", result.getSearchQueryContainer().get(1).getFilter().getFieldName());
        assertEquals(Operator.NEQ, result.getSearchQueryContainer().get(1).getFilter().getOperator());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parsePatternToSearchQuery方法 - 空pattern
     */
    @Test
    @DisplayName("模板解析 - parsePatternToSearchQuery空pattern")
    void testParsePatternToSearchQuery_EmptyPattern() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(Lists.newArrayList(createFilter("field1", "value1", Operator.EQ)));
        query.setPattern("");
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);

        // 执行测试
        SearchQuery result = searchTemplateQueryExt.parsePatternToSearchQuery();

        // 验证结果 - 空pattern应该返回null
        assertNull(result);
    }

    // ==================== 过滤器操作方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试addFilter方法 - 单个值
     */
    @Test
    @DisplayName("过滤器操作 - addFilter单个值")
    void testAddFilter_SingleValue() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);

        // 执行测试
        SearchTemplateQueryExt result = searchTemplateQueryExt.addFilter(Operator.EQ, "test_field", "test_value");

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getFilters().size());
        assertEquals("test_field", result.getFilters().get(0).getFieldName());
        assertEquals("test_value", result.getFilters().get(0).getFieldValues().get(0));
        assertEquals(Operator.EQ, result.getFilters().get(0).getOperator());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试addFilter方法 - 多个值
     */
    @Test
    @DisplayName("过滤器操作 - addFilter多个值")
    void testAddFilter_MultipleValues() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);
        List<String> values = Lists.newArrayList("value1", "value2", "value3");

        // 执行测试
        SearchTemplateQueryExt result = searchTemplateQueryExt.addFilter(Operator.IN, "test_field", values);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getFilters().size());
        assertEquals("test_field", result.getFilters().get(0).getFieldName());
        assertEquals(3, result.getFilters().get(0).getFieldValues().size());
        assertEquals("value1", result.getFilters().get(0).getFieldValues().get(0));
        assertEquals("value2", result.getFilters().get(0).getFieldValues().get(1));
        assertEquals("value3", result.getFilters().get(0).getFieldValues().get(2));
        assertEquals(Operator.IN, result.getFilters().get(0).getOperator());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试addIsDeletedFalseFilter方法
     */
    @Test
    @DisplayName("过滤器操作 - addIsDeletedFalseFilter")
    void testAddIsDeletedFalseFilter() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);

        // 执行测试
        SearchTemplateQueryExt result = searchTemplateQueryExt.addIsDeletedFalseFilter();

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getFilters().size());
        assertEquals("is_deleted", result.getFilters().get(0).getFieldName());
        assertEquals("0", result.getFilters().get(0).getFieldValues().get(0));
        assertEquals(Operator.EQ, result.getFilters().get(0).getOperator());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试addDeletedFilterIfNoDeletedFilter方法 - 基本功能验证
     */
    @Test
    @DisplayName("过滤器操作 - addDeletedFilterIfNoDeletedFilter基本功能")
    void testAddDeletedFilterIfNoDeletedFilter_BasicFunction() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(Lists.newArrayList(createFilter("other_field", "value", Operator.EQ)));
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);

        // 执行测试
        searchTemplateQueryExt.addDeletedFilterIfNoDeletedFilter();

        // 验证结果 - 主要验证方法执行不抛异常
        assertNotNull(searchTemplateQueryExt.getFilters());
        // 验证添加了is_deleted过滤器
        boolean hasDeletedFilter = searchTemplateQueryExt.getFilters().stream()
                .anyMatch(filter -> "is_deleted".equals(filter.getFieldName()));
        assertTrue(hasDeletedFilter);
    }

    // ==================== 查询操作方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试limit方法
     */
    @Test
    @DisplayName("查询操作 - limit设置限制")
    void testLimit_Success() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);

        // 执行测试
        SearchTemplateQueryExt result = searchTemplateQueryExt.limit(50);

        // 验证结果
        assertNotNull(result);
        assertEquals(50, result.getLimit());
        assertSame(searchTemplateQueryExt, result); // 验证返回自身
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试copy方法
     */
    @Test
    @DisplayName("查询操作 - copy复制查询")
    void testCopy_Success() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(10);
        query.setOffset(5);
        query.setFilters(Lists.newArrayList(createFilter("test_field", "test_value", Operator.EQ)));
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);

        // 执行测试
        ISearchTemplateQuery result = searchTemplateQueryExt.copy();

        // 验证结果
        assertNotNull(result);
        assertNotSame(query, result); // 验证是新对象
        assertEquals(10, result.getLimit());
        assertEquals(5, result.getOffset());
        assertEquals(1, result.getFilters().size());
        assertEquals("test_field", result.getFilters().get(0).getFieldName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setDefaultOrderBy方法
     */
    @Test
    @DisplayName("查询操作 - setDefaultOrderBy设置默认排序")
    void testSetDefaultOrderBy_Success() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);

        // 执行测试
        searchTemplateQueryExt.setDefaultOrderBy();

        // 验证结果
        assertNotNull(searchTemplateQueryExt.getOrders());
        assertEquals(2, searchTemplateQueryExt.getOrders().size());
        assertEquals("create_time", searchTemplateQueryExt.getOrders().get(0).getFieldName());
        assertEquals("_id", searchTemplateQueryExt.getOrders().get(1).getFieldName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试searchInDB方法
     */
    @Test
    @DisplayName("查询操作 - searchInDB设置数据库搜索")
    void testSearchInDB_Success() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);

        // 执行测试
        searchTemplateQueryExt.searchInDB();

        // 验证结果
        assertEquals("db", searchTemplateQueryExt.getSearchSource());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试searchInES方法
     */
    @Test
    @DisplayName("查询操作 - searchInES设置ES搜索")
    void testSearchInES_Success() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);

        // 执行测试
        searchTemplateQueryExt.searchInES();

        // 验证结果
        assertEquals("es", searchTemplateQueryExt.getSearchSource());
    }

    // ==================== 静态工具方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试calculateTotalPage静态方法
     */
    @ParameterizedTest
    @CsvSource({
            "100, 10, 10",
            "105, 10, 11",
            "99, 10, 10",
            "1, 10, 1",
            "0, 10, 0"
    })
    @DisplayName("静态工具 - calculateTotalPage计算总页数")
    void testCalculateTotalPage(int totalNum, int pageSize, int expectedTotalPage) {
        // 执行测试
        int result = SearchTemplateQueryExt.calculateTotalPage(totalNum, pageSize);

        // 验证结果
        assertEquals(expectedTotalPage, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试calculateOffset静态方法
     */
    @ParameterizedTest
    @CsvSource({
            "1, 10, 0",
            "2, 10, 10",
            "3, 20, 40",
            "5, 5, 20"
    })
    @DisplayName("静态工具 - calculateOffset计算偏移量")
    void testCalculateOffset(int pageNum, int pageSize, int expectedOffset) {
        // 执行测试
        int result = SearchTemplateQueryExt.calculateOffset(pageNum, pageSize);

        // 验证结果
        assertEquals(expectedOffset, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试calculatePageNum静态方法
     */
    @ParameterizedTest
    @CsvSource({
            "0, 10, 0",
            "10, 10, 1",
            "40, 20, 2",
            "20, 5, 4"
    })
    @DisplayName("静态工具 - calculatePageNum计算页码")
    void testCalculatePageNum(int offset, int pageSize, int expectedPageNum) {
        // 执行测试
        int result = SearchTemplateQueryExt.calculatePageNum(offset, pageSize);

        // 验证结果
        assertEquals(expectedPageNum, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateQueryPattern静态方法 - 基本功能
     */
    @Test
    @DisplayName("静态工具 - validateQueryPattern基本功能")
    void testValidateQueryPattern_BasicFunction() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setPattern("1 and 2");

        // 执行测试 - 主要验证方法不抛异常
        boolean result = SearchTemplateQueryExt.validateQueryPattern(query);

        // 验证结果 - 验证返回布尔值
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateQueryPattern静态方法 - 空pattern
     */
    @Test
    @DisplayName("静态工具 - validateQueryPattern空pattern")
    void testValidateQueryPattern_EmptyPattern() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setPattern("");

        // 执行测试
        boolean result = SearchTemplateQueryExt.validateQueryPattern(query);

        // 验证结果
        assertTrue(result); // 空pattern被认为是有效的
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateQueryPattern静态方法 - null pattern
     */
    @Test
    @DisplayName("静态工具 - validateQueryPattern null pattern")
    void testValidateQueryPattern_NullPattern() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setPattern(null);

        // 执行测试
        boolean result = SearchTemplateQueryExt.validateQueryPattern(query);

        // 验证结果
        assertTrue(result); // null pattern被认为是有效的
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildEmptyResult静态方法
     */
    @Test
    @DisplayName("静态工具 - buildEmptyResult构建空结果")
    void testBuildEmptyResult() {
        // 执行测试
        QueryResult<IObjectData> result = SearchTemplateQueryExt.buildEmptyResult();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
    }

    // ==================== 业务逻辑方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试onlyIdFilter方法 - 只有ID过滤器
     */
    @Test
    @DisplayName("业务逻辑 - onlyIdFilter只有ID过滤器")
    void testOnlyIdFilter_OnlyIdFilters() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(Lists.newArrayList(
                createFilter("_id", "123", Operator.EQ),
                createFilter("_id", "456", Operator.IN)
        ));
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);

        // 执行测试
        boolean result = searchTemplateQueryExt.onlyIdFilter();

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试onlyIdFilter方法 - 包含非ID过滤器
     */
    @Test
    @DisplayName("业务逻辑 - onlyIdFilter包含非ID过滤器")
    void testOnlyIdFilter_HasNonIdFilters() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(Lists.newArrayList(
                createFilter("_id", "123", Operator.EQ),
                createFilter("name", "test", Operator.EQ)
        ));
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);

        // 执行测试
        boolean result = searchTemplateQueryExt.onlyIdFilter();

        // 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试clearDataPermissionConfig方法
     */
    @Test
    @DisplayName("业务逻辑 - clearDataPermissionConfig清除数据权限配置")
    void testClearDataPermissionConfig() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setPermissionType(1);
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);

        // 执行测试
        searchTemplateQueryExt.clearDataPermissionConfig();

        // 验证结果
        assertNull(searchTemplateQueryExt.getDataRightsParameter());
        assertEquals(0, searchTemplateQueryExt.getPermissionType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试onlyQueryTotalNumIgnorePermission方法
     */
    @Test
    @DisplayName("业务逻辑 - onlyQueryTotalNumIgnorePermission只查询总数")
    void testOnlyQueryTotalNumIgnorePermission() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(100);
        query.setLimit(50);
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(query);

        // 执行测试
        searchTemplateQueryExt.onlyQueryTotalNumIgnorePermission();

        // 验证结果
        assertEquals(0, searchTemplateQueryExt.getOffset());
        assertEquals(1, searchTemplateQueryExt.getLimit());
        assertEquals(0, searchTemplateQueryExt.getPermissionType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试toSearchTemplateQuery方法
     */
    @Test
    @DisplayName("业务逻辑 - toSearchTemplateQuery转换为SearchTemplateQuery")
    void testToSearchTemplateQuery() {
        // 准备测试数据
        SearchTemplateQuery originalQuery = new SearchTemplateQuery();
        originalQuery.setLimit(25);
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(originalQuery);

        // 执行测试
        SearchTemplateQuery result = searchTemplateQueryExt.toSearchTemplateQuery();

        // 验证结果
        assertNotNull(result);
        assertSame(originalQuery, result);
        assertEquals(25, result.getLimit());
    }
}
