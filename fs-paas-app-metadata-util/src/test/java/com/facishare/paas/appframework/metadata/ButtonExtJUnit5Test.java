package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.UdefButton;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ButtonExt的JUnit 5测试类
 * 测试按钮扩展功能
 * 
 * GenerateByAI
 * 测试内容描述：测试按钮的创建、属性设置和业务逻辑
 */
class ButtonExtJUnit5Test {

    private ButtonExt buttonExt;
    private Map<String, Object> testButtonMap;

    @BeforeEach
    void setUp() {
        // 创建测试用的按钮数据
        testButtonMap = Maps.newHashMap();
        testButtonMap.put("api_name", "test_button");
        testButtonMap.put("name", "测试按钮");
        testButtonMap.put("label", "Test Button");
        testButtonMap.put("button_type", "action");
        testButtonMap.put("define_type", "custom");
        testButtonMap.put("action_type", "custom_function");
        testButtonMap.put("is_exposed_button", false);
        
        UdefButton realButton = new UdefButton(testButtonMap);
        buttonExt = ButtonExt.of(realButton);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法 - Map参数
     */
    @Test
    @DisplayName("静态方法 - of工厂方法Map参数")
    void testOf_WithMap() {
        // Act: 使用of方法创建实例
        ButtonExt result = ButtonExt.of(testButtonMap);
        
        // Assert: 验证工厂方法
        assertNotNull(result);
        assertNotNull(result.getButton());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试基本属性访问
     */
    @Test
    @DisplayName("基本属性 - 属性访问")
    void testBasicProperties() {
        // Act & Assert: 验证基本属性
        assertEquals("test_button", buttonExt.getApiName());
        assertEquals("Test Button", buttonExt.getLabel());
        assertEquals("action", buttonExt.getButtonType());
        assertEquals("custom", buttonExt.getDefineType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSystemButton方法 - 自定义按钮
     */
    @Test
    @DisplayName("按钮类型 - isSystemButton方法自定义按钮")
    void testIsSystemButton_False() {
        // Act: 执行isSystemButton方法
        boolean result = buttonExt.isSystemButton();
        
        // Assert: 验证结果 - 测试数据中设置为custom
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSystemButton方法 - 系统按钮
     */
    @Test
    @DisplayName("按钮类型 - isSystemButton方法系统按钮")
    void testIsSystemButton_True() {
        // Arrange: 创建系统按钮
        Map<String, Object> systemButtonMap = Maps.newHashMap(testButtonMap);
        systemButtonMap.put("define_type", "system");
        UdefButton systemButton = new UdefButton(systemButtonMap);
        ButtonExt systemButtonExt = ButtonExt.of(systemButton);
        
        // Act: 执行isSystemButton方法
        boolean result = systemButtonExt.isSystemButton();
        
        // Assert: 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isNotEditableButton方法 - 自定义按钮
     */
    @Test
    @DisplayName("按钮属性 - isNotEditableButton方法自定义按钮")
    void testIsNotEditableButton_CustomButton() {
        // Act: 执行isNotEditableButton方法
        boolean result = buttonExt.isNotEditableButton();
        
        // Assert: 验证结果 - 自定义action按钮应该可编辑
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isNotEditableButton方法 - 重定向按钮
     */
    @Test
    @DisplayName("按钮属性 - isNotEditableButton方法重定向按钮")
    void testIsNotEditableButton_RedirectButton() {
        // Arrange: 创建重定向按钮
        Map<String, Object> redirectButtonMap = Maps.newHashMap(testButtonMap);
        redirectButtonMap.put("button_type", "redirect");
        UdefButton redirectButton = new UdefButton(redirectButtonMap);
        ButtonExt redirectButtonExt = ButtonExt.of(redirectButton);
        
        // Act: 执行isNotEditableButton方法
        boolean result = redirectButtonExt.isNotEditableButton();
        
        // Assert: 验证结果 - 重定向按钮不可编辑
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试generateQinxinGroupButton静态方法
     */
    @Test
    @DisplayName("静态构建 - generateQinxinGroupButton方法")
    void testGenerateQinxinGroupButton() {
        // Act: 执行generateQinxinGroupButton方法
        Button result = ButtonExt.generateQinxinGroupButton();
        
        // Assert: 验证结果
        assertNotNull(result);
        assertEquals("QixinChatGroup", result.getAction());
        assertEquals("Qixin_chat_group", result.getName());
        assertNotNull(result.getLabel());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试filterButtonsWheres静态方法 - 有效按钮列表
     */
    @Test
    @DisplayName("静态过滤 - filterButtonsWheres方法有效按钮列表")
    void testFilterButtonsWheres_ValidButtons() {
        // Arrange: 创建测试数据
        IObjectData objectData = new ObjectData();
        objectData.set("name", "test");
        objectData.set("_id", "test_id");
        
        IObjectDescribe describe = new ObjectDescribe();
        describe.setApiName("TestObj");
        
        UdefButton testButton = new UdefButton(testButtonMap);
        testButton.set("describe_api_name", "TestObj");
        testButton.set("is_active", true);
        List<IUdefButton> buttons = Lists.newArrayList(testButton);
        
        // Act: 执行filterButtonsWheres方法
        List<IUdefButton> result = ButtonExt.filterButtonsWheres(objectData, describe, buttons);
        
        // Assert: 验证结果
        assertNotNull(result);
        // 由于没有设置wheres条件，按钮应该被保留
        assertEquals(1, result.size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试filterButtonsWheres静态方法 - 空按钮列表
     */
    @Test
    @DisplayName("边界条件 - filterButtonsWheres方法空按钮列表")
    void testFilterButtonsWheres_EmptyButtons() {
        // Arrange: 创建测试数据
        IObjectData objectData = new ObjectData();
        IObjectDescribe describe = new ObjectDescribe();
        List<IUdefButton> buttons = Lists.newArrayList();
        
        // Act: 执行filterButtonsWheres方法
        List<IUdefButton> result = ButtonExt.filterButtonsWheres(objectData, describe, buttons);
        
        // Assert: 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试filterButtonsWheres静态方法 - null按钮列表
     */
    @Test
    @DisplayName("边界条件 - filterButtonsWheres方法null按钮列表")
    void testFilterButtonsWheres_NullButtons() {
        // Arrange: 创建测试数据
        IObjectData objectData = new ObjectData();
        IObjectDescribe describe = new ObjectDescribe();
        
        // Act: 执行filterButtonsWheres方法
        List<IUdefButton> result = ButtonExt.filterButtonsWheres(objectData, describe, null);
        
        // Assert: 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setIsExposedButton方法
     */
    @Test
    @DisplayName("属性设置 - setIsExposedButton方法")
    void testSetIsExposedButton() {
        // Act: 执行setIsExposedButton方法
        buttonExt.setIsExposedButton(true);
        
        // Assert: 验证结果
        assertEquals(true, buttonExt.getButton().get("is_exposed_button"));
        
        // Act: 设置为false
        buttonExt.setIsExposedButton(false);
        
        // Assert: 验证结果
        assertEquals(false, buttonExt.getButton().get("is_exposed_button"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试copy方法
     */
    @Test
    @DisplayName("复制方法 - copy方法")
    void testCopy() {
        // Act: 执行copy方法
        IUdefButton result = buttonExt.copy();
        
        // Assert: 验证结果
        assertNotNull(result);
        assertNotSame(buttonExt.getButton(), result);
        assertEquals(buttonExt.getApiName(), result.getApiName());
        assertEquals(buttonExt.getLabel(), result.getLabel());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试handlePrintButtonUsePage方法 - 非打印按钮
     */
    @Test
    @DisplayName("业务逻辑 - handlePrintButtonUsePage方法非打印按钮")
    void testHandlePrintButtonUsePage_NonPrintButton() {
        // Act: 执行handlePrintButtonUsePage方法
        assertDoesNotThrow(() -> buttonExt.handlePrintButtonUsePage());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试toMap方法
     */
    @Test
    @DisplayName("数据转换 - toMap方法")
    void testToMap() {
        // Act: 执行toMap方法
        Map<String, Object> result = buttonExt.toMap();
        
        // Assert: 验证结果
        assertNotNull(result);
        assertEquals("test_button", result.get("api_name"));
        assertEquals("测试按钮", result.get("name"));
        assertEquals("Test Button", result.get("label"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试常量值
     */
    @Test
    @DisplayName("常量验证 - 常量值验证")
    void testConstants() {
        // Assert: 验证重要常量
        assertEquals("uipaas", ButtonExt.REDIRECT_UIPAAS);
        assertEquals("url", ButtonExt.REDIRECT_URL);
        assertEquals("ai_agent", ButtonExt.REDIRECT_AI_AGENT);
        
        // 验证ADD_EDIT_BUTTON_API_NAME集合
        assertNotNull(ButtonExt.ADD_EDIT_BUTTON_API_NAME);
        assertFalse(ButtonExt.ADD_EDIT_BUTTON_API_NAME.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多种按钮类型
     */
    @ParameterizedTest
    @ValueSource(strings = {"action", "redirect", "common"})
    @DisplayName("按钮类型 - 多种按钮类型测试")
    void testVariousButtonTypes(String buttonType) {
        // Arrange: 创建不同类型的按钮
        Map<String, Object> buttonMap = Maps.newHashMap(testButtonMap);
        buttonMap.put("button_type", buttonType);
        UdefButton button = new UdefButton(buttonMap);
        ButtonExt buttonExt = ButtonExt.of(button);
        
        // Act & Assert: 验证按钮类型
        assertEquals(buttonType, buttonExt.getButtonType());
        assertNotNull(buttonExt.getApiName());
        assertNotNull(buttonExt.getLabel());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试数据一致性
     */
    @Test
    @DisplayName("一致性验证 - 数据一致性验证")
    void testDataConsistency() {
        // Act: 多次获取相同的数据
        String apiName1 = buttonExt.getApiName();
        String apiName2 = buttonExt.getApiName();
        Map<String, Object> map1 = buttonExt.toMap();
        Map<String, Object> map2 = buttonExt.toMap();
        
        // Assert: 验证数据一致性
        assertEquals(apiName1, apiName2);
        assertEquals(map1.get("api_name"), map2.get("api_name"));
        assertEquals(map1.get("name"), map2.get("name"));
        assertEquals(map1.get("label"), map2.get("label"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试线程安全性
     */
    @Test
    @DisplayName("线程安全 - 多线程访问")
    void testThreadSafety() throws InterruptedException {
        // Act: 在多线程中访问
        Thread thread1 = new Thread(() -> {
            buttonExt.setIsExposedButton(true);
            assertNotNull(buttonExt.getApiName());
        });
        
        Thread thread2 = new Thread(() -> {
            assertDoesNotThrow(() -> buttonExt.copy());
        });
        
        // Assert: 验证线程安全
        assertDoesNotThrow(() -> {
            thread1.start();
            thread2.start();
            thread1.join();
            thread2.join();
        });
    }
}
