package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * LayoutExt JUnit5 单元测试类
 * GenerateByAI
 * 专注于提高代码覆盖率的测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("LayoutExt JUnit5 测试")
class LayoutExtJUnit5Test {

    @Mock
    private ILayout mockLayout;
    
    @Mock
    private IObjectDescribe mockObjectDescribe;
    
    @Mock
    private IComponent mockComponent;
    
    @Mock
    private IFormField mockFormField;
    
    private LayoutExt layoutExt;
    private Map<String, Object> testLayoutMap;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testLayoutMap = Maps.newHashMap();
        testLayoutMap.put("name", "test_layout");
        testLayoutMap.put("layout_type", LayoutTypes.DETAIL);
        testLayoutMap.put("ref_object_api_name", "TestObj");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试通过Map创建LayoutExt实例
     */
    @Test
    @DisplayName("工厂方法 - 通过Map创建LayoutExt")
    void testOfMap_Success() {
        // 执行测试
        LayoutExt result = LayoutExt.of(testLayoutMap);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getLayout());
        assertTrue(result.getLayout() instanceof Layout);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试通过ILayout创建LayoutExt实例
     */
    @Test
    @DisplayName("工厂方法 - 通过ILayout创建LayoutExt")
    void testOfILayout_Success() {
        // 执行测试
        LayoutExt result = LayoutExt.of(mockLayout);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockLayout, result.getLayout());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试通过LayoutExt创建LayoutExt实例（避免双重包装）
     */
    @Test
    @DisplayName("工厂方法 - 避免双重包装")
    void testOfLayoutExt_AvoidDoubleWrapping() {
        // 准备测试数据
        LayoutExt originalLayoutExt = LayoutExt.of(mockLayout);
        
        // 执行测试
        LayoutExt result = LayoutExt.of(originalLayoutExt);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(mockLayout, result.getLayout());
        assertNotEquals(originalLayoutExt, result.getLayout());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试布局类型判断方法
     */
    @Test
    @DisplayName("布局类型判断 - isFlowLayout")
    void testIsFlowLayout() {
        // 测试流程布局
        when(mockLayout.getNamespace()).thenReturn("flow");
        layoutExt = LayoutExt.of(mockLayout);
        assertTrue(layoutExt.isFlowLayout());
        
        // 测试非流程布局
        when(mockLayout.getNamespace()).thenReturn("normal");
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isFlowLayout());
        
        // 测试null值
        when(mockLayout.getNamespace()).thenReturn(null);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isFlowLayout());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试What列表布局判断
     */
    @Test
    @DisplayName("布局类型判断 - isWhatListLayout")
    void testIsWhatListLayout() {
        // 测试What列表布局
        when(mockLayout.getLayoutType()).thenReturn(LayoutTypes.WHAT_LIST);
        layoutExt = LayoutExt.of(mockLayout);
        assertTrue(layoutExt.isWhatListLayout());
        
        // 测试非What列表布局
        when(mockLayout.getLayoutType()).thenReturn(LayoutTypes.DETAIL);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isWhatListLayout());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试编辑布局判断
     */
    @Test
    @DisplayName("布局类型判断 - isEditLayout")
    void testIsEditLayout() {
        // 测试编辑布局
        when(mockLayout.getLayoutType()).thenReturn(LayoutTypes.EDIT);
        layoutExt = LayoutExt.of(mockLayout);
        assertTrue(layoutExt.isEditLayout());
        
        // 测试非编辑布局
        when(mockLayout.getLayoutType()).thenReturn(LayoutTypes.DETAIL);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isEditLayout());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试移动端列表布局判断
     */
    @Test
    @DisplayName("布局类型判断 - isMobileListLayout")
    void testIsMobileListLayout() {
        // 测试移动端列表布局
        when(mockLayout.getLayoutType()).thenReturn(LayoutTypes.LIST);
        layoutExt = LayoutExt.of(mockLayout);
        assertTrue(layoutExt.isMobileListLayout());
        
        // 测试非移动端列表布局
        when(mockLayout.getLayoutType()).thenReturn(LayoutTypes.DETAIL);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isMobileListLayout());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试新布局版本判断
     */
    @Test
    @DisplayName("布局版本判断 - isNewLayout")
    void testIsNewLayout() {
        // 测试新布局
        Map<String, Object> layoutStructure = Maps.newHashMap();
        layoutStructure.put("head", Maps.newHashMap());
        when(mockLayout.getLayoutStructure()).thenReturn(layoutStructure);
        layoutExt = LayoutExt.of(mockLayout);
        assertTrue(layoutExt.isNewLayout());
        
        // 测试旧布局
        when(mockLayout.getLayoutStructure()).thenReturn(null);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isNewLayout());
        
        // 测试空结构
        when(mockLayout.getLayoutStructure()).thenReturn(Maps.newHashMap());
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isNewLayout());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试V2布局判断
     */
    @Test
    @DisplayName("布局版本判断 - isV2Layout")
    void testIsV2Layout() {
        // 测试V2布局（包含head，不包含layout）
        Map<String, Object> layoutStructure = Maps.newHashMap();
        layoutStructure.put("head", Maps.newHashMap());
        when(mockLayout.getLayoutStructure()).thenReturn(layoutStructure);
        layoutExt = LayoutExt.of(mockLayout);
        assertTrue(layoutExt.isV2Layout());
        
        // 测试V3布局（包含layout）
        layoutStructure.put("layout", Maps.newHashMap());
        when(mockLayout.getLayoutStructure()).thenReturn(layoutStructure);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isV2Layout());
        
        // 测试旧布局
        when(mockLayout.getLayoutStructure()).thenReturn(null);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isV2Layout());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试V3布局判断
     */
    @Test
    @DisplayName("布局版本判断 - isV3Layout")
    void testIsV3Layout() {
        // 测试V3布局（包含layout）
        Map<String, Object> layoutStructure = Maps.newHashMap();
        layoutStructure.put("layout", Maps.newHashMap());
        when(mockLayout.getLayoutStructure()).thenReturn(layoutStructure);
        layoutExt = LayoutExt.of(mockLayout);
        assertTrue(layoutExt.isV3Layout());
        
        // 测试V2布局（不包含layout）
        layoutStructure.remove("layout");
        layoutStructure.put("head", Maps.newHashMap());
        when(mockLayout.getLayoutStructure()).thenReturn(layoutStructure);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isV3Layout());
        
        // 测试旧布局
        when(mockLayout.getLayoutStructure()).thenReturn(null);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isV3Layout());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取默认编辑布局名称
     */
    @Test
    @DisplayName("布局名称 - getDefaultEditLayoutName")
    void testGetDefaultEditLayoutName() {
        // 准备测试数据
        when(mockLayout.getName()).thenReturn("test_layout");
        layoutExt = LayoutExt.of(mockLayout);
        
        // 执行测试
        String result = layoutExt.getDefaultEditLayoutName();
        
        // 验证结果
        assertEquals("edit_test_layout", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试设置使用信息
     */
    @Test
    @DisplayName("布局操作 - setUsedInfo")
    void testSetUsedInfo() {
        // 准备测试数据
        layoutExt = LayoutExt.of(testLayoutMap);
        String usedInfo = "test_used_info";
        
        // 执行测试
        layoutExt.setUsedInfo(usedInfo);
        
        // 验证结果
        assertEquals(usedInfo, layoutExt.toMap().get("used_info"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试复制布局
     */
    @Test
    @DisplayName("布局操作 - copy")
    void testCopy() {
        // 准备测试数据
        layoutExt = LayoutExt.of(testLayoutMap);

        // 执行测试
        ILayout result = layoutExt.copy();

        // 验证结果
        assertNotNull(result);
        // copy方法返回的是新的Layout实例，但内容相同
        assertNotSame(layoutExt.getLayout(), result);
        assertEquals(layoutExt.getName(), result.getName());
        assertEquals(layoutExt.getLayoutType(), result.getLayoutType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试转换为Map
     */
    @Test
    @DisplayName("布局操作 - toMap")
    void testToMap() {
        // 准备测试数据
        layoutExt = LayoutExt.of(testLayoutMap);
        
        // 执行测试
        Map<String, Object> result = layoutExt.toMap();
        
        // 验证结果
        assertNotNull(result);
        assertEquals("test_layout", result.get("name"));
        assertEquals(LayoutTypes.DETAIL, result.get("layout_type"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试静默获取组件列表
     */
    @Test
    @DisplayName("组件操作 - getComponentsSilently")
    void testGetComponentsSilently() throws MetadataServiceException {
        // 测试正常情况
        List<IComponent> components = Lists.newArrayList(mockComponent);
        when(mockLayout.getComponents()).thenReturn(components);
        layoutExt = LayoutExt.of(mockLayout);
        
        List<IComponent> result = layoutExt.getComponentsSilently();
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(mockComponent, result.get(0));
        
        // 测试空组件列表
        when(mockLayout.getComponents()).thenReturn(null);
        layoutExt = LayoutExt.of(mockLayout);
        
        result = layoutExt.getComponentsSilently();
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    // ==================== 布局类型判断方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试详情布局判断
     */
    @Test
    @DisplayName("布局类型判断 - isDetailLayout")
    void testIsDetailLayout() {
        // 测试详情布局
        when(mockLayout.getLayoutType()).thenReturn(ILayout.DETAIL_LAYOUT_TYPE);
        layoutExt = LayoutExt.of(mockLayout);
        assertTrue(layoutExt.isDetailLayout());

        // 测试非详情布局
        when(mockLayout.getLayoutType()).thenReturn(LayoutTypes.EDIT);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isDetailLayout());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试列表布局判断
     */
    @Test
    @DisplayName("布局类型判断 - isListLayout")
    void testIsListLayout() {
        // 测试列表布局
        when(mockLayout.getLayoutType()).thenReturn(ListLayoutExt.LIST_LAYOUT);
        layoutExt = LayoutExt.of(mockLayout);
        assertTrue(layoutExt.isListLayout());

        // 测试非列表布局
        when(mockLayout.getLayoutType()).thenReturn(LayoutTypes.DETAIL);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isListLayout());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试流程任务布局判断
     */
    @Test
    @DisplayName("布局类型判断 - isFlowTaskLayout")
    void testIsFlowTaskLayout() {
        // 测试流程任务布局
        when(mockLayout.getLayoutType()).thenReturn(LayoutTypes.FLOW_TASK_LIST);
        layoutExt = LayoutExt.of(mockLayout);
        assertTrue(layoutExt.isFlowTaskLayout());

        // 测试非流程任务布局
        when(mockLayout.getLayoutType()).thenReturn(LayoutTypes.DETAIL);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isFlowTaskLayout());
    }

    // ==================== 布局版本判断方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试V2布局判断 - 扩展测试
     */
    @Test
    @DisplayName("布局版本判断 - isV2Layout扩展测试")
    void testIsV2Layout_Extended() {
        // 测试V2布局 - 包含head但不包含layout
        Map<String, Object> layoutStructure = Maps.newHashMap();
        layoutStructure.put("head", Maps.newHashMap());
        when(mockLayout.getLayoutStructure()).thenReturn(layoutStructure);
        layoutExt = LayoutExt.of(mockLayout);
        assertTrue(layoutExt.isV2Layout());

        // 测试V3布局 - 包含layout（不是V2）
        layoutStructure = Maps.newHashMap();
        layoutStructure.put("layout", Maps.newHashMap());
        when(mockLayout.getLayoutStructure()).thenReturn(layoutStructure);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isV2Layout());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试V3布局判断 - 扩展测试
     */
    @Test
    @DisplayName("布局版本判断 - isV3Layout扩展测试")
    void testIsV3Layout_Extended() {
        // 测试V3布局 - 包含layout
        Map<String, Object> layoutStructure = Maps.newHashMap();
        layoutStructure.put("layout", Maps.newHashMap());
        when(mockLayout.getLayoutStructure()).thenReturn(layoutStructure);
        layoutExt = LayoutExt.of(mockLayout);
        assertTrue(layoutExt.isV3Layout());

        // 测试V2布局 - 只包含head（不是V3）
        layoutStructure = Maps.newHashMap();
        layoutStructure.put("head", Maps.newHashMap());
        when(mockLayout.getLayoutStructure()).thenReturn(layoutStructure);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isV3Layout());
    }

    // ==================== 字段操作方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试字段是否在布局中
     */
    @Test
    @DisplayName("字段操作 - containsField")
    void testContainsField() {
        // 准备测试数据
        layoutExt = LayoutExt.of(testLayoutMap);

        // 测试字段不存在的情况（因为没有实际的表单组件）
        boolean result = layoutExt.containsField("test_field");
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试字段是否在布局中（别名方法）
     */
    @Test
    @DisplayName("字段操作 - isFieldInLayout")
    void testIsFieldInLayout() {
        // 准备测试数据
        layoutExt = LayoutExt.of(testLayoutMap);

        // 测试字段不存在的情况
        boolean result = layoutExt.isFieldInLayout("test_field");
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试分组是否在布局中
     */
    @Test
    @DisplayName("字段操作 - isSectionInLayout")
    void testIsSectionInLayout() {
        // 准备测试数据
        layoutExt = LayoutExt.of(testLayoutMap);

        // 测试分组不存在的情况
        boolean result = layoutExt.isSectionInLayout("test_section");
        assertFalse(result);
    }

    // ==================== 组件操作方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试组件是否包含在布局中
     */
    @Test
    @DisplayName("组件操作 - containsComponent")
    void testContainsComponent() {
        // 准备测试数据
        layoutExt = LayoutExt.of(testLayoutMap);

        // 测试组件不存在的情况
        boolean result = layoutExt.containsComponent("test_component");
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试组件是否隐藏
     */
    @Test
    @DisplayName("组件操作 - isComponentHidden")
    void testIsComponentHidden() {
        // 测试非流程布局中不存在的组件
        when(mockLayout.getNamespace()).thenReturn("normal");
        layoutExt = LayoutExt.of(mockLayout);
        boolean result = layoutExt.isComponentHidden("test_component");
        assertFalse(result); // 非流程布局中，不存在的组件不算隐藏

        // 测试流程布局中不存在的组件
        when(mockLayout.getNamespace()).thenReturn("flow");
        layoutExt = LayoutExt.of(mockLayout);
        result = layoutExt.isComponentHidden("test_component");
        assertTrue(result); // 流程布局中，不存在的组件算隐藏
    }

    // ==================== 布局操作方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试获取默认编辑布局名称 - 扩展测试
     */
    @Test
    @DisplayName("布局操作 - getDefaultEditLayoutName扩展测试")
    void testGetDefaultEditLayoutName_Extended() {
        // 准备测试数据
        when(mockLayout.getName()).thenReturn("custom_layout");
        layoutExt = LayoutExt.of(mockLayout);

        // 执行测试
        String result = layoutExt.getDefaultEditLayoutName();

        // 验证结果
        assertEquals("edit_custom_layout", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试设置使用信息 - 扩展测试
     */
    @Test
    @DisplayName("布局操作 - setUsedInfo扩展测试")
    void testSetUsedInfo_Extended() {
        // 准备测试数据
        layoutExt = LayoutExt.of(testLayoutMap);
        String usedInfo = "extended_used_info";

        // 执行测试
        layoutExt.setUsedInfo(usedInfo);

        // 验证结果
        assertEquals(usedInfo, layoutExt.toMap().get("used_info"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试移除布局结构
     */
    @Test
    @DisplayName("布局操作 - removeLayoutStructure")
    void testRemoveLayoutStructure() {
        // 准备测试数据
        layoutExt = LayoutExt.of(testLayoutMap);

        // 执行测试
        layoutExt.removeLayoutStructure();

        // 验证结果 - 布局结构应该被移除
        assertFalse(layoutExt.toMap().containsKey("layout_structure"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试移除UI事件
     */
    @Test
    @DisplayName("布局操作 - removeUIEvent")
    void testRemoveUIEvent() {
        // 准备测试数据
        layoutExt = LayoutExt.of(testLayoutMap);

        // 执行测试 - 这个方法主要是移除事件，不会抛异常
        assertDoesNotThrow(() -> layoutExt.removeUIEvent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试移除顶部信息
     */
    @Test
    @DisplayName("布局操作 - removeTopInfo")
    void testRemoveTopInfo() {
        // 准备测试数据
        layoutExt = LayoutExt.of(testLayoutMap);

        // 执行测试
        layoutExt.removeTopInfo();

        // 验证结果 - 顶部信息组件应该被移除
        assertFalse(layoutExt.toMap().containsKey("top_info"));
    }

    // ==================== 静态方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试获取渲染类型 - 简单版本
     */
    @Test
    @DisplayName("静态方法 - getRenderType简单版本")
    void testGetRenderType_Simple() {
        // 执行测试
        String result = LayoutExt.getRenderType("test_field", "text");

        // 验证结果 - 应该返回字段类型
        assertEquals("text", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取渲染类型 - 完整版本
     */
    @Test
    @DisplayName("静态方法 - getRenderType完整版本")
    void testGetRenderType_Full() {
        // 执行测试
        String result = LayoutExt.getRenderType("TestObj", "test_field", "number");

        // 验证结果 - 应该返回字段类型
        assertEquals("number", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取相关对象组件API名称
     */
    @Test
    @DisplayName("静态方法 - getRelatedObjectComponentApiName")
    void testGetRelatedObjectComponentApiName() {
        // 准备测试数据
        RelatedObjectDescribeStructure mockStructure = mock(RelatedObjectDescribeStructure.class);
        IObjectDescribe mockRelatedObject = mock(IObjectDescribe.class);
        when(mockStructure.getRelatedObjectDescribe()).thenReturn(mockRelatedObject);
        when(mockRelatedObject.getApiName()).thenReturn("RelatedObj");
        when(mockStructure.getFieldApiName()).thenReturn("related_field");

        // 执行测试
        String result = LayoutExt.getRelatedObjectComponentApiName(mockStructure);

        // 验证结果
        assertEquals("RelatedObj_related_field_related_list", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取相关列表问题键
     */
    @Test
    @DisplayName("静态方法 - getRelatedListIssueKey")
    void testGetRelatedListIssueKey() {
        // 执行测试
        String result = LayoutExt.getRelatedListIssueKey("TestObj", "test_field");

        // 验证结果
        assertEquals("TestObj_test_field|related_list_issue", result);
    }

    // ==================== 移动端布局方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试移动端布局启用判断
     */
    @Test
    @DisplayName("移动端布局 - isEnableMobileLayout")
    void testIsEnableMobileLayout() {
        // 测试未启用移动端布局
        when(mockLayout.getEnableMobileLayout()).thenReturn(false);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isEnableMobileLayout());

        // 测试启用但没有移动端布局内容
        when(mockLayout.getEnableMobileLayout()).thenReturn(true);
        when(mockLayout.getMobileLayout()).thenReturn(null);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isEnableMobileLayout());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试侧边栏布局启用判断
     */
    @Test
    @DisplayName("移动端布局 - isEnableSidebarLayout")
    void testIsEnableSidebarLayout() {
        // 测试未启用侧边栏布局
        when(mockLayout.getEnableSidebarLayout()).thenReturn(false);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isEnableSidebarLayout());

        // 测试启用但没有侧边栏布局内容
        when(mockLayout.getEnableSidebarLayout()).thenReturn(true);
        when(mockLayout.getSidebarLayout()).thenReturn(null);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isEnableSidebarLayout());
    }

    // ==================== 默认值判断方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试是否为默认布局
     */
    @Test
    @DisplayName("默认值判断 - isDefault")
    void testIsDefault() {
        // 测试默认布局
        when(mockLayout.isDefault()).thenReturn(true);
        layoutExt = LayoutExt.of(mockLayout);
        assertTrue(layoutExt.isDefault());

        // 测试非默认布局
        when(mockLayout.isDefault()).thenReturn(false);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isDefault());

        // 测试null情况
        when(mockLayout.isDefault()).thenReturn(null);
        layoutExt = LayoutExt.of(mockLayout);
        assertFalse(layoutExt.isDefault());
    }

    // ==================== 组件获取方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试获取表单组件
     */
    @Test
    @DisplayName("组件获取 - getFormComponents")
    void testGetFormComponents() {
        // 准备测试数据
        layoutExt = LayoutExt.of(testLayoutMap);

        // 执行测试
        List<FormComponentExt> result = layoutExt.getFormComponents();

        // 验证结果 - 应该返回空列表（因为没有实际的组件）
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取表单表格
     */
    @Test
    @DisplayName("组件获取 - getFormTables")
    void testGetFormTables() {
        // 准备测试数据
        layoutExt = LayoutExt.of(testLayoutMap);

        // 执行测试
        List<FormTable> result = layoutExt.getFormTables();

        // 验证结果 - 应该返回空列表
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取主从组件
     */
    @Test
    @DisplayName("组件获取 - getMasterDetailComponents")
    void testGetMasterDetailComponents() {
        // 准备测试数据
        layoutExt = LayoutExt.of(testLayoutMap);

        // 执行测试
        List<IComponent> result = layoutExt.getMasterDetailComponents();

        // 验证结果 - 应该返回空列表
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取相关列表组件名称
     */
    @Test
    @DisplayName("组件获取 - getRelatedListComponentNames")
    void testGetRelatedListComponentNames() {
        // 准备测试数据
        layoutExt = LayoutExt.of(testLayoutMap);

        // 执行测试
        List<String> result = layoutExt.getRelatedListComponentNames();

        // 验证结果 - 应该返回空列表
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取主从组件名称
     */
    @Test
    @DisplayName("组件获取 - getMasterDetailComponentNames")
    void testGetMasterDetailComponentNames() {
        // 准备测试数据
        layoutExt = LayoutExt.of(testLayoutMap);

        // 执行测试
        List<String> result = layoutExt.getMasterDetailComponentNames();

        // 验证结果 - 应该返回空列表
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取组件顺序
     */
    @Test
    @DisplayName("组件操作 - getComponentOrder")
    void testGetComponentOrder() {
        // 准备测试数据
        layoutExt = LayoutExt.of(testLayoutMap);

        // 执行测试 - 测试不存在的组件
        int result = layoutExt.getComponentOrder("non_existing_component");

        // 验证结果 - 不存在的组件应该返回9999（根据实际实现）
        assertEquals(9999, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取详情组件顺序
     */
    @Test
    @DisplayName("组件操作 - getDetailComponentOrder")
    void testGetDetailComponentOrder() {
        // 准备测试数据
        layoutExt = LayoutExt.of(testLayoutMap);

        // 执行测试
        int result = layoutExt.getDetailComponentOrder("TestObj");

        // 验证结果 - 不存在的组件应该返回9999（根据实际实现）
        assertEquals(9999, result);
    }
}
