package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IGroupComponent;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import java.util.Arrays;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ComponentExt的JUnit 5测试类
 * 测试组件扩展类功能
 */
class ComponentExtJUnit5Test {

    private IComponent mockComponent;
    private IButton mockButton1;
    private IButton mockButton2;
    private IButton mockButton3;

    @BeforeEach
    void setUp() {
        mockComponent = mock(IComponent.class);
        mockButton1 = mock(IButton.class);
        mockButton2 = mock(IButton.class);
        mockButton3 = mock(IButton.class);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of工厂方法
     */
    @Test
    @DisplayName("of - 工厂方法")
    void testOf() {
        ComponentExt result = ComponentExt.of(mockComponent);

        assertNotNull(result);
        assertSame(mockComponent, result.getComponent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of工厂方法 - null参数
     */
    @Test
    @DisplayName("of - null参数")
    void testOf_NullComponent() {
        assertThrows(NullPointerException.class, () -> ComponentExt.of(null));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getConfigKey静态方法
     */
    @Test
    @DisplayName("getConfigKey - 生成配置键")
    void testGetConfigKey() {
        String objectApiName = "TestObject";
        
        String result = ComponentExt.getConfigKey(objectApiName);
        
        assertEquals("component_config_TestObject", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getConfigKey静态方法 - 空字符串
     */
    @Test
    @DisplayName("getConfigKey - 空字符串")
    void testGetConfigKey_EmptyString() {
        String result = ComponentExt.getConfigKey("");
        
        assertEquals("component_config_", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试removeButtonByActions方法 - 有按钮
     */
    @Test
    @DisplayName("removeButtonByActions - 有按钮")
    void testRemoveButtonByActions_WithButtons() {
        List<String> actionsToRemove = Arrays.asList("action1", "action3");
        List<IButton> buttons = Lists.newArrayList(mockButton1, mockButton2, mockButton3);
        
        when(mockButton1.getAction()).thenReturn("action1");
        when(mockButton2.getAction()).thenReturn("action2");
        when(mockButton3.getAction()).thenReturn("action3");
        when(mockComponent.getButtons()).thenReturn(buttons);

        ComponentExt componentExt = ComponentExt.of(mockComponent);
        componentExt.removeButtonByActions(actionsToRemove);

        assertEquals(1, buttons.size());
        assertTrue(buttons.contains(mockButton2));
        assertFalse(buttons.contains(mockButton1));
        assertFalse(buttons.contains(mockButton3));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试removeButtonByActions方法 - 无按钮
     */
    @Test
    @DisplayName("removeButtonByActions - 无按钮")
    void testRemoveButtonByActions_NoButtons() {
        List<String> actionsToRemove = Arrays.asList("action1", "action2");
        when(mockComponent.getButtons()).thenReturn(null);

        ComponentExt componentExt = ComponentExt.of(mockComponent);
        
        assertDoesNotThrow(() -> componentExt.removeButtonByActions(actionsToRemove));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试removeButtonByActions方法 - 空按钮列表
     */
    @Test
    @DisplayName("removeButtonByActions - 空按钮列表")
    void testRemoveButtonByActions_EmptyButtonList() {
        List<String> actionsToRemove = Arrays.asList("action1", "action2");
        when(mockComponent.getButtons()).thenReturn(Lists.newArrayList());

        ComponentExt componentExt = ComponentExt.of(mockComponent);
        
        assertDoesNotThrow(() -> componentExt.removeButtonByActions(actionsToRemove));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试removeButtonByActionsDeeply方法 - 组件组
     */
    @Test
    @DisplayName("removeButtonByActionsDeeply - 组件组")
    void testRemoveButtonByActionsDeeply_GroupComponent() {
        IGroupComponent mockGroupComponent = mock(IGroupComponent.class);
        List<String> actionsToRemove = Arrays.asList("action1", "action2");

        try (MockedStatic<GroupComponentExt> groupComponentExtMock = mockStatic(GroupComponentExt.class)) {
            GroupComponentExt mockGroupComponentExt = mock(GroupComponentExt.class);
            groupComponentExtMock.when(() -> GroupComponentExt.of(mockGroupComponent)).thenReturn(mockGroupComponentExt);

            ComponentExt componentExt = ComponentExt.of(mockGroupComponent);
            componentExt.removeButtonByActionsDeeply(actionsToRemove);

            verify(mockGroupComponentExt).removeButtonByActionsDeeply(actionsToRemove);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试removeEmptyOrDuplicateComponent静态方法
     */
    @Test
    @DisplayName("removeEmptyOrDuplicateComponent - 移除空和重复组件")
    void testRemoveEmptyOrDuplicateComponent() {
        List<Map<String, Object>> components = Lists.newArrayList();
        
        Map<String, Object> component1 = Maps.newHashMap();
        component1.put("api_name", "component1");
        component1.put("type", "type1");
        
        Map<String, Object> component2 = Maps.newHashMap();
        component2.put("api_name", "component2");
        component2.put("type", "type2");
        
        Map<String, Object> component3 = Maps.newHashMap();
        component3.put("api_name", "component1"); // 重复
        component3.put("type", "type3");
        
        Map<String, Object> component4 = Maps.newHashMap();
        component4.put("api_name", ""); // 空名称
        component4.put("type", "type4");
        
        Map<String, Object> component5 = Maps.newHashMap();
        component5.put("api_name", null); // null名称
        component5.put("type", "type5");
        
        components.add(component1);
        components.add(component2);
        components.add(component3);
        components.add(component4);
        components.add(component5);

        ComponentExt.removeEmptyOrDuplicateComponent(components);

        assertEquals(2, components.size());
        assertTrue(components.contains(component1));
        assertTrue(components.contains(component2));
        assertFalse(components.contains(component3));
        assertFalse(components.contains(component4));
        assertFalse(components.contains(component5));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试removeEmptyOrDuplicateComponent静态方法 - 空列表
     */
    @Test
    @DisplayName("removeEmptyOrDuplicateComponent - 空列表")
    void testRemoveEmptyOrDuplicateComponent_EmptyList() {
        List<Map<String, Object>> components = Lists.newArrayList();
        
        assertDoesNotThrow(() -> ComponentExt.removeEmptyOrDuplicateComponent(components));
        assertTrue(components.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试removeEmptyOrDuplicateComponent静态方法 - null列表
     */
    @Test
    @DisplayName("removeEmptyOrDuplicateComponent - null列表")
    void testRemoveEmptyOrDuplicateComponent_NullList() {
        assertDoesNotThrow(() -> ComponentExt.removeEmptyOrDuplicateComponent(null));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试filterComponentsByWheres方法 - 无条件
     */
    @Test
    @DisplayName("filterComponentsByWheres - 无条件")
    void testFilterComponentsByWheres_NoConditions() {
        when(mockComponent.get("wheres")).thenReturn(null);
        
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("_id", "1");
        dataMap.put("name", "test");
        IObjectData objectData = new ObjectData(dataMap);
        
        IObjectDescribe describe = new ObjectDescribe();
        describe.setApiName("TestObj");

        ComponentExt componentExt = ComponentExt.of(mockComponent);
        boolean result = componentExt.filterComponentsByWheres(objectData, describe);

        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试filterComponentsByWheres方法 - null对象数据
     */
    @Test
    @DisplayName("filterComponentsByWheres - null对象数据")
    void testFilterComponentsByWheres_NullObjectData() {
        IObjectDescribe describe = new ObjectDescribe();
        describe.setApiName("TestObj");

        ComponentExt componentExt = ComponentExt.of(mockComponent);
        boolean result = componentExt.filterComponentsByWheres(null, describe);

        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getWheres方法 - 空条件
     */
    @Test
    @DisplayName("getWheres - 空条件")
    void testGetWheres_EmptyConditions() {
        when(mockComponent.get("wheres")).thenReturn(null);

        ComponentExt componentExt = ComponentExt.of(mockComponent);
        List<Wheres> result = componentExt.getWheres();

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isDepend方法 - 依赖为true
     */
    @Test
    @DisplayName("isDepend - 依赖为true")
    void testIsDepend_True() {
        when(mockComponent.get("is_depend")).thenReturn(Boolean.TRUE);

        ComponentExt componentExt = ComponentExt.of(mockComponent);
        boolean result = componentExt.isDepend();

        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isDepend方法 - 依赖为false
     */
    @Test
    @DisplayName("isDepend - 依赖为false")
    void testIsDepend_False() {
        when(mockComponent.get("is_depend")).thenReturn(Boolean.FALSE);

        ComponentExt componentExt = ComponentExt.of(mockComponent);
        boolean result = componentExt.isDepend();

        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isDepend方法 - 依赖为null
     */
    @Test
    @DisplayName("isDepend - 依赖为null")
    void testIsDepend_Null() {
        when(mockComponent.get("is_depend")).thenReturn(null);

        ComponentExt componentExt = ComponentExt.of(mockComponent);
        boolean result = componentExt.isDepend();

        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getBiComponentNameI18nKeyList方法 - 有CardID
     */
    @Test
    @DisplayName("getBiComponentNameI18nKeyList - 有CardID")
    void testGetBiComponentNameI18nKeyList_WithCardId() {
        when(mockComponent.get("CardID", String.class)).thenReturn("test-card-123");

        ComponentExt componentExt = ComponentExt.of(mockComponent);
        List<String> result = (List<String>) componentExt.getBiComponentNameI18nKeyList();

        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.contains("Bi.Custom.Realtime.RptName.test-card-123.Label"));
        assertTrue(result.contains("Bi.Custom.Realtime.StatName.test-card-123.Label"));
        assertTrue(result.contains("Bi.Custom.Realtime.DashBoardName.test-card-123.Label"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getBiComponentNameI18nKeyList方法 - 无CardID
     */
    @Test
    @DisplayName("getBiComponentNameI18nKeyList - 无CardID")
    void testGetBiComponentNameI18nKeyList_NoCardId() {
        when(mockComponent.get("CardID", String.class)).thenReturn(null);

        ComponentExt componentExt = ComponentExt.of(mockComponent);
        List<String> result = (List<String>) componentExt.getBiComponentNameI18nKeyList();

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getBiComponentNameI18nKeyList方法 - 空CardID
     */
    @Test
    @DisplayName("getBiComponentNameI18nKeyList - 空CardID")
    void testGetBiComponentNameI18nKeyList_BlankCardId() {
        when(mockComponent.get("CardID", String.class)).thenReturn("");

        ComponentExt componentExt = ComponentExt.of(mockComponent);
        List<String> result = (List<String>) componentExt.getBiComponentNameI18nKeyList();

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
