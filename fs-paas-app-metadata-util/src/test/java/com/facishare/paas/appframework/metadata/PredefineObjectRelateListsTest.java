package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.Tuple;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * PredefineObjectRelateLists 单元测试类
 * 
 * GenerateByAI
 * 测试内容描述：测试预定义对象关联列表的配置加载和获取功能
 */
@ExtendWith(MockitoExtension.class)
class PredefineObjectRelateListsTest {

    /**
     * GenerateByAI
     * 测试内容描述：测试正常获取预定义关联列表
     */
    @Test
    @DisplayName("正常场景 - 获取存在的对象关联列表")
    void testGetRelateLists_ExistingApiName() {
        // 执行被测试方法
        List<String> result = PredefineObjectRelateLists.getRelateLists("AccountObj");
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertTrue(result.contains("account_contract_list"));
        assertTrue(result.contains("account_invoiceapp_list"));
        assertTrue(result.contains("account_contact_list"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取不存在的API名称对应的关联列表
     */
    @Test
    @DisplayName("边界场景 - 获取不存在的API名称")
    void testGetRelateLists_NonExistentApiName() {
        // 执行被测试方法
        List<String> result = PredefineObjectRelateLists.getRelateLists("NonExistentObj");
        
        // 验证结果
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试传入null参数的情况
     */
    @Test
    @DisplayName("异常场景 - 传入null参数")
    void testGetRelateLists_NullApiName() {
        // 执行被测试方法
        List<String> result = PredefineObjectRelateLists.getRelateLists(null);
        
        // 验证结果
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试传入空字符串参数的情况
     */
    @Test
    @DisplayName("边界场景 - 传入空字符串")
    void testGetRelateLists_EmptyApiName() {
        // 执行被测试方法
        List<String> result = PredefineObjectRelateLists.getRelateLists("");
        
        // 验证结果
        assertNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：参数化测试验证所有已知的对象类型
     */
    @ParameterizedTest
    @MethodSource("provideKnownApiNames")
    @DisplayName("参数化测试 - 验证已知对象类型的关联列表")
    void testGetRelateLists_KnownApiNames(String apiName, boolean shouldExist, int expectedMinSize) {
        // 执行被测试方法
        List<String> result = PredefineObjectRelateLists.getRelateLists(apiName);
        
        // 验证结果
        if (shouldExist) {
            assertNotNull(result, "API名称 " + apiName + " 应该有对应的关联列表");
            assertTrue(result.size() >= expectedMinSize, 
                "API名称 " + apiName + " 的关联列表大小应该至少为 " + expectedMinSize);
        } else {
            assertNull(result, "API名称 " + apiName + " 不应该有对应的关联列表");
        }
    }

    /**
     * 提供已知API名称的测试数据
     */
    private static Stream<Arguments> provideKnownApiNames() {
        return Stream.of(
            Arguments.of("AccountObj", true, 10),
            Arguments.of("ContactObj", true, 1),
            Arguments.of("OpportunityObj", true, 3),
            Arguments.of("SalesOrderObj", true, 5),
            Arguments.of("MarketingEventObj", true, 1),
            Arguments.of("UnknownObj", false, 0)
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试静态初始化过程中ResourceLoader异常的处理
     */
    @Test
    @DisplayName("异常场景 - ResourceLoader加载异常时的处理")
    void testInitialization_ResourceLoaderException() {
        // 这个测试验证类的静态初始化是否正常完成
        // 由于静态初始化在类加载时就完成了，我们主要验证结果状态
        
        // 验证静态初始化成功，至少有一些预定义的映射
        List<String> accountResult = PredefineObjectRelateLists.getRelateLists("AccountObj");
        assertNotNull(accountResult, "静态初始化应该成功加载AccountObj的配置");
        
        List<String> contactResult = PredefineObjectRelateLists.getRelateLists("ContactObj");
        assertNotNull(contactResult, "静态初始化应该成功加载ContactObj的配置");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试配置文件内容的完整性
     */
    @Test
    @DisplayName("完整性验证 - 验证配置文件内容的正确性")
    void testConfigurationIntegrity() {
        // 验证AccountObj的配置完整性
        List<String> accountLists = PredefineObjectRelateLists.getRelateLists("AccountObj");
        assertNotNull(accountLists);
        
        // 验证包含预期的关联列表项
        String[] expectedAccountLists = {
            "account_contract_list", "account_invoiceapp_list", "account_refund_list",
            "account_returned_goods_list", "account_sales_order_list", "account_payment_list",
            "account_visiting_list", "account_add_list", "account_att_list",
            "account_contact_list", "account_oppo_list", "account_fininfo_list", "account_leads_list"
        };
        
        for (String expectedList : expectedAccountLists) {
            assertTrue(accountLists.contains(expectedList), 
                "AccountObj应该包含关联列表: " + expectedList);
        }
        
        // 验证OpportunityObj的配置
        List<String> opportunityLists = PredefineObjectRelateLists.getRelateLists("OpportunityObj");
        assertNotNull(opportunityLists);
        assertTrue(opportunityLists.contains("opportunity_sales_order_list"));
        assertTrue(opportunityLists.contains("opportunity_visiting_list"));
        assertTrue(opportunityLists.contains("opportunity_contact_list"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试方法的幂等性
     */
    @Test
    @DisplayName("幂等性验证 - 多次调用返回相同结果")
    void testGetRelateLists_Idempotency() {
        String apiName = "AccountObj";
        
        // 多次调用同一个方法
        List<String> result1 = PredefineObjectRelateLists.getRelateLists(apiName);
        List<String> result2 = PredefineObjectRelateLists.getRelateLists(apiName);
        List<String> result3 = PredefineObjectRelateLists.getRelateLists(apiName);
        
        // 验证结果一致性
        assertNotNull(result1);
        assertNotNull(result2);
        assertNotNull(result3);
        
        assertEquals(result1.size(), result2.size());
        assertEquals(result2.size(), result3.size());
        
        // 验证内容完全相同
        assertTrue(result1.containsAll(result2));
        assertTrue(result2.containsAll(result3));
        assertTrue(result3.containsAll(result1));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试返回列表的不可变性（防御性编程）
     */
    @Test
    @DisplayName("安全性验证 - 验证返回列表的安全性")
    void testGetRelateLists_ReturnedListSafety() {
        List<String> result = PredefineObjectRelateLists.getRelateLists("AccountObj");
        assertNotNull(result);
        
        int originalSize = result.size();
        
        // 尝试修改返回的列表（这应该不会影响内部状态）
        assertDoesNotThrow(() -> {
            // 如果返回的是不可变列表，这里会抛异常
            // 如果返回的是可变列表，修改不应该影响后续调用
            try {
                result.add("test_item");
            } catch (UnsupportedOperationException e) {
                // 这是期望的行为 - 返回不可变列表
            }
        });
        
        // 验证后续调用不受影响
        List<String> newResult = PredefineObjectRelateLists.getRelateLists("AccountObj");
        assertNotNull(newResult);
        
        // 如果原列表是可变的且被修改了，新结果应该不包含测试项
        assertFalse(newResult.contains("test_item"));
    }
}
