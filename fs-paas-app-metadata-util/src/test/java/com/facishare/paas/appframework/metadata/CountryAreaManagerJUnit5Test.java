package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.support.CountryAreaService;
import com.facishare.paas.metadata.support.CountryAreaService.AreaInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * CountryAreaManager的JUnit 5测试类
 * 测试国家地区管理器功能
 */
class CountryAreaManagerJUnit5Test {

    private String testTenantId;
    private CountryAreaService mockCountryAreaService;

    @BeforeEach
    void setUp() {
        testTenantId = "test-tenant-123";
        mockCountryAreaService = mock(CountryAreaService.class);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Node工厂方法
     */
    @Test
    @DisplayName("Node.of - 工厂方法")
    void testNode_Of() {
        String code = "CN";
        String name = "中国";

        CountryAreaManager.Node node = CountryAreaManager.Node.of(code, name);

        assertNotNull(node);
        assertEquals(code, node.getCode());
        assertEquals(name, node.getName());
        assertNotNull(node.getChildren());
        assertTrue(node.getChildren().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Node的getter和setter方法
     */
    @Test
    @DisplayName("Node - getter和setter方法")
    void testNode_GettersAndSetters() {
        CountryAreaManager.Node node = new CountryAreaManager.Node();
        
        String code = "US";
        String name = "美国";
        List<CountryAreaManager.Node> children = Lists.newArrayList();
        children.add(CountryAreaManager.Node.of("CA", "加利福尼亚"));

        node.setCode(code);
        node.setName(name);
        node.setChildren(children);

        assertEquals(code, node.getCode());
        assertEquals(name, node.getName());
        assertEquals(children, node.getChildren());
        assertEquals(1, node.getChildren().size());
        assertEquals("CA", node.getChildren().get(0).getCode());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabelByCode方法 - 基本功能
     */
    @Test
    @DisplayName("getLabelByCode - 基本功能")
    void testGetLabelByCode_Basic() {
        try (MockedStatic<RequestUtil> requestUtilMock = Mockito.mockStatic(RequestUtil.class);
             MockedStatic<CountryAreaService> countryAreaServiceMock = Mockito.mockStatic(CountryAreaService.class)) {
            
            // Mock RequestUtil.getCurrentLang()
            requestUtilMock.when(RequestUtil::getCurrentLang).thenReturn(Lang.zh_CN);
            
            // Mock CountryAreaService.countryJsonIsParsedFromDB()
            countryAreaServiceMock.when(() -> CountryAreaService.countryJsonIsParsedFromDB(testTenantId))
                    .thenReturn(false);

            // 由于静态初始化的限制，这个测试主要验证方法不会抛出异常
            String result = CountryAreaManager.getLabelByCode(testTenantId, "CN", IFieldType.COUNTRY);
            
            // 验证返回值不为null（可能是code本身或者映射的label）
            assertNotNull(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabelByCode方法 - 使用数据库数据
     */
    @Test
    @DisplayName("getLabelByCode - 使用数据库数据")
    void testGetLabelByCode_FromDatabase() {
        try (MockedStatic<RequestUtil> requestUtilMock = Mockito.mockStatic(RequestUtil.class);
             MockedStatic<CountryAreaService> countryAreaServiceMock = Mockito.mockStatic(CountryAreaService.class)) {
            
            requestUtilMock.when(RequestUtil::getCurrentLang).thenReturn(Lang.zh_CN);
            countryAreaServiceMock.when(() -> CountryAreaService.countryJsonIsParsedFromDB(testTenantId))
                    .thenReturn(true);

            // 这个测试主要验证方法调用路径
            String result = CountryAreaManager.getLabelByCode(testTenantId, "CN", IFieldType.COUNTRY);
            
            assertNotNull(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabelByCode方法 - 指定语言
     */
    @Test
    @DisplayName("getLabelByCode - 指定语言")
    void testGetLabelByCode_WithLang() {
        try (MockedStatic<CountryAreaService> countryAreaServiceMock = Mockito.mockStatic(CountryAreaService.class)) {
            
            countryAreaServiceMock.when(() -> CountryAreaService.countryJsonIsParsedFromDB(testTenantId))
                    .thenReturn(false);

            String result = CountryAreaManager.getLabelByCode(testTenantId, "CN", IFieldType.COUNTRY, Lang.en);
            
            assertNotNull(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getCodeByLabel方法 - 空标签
     */
    @Test
    @DisplayName("getCodeByLabel - 空标签")
    void testGetCodeByLabel_EmptyLabel() {
        String result = CountryAreaManager.getCodeByLabel(testTenantId, "", IFieldType.COUNTRY, Lang.zh_CN);
        assertEquals("", result);

        result = CountryAreaManager.getCodeByLabel(testTenantId, null, IFieldType.COUNTRY, Lang.zh_CN);
        assertEquals("", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getCodeByLabel方法 - 基本功能
     */
    @Test
    @DisplayName("getCodeByLabel - 基本功能")
    void testGetCodeByLabel_Basic() {
        try (MockedStatic<CountryAreaService> countryAreaServiceMock = Mockito.mockStatic(CountryAreaService.class)) {
            
            countryAreaServiceMock.when(() -> CountryAreaService.countryJsonIsParsedFromDB(testTenantId))
                    .thenReturn(false);

            String result = CountryAreaManager.getCodeByLabel(testTenantId, "中国", IFieldType.COUNTRY, Lang.zh_CN);
            
            assertNotNull(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getCodeByLabel方法 - 使用数据库数据
     */
    @Test
    @DisplayName("getCodeByLabel - 使用数据库数据")
    void testGetCodeByLabel_FromDatabase() {
        try (MockedStatic<CountryAreaService> countryAreaServiceMock = Mockito.mockStatic(CountryAreaService.class)) {
            
            countryAreaServiceMock.when(() -> CountryAreaService.countryJsonIsParsedFromDB(testTenantId))
                    .thenReturn(true);

            String result = CountryAreaManager.getCodeByLabel(testTenantId, "中国", IFieldType.COUNTRY, Lang.zh_CN);
            
            assertNotNull(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getZoningCodeByLabel方法 - 空参数
     */
    @Test
    @DisplayName("getZoningCodeByLabel - 空参数")
    void testGetZoningCodeByLabel_EmptyParams() {
        AreaInfo result = CountryAreaManager.getZoningCodeByLabel(testTenantId, null, IFieldType.COUNTRY);
        assertNotNull(result);

        result = CountryAreaManager.getZoningCodeByLabel(testTenantId, "", IFieldType.COUNTRY);
        assertNotNull(result);

        result = CountryAreaManager.getZoningCodeByLabel(testTenantId, "中国", null);
        assertNotNull(result);

        result = CountryAreaManager.getZoningCodeByLabel(testTenantId, "中国", "");
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getZoningCodeByLabel方法 - 基本功能
     */
    @Test
    @DisplayName("getZoningCodeByLabel - 基本功能")
    void testGetZoningCodeByLabel_Basic() {
        try (MockedStatic<RequestUtil> requestUtilMock = Mockito.mockStatic(RequestUtil.class);
             MockedStatic<CountryAreaService> countryAreaServiceMock = Mockito.mockStatic(CountryAreaService.class)) {
            
            requestUtilMock.when(RequestUtil::getCurrentLang).thenReturn(Lang.zh_CN);
            countryAreaServiceMock.when(() -> CountryAreaService.countryJsonIsParsedFromDB(testTenantId))
                    .thenReturn(false);

            AreaInfo result = CountryAreaManager.getZoningCodeByLabel(testTenantId, "中国", IFieldType.COUNTRY);
            
            assertNotNull(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getZoningCodeByLabel方法 - 使用数据库数据
     */
    @Test
    @DisplayName("getZoningCodeByLabel - 使用数据库数据")
    void testGetZoningCodeByLabel_FromDatabase() {
        try (MockedStatic<RequestUtil> requestUtilMock = Mockito.mockStatic(RequestUtil.class);
             MockedStatic<CountryAreaService> countryAreaServiceMock = Mockito.mockStatic(CountryAreaService.class)) {
            
            requestUtilMock.when(RequestUtil::getCurrentLang).thenReturn(Lang.zh_CN);
            countryAreaServiceMock.when(() -> CountryAreaService.countryJsonIsParsedFromDB(testTenantId))
                    .thenReturn(true);

            AreaInfo mockAreaInfo = AreaInfo.builder()
                    .code("CN")
                    .label("中国")
                    .type(IFieldType.COUNTRY)
                    .zoningCode("156")
                    .build();

            when(mockCountryAreaService.getZoningCodeAreaInfoByLabel(anyString(), anyString(), anyString(), anyString()))
                    .thenReturn(mockAreaInfo);

            AreaInfo result = CountryAreaManager.getZoningCodeByLabel(testTenantId, "中国", IFieldType.COUNTRY);
            
            assertNotNull(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getNodeList方法 - 基本功能
     */
    @Test
    @DisplayName("getNodeList - 基本功能")
    void testGetNodeList_Basic() {
        try (MockedStatic<RequestUtil> requestUtilMock = Mockito.mockStatic(RequestUtil.class);
             MockedStatic<CountryAreaService> countryAreaServiceMock = Mockito.mockStatic(CountryAreaService.class)) {
            
            requestUtilMock.when(RequestUtil::getCurrentLang).thenReturn(Lang.zh_CN);
            countryAreaServiceMock.when(() -> CountryAreaService.countryJsonIsParsedFromDB(testTenantId))
                    .thenReturn(false);

            List<CountryAreaManager.Node> result = CountryAreaManager.getNodeList(testTenantId);
            
            assertNotNull(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getNodeList方法 - 使用数据库数据
     */
    @Test
    @DisplayName("getNodeList - 使用数据库数据")
    void testGetNodeList_FromDatabase() {
        try (MockedStatic<RequestUtil> requestUtilMock = Mockito.mockStatic(RequestUtil.class);
             MockedStatic<CountryAreaService> countryAreaServiceMock = Mockito.mockStatic(CountryAreaService.class)) {
            
            requestUtilMock.when(RequestUtil::getCurrentLang).thenReturn(Lang.zh_CN);
            countryAreaServiceMock.when(() -> CountryAreaService.countryJsonIsParsedFromDB(testTenantId))
                    .thenReturn(true);

            // Mock数据库返回的国家地区数据
            Map<String, Map> mockCountryArea = createMockCountryAreaData();
            when(mockCountryAreaService.getCountryAreaMap(anyString(), anyString()))
                    .thenReturn(mockCountryArea);

            List<CountryAreaManager.Node> result = CountryAreaManager.getNodeList(testTenantId);
            
            assertNotNull(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getCountryArea方法 - 基本功能
     */
    @Test
    @DisplayName("getCountryArea - 基本功能")
    void testGetCountryArea_Basic() {
        try (MockedStatic<RequestUtil> requestUtilMock = Mockito.mockStatic(RequestUtil.class);
             MockedStatic<CountryAreaService> countryAreaServiceMock = Mockito.mockStatic(CountryAreaService.class)) {
            
            requestUtilMock.when(RequestUtil::getCurrentLang).thenReturn(Lang.zh_CN);
            countryAreaServiceMock.when(() -> CountryAreaService.countryJsonIsParsedFromDB(testTenantId))
                    .thenReturn(false);

            Map<String, Map> result = CountryAreaManager.getCountryArea(testTenantId);
            
            assertNotNull(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getCountryArea方法 - 使用数据库数据
     */
    @Test
    @DisplayName("getCountryArea - 使用数据库数据")
    void testGetCountryArea_FromDatabase() {
        try (MockedStatic<RequestUtil> requestUtilMock = Mockito.mockStatic(RequestUtil.class);
             MockedStatic<CountryAreaService> countryAreaServiceMock = Mockito.mockStatic(CountryAreaService.class)) {
            
            requestUtilMock.when(RequestUtil::getCurrentLang).thenReturn(Lang.zh_CN);
            countryAreaServiceMock.when(() -> CountryAreaService.countryJsonIsParsedFromDB(testTenantId))
                    .thenReturn(true);

            Map<String, Map> mockCountryArea = createMockCountryAreaData();
            when(mockCountryAreaService.getCountryAreaMap(anyString(), anyString()))
                    .thenReturn(mockCountryArea);

            Map<String, Map> result = CountryAreaManager.getCountryArea(testTenantId);
            
            assertNotNull(result);
        }
    }

    /**
     * 创建模拟的国家地区数据
     */
    private Map<String, Map> createMockCountryAreaData() {
        Map<String, Map> countryArea = Maps.newHashMap();
        
        // 创建国家数据
        Map<String, Object> countryData = Maps.newHashMap();
        List<Map<String, Object>> countryOptions = Lists.newArrayList();
        
        Map<String, Object> chinaOption = Maps.newHashMap();
        chinaOption.put("value", "CN");
        chinaOption.put("label", "中国");
        chinaOption.put("standard_code", "156");
        countryOptions.add(chinaOption);
        
        countryData.put("options", countryOptions);
        countryArea.put(IFieldType.COUNTRY, countryData);
        
        return countryArea;
    }
}
